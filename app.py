"""
Enhanced <PERSON>da Handler with Intelligent Error Management
"""

import shutil
import json
import sys
import os
import threading
import string
import random
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append('/var/task')
        
from Invoices.config import DefaultConfig
from Invoices.vendors import FORD, LEXUS, JLR, PORSCHE, CADILLAC, HONDA, MANHEIM, MAZDA, GENERALMOTORS

import traceback

from error_exceptions import StoreLoginException, StoreProcessingException

# Import the external email function
try:
    from external_email_interface import send_email_notification
    EMAIL_FUNCTION_AVAILABLE = True
except ImportError:
    EMAIL_FUNCTION_AVAILABLE = False
    print("Warning: Email notification system not available")

# Import error notification system
try:
    from error_notification_system import (
        IntelligentErrorNotificationSystem, ErrorContext, <PERSON><PERSON>r<PERSON><PERSON><PERSON>, ErrorCategory
    )
    ERROR_SYSTEM_AVAILABLE = True
except ImportError:
    ERROR_SYSTEM_AVAILABLE = False
    print("Warning: Enhanced error notification system not available")

def generate_strong_password(min_length=8, max_length=15, special_characters=None):
    if min_length < 8 or max_length > 15:
        raise ValueError("Password length must be between 8 and 15 characters.")

    # Selection of at least one character per required type
    upper = random.choice(string.ascii_uppercase)
    lower = random.choice(string.ascii_lowercase)
    digit = random.choice(string.digits)
    
    if special_characters:
        special = random.choice(special_characters)
        allowed_special = special_characters
    else:
        special = random.choice(string.punctuation)
        allowed_special = string.punctuation

    # Required characters
    required_chars = [upper, lower, digit, special]

    # How many random characters are needed to complete the length
    remaining_length = random.randint(min_length, max_length) - len(required_chars)

    # Complete pool for the rest of the password
    all_chars = string.ascii_letters + string.digits + allowed_special

    # Add random characters
    required_chars += random.choices(all_chars, k=remaining_length)

    # Shuffle the result
    random.shuffle(required_chars)

    return ''.join(required_chars)

def get_parameters():
    """to be replaced by secret manager"""
    return DefaultConfig.get_parameters()

def lambda_handler(event, context):
    # Initialize enhanced error management system
    error_manager = None
    
    if ERROR_SYSTEM_AVAILABLE and EMAIL_FUNCTION_AVAILABLE:
        error_manager = IntelligentErrorNotificationSystem(send_email_notification)
        
    action = event.get("action", "invoice_download")
    store = event['store']
    stage = event.get('stage', "")
    data = event.get('data', {})
    brand = data.get("brand", "")
    vins = data.get("vins", [])

    store = store.split("_")[0]
    
    # Log the incoming request
    if error_manager:
        error_manager.logger.info(f"Processing request - Action: {action}, Store: {store}, VINs: {len(vins)}")

    print("Current directory: ", os.getcwd())
    os.chdir('/tmp')
    print("Modified directory: ", os.getcwd())

    is_reset = action == "reset_password"
    store_site = None
    
    try:
        # Initialize store classes (same as before but with enhanced error handling)
        if store == 'FOR':
            store_site = FORD(store, stage, action)
            if not is_reset:
                store_site.login(*DefaultConfig.get_user_password(store))
        elif store == 'LOA':
            store_site = LEXUS(store, stage, action)
            store_site.login(*DefaultConfig.get_user_password(store))
        elif store == 'LOG':
            store_site = LEXUS(store, stage, action)
            store_site.login(*DefaultConfig.get_user_password(store))
        elif store in ['JLRN', 'JLRB', 'JLRG']:
            store_site = JLR(store, stage, brand)
            store_site.login(*DefaultConfig.get_user_password('JLRN'))
        elif store == 'POR':
            store_site = PORSCHE(store, stage, action)
            store_site.login(*DefaultConfig.get_user_password(store))
        elif store == 'PNW':
            store_site = PORSCHE(store, stage, action)
            store_site.login(*DefaultConfig.get_user_password(store))
        elif store == 'CAD':
            store_site = CADILLAC(store, stage, action)
            store_site.login(*DefaultConfig.get_user_password(store))
        elif store == 'HON':
            store_site = HONDA(store, stage, action)
            if not is_reset:
                store_site.login(*DefaultConfig.get_user_password(store))
        elif store == 'MANHEIM':
            store_site = MANHEIM(store, stage, action)
            store_site.login(*DefaultConfig.get_user_password(store))
        elif store == 'MBG':
            if "MAZDA" in brand:
                store_site = MAZDA(store, stage, action)
                store_site.login(*DefaultConfig.get_user_password(store, brand))
            else:
                # Enhanced error handling for unsupported brand
                error_msg = f"General Motors processing not available for brand: {brand}"
                # if error_manager:
                #     context = ErrorContext(
                #         store=store,
                #         action=action,
                #         stage='INITIALIZATION',
                #         additional_data={'brand': brand, 'supported_brands': ['MAZDA']}
                #     )
                #     error_manager.capture_error(
                #         Exception(error_msg),
                #         context,
                #         ErrorSeverity.MEDIUM,  # Not critical - just unsupported
                #         ErrorCategory.VALIDATION_ERROR,
                #         app_id="invoice_downloader"
                #     )
                # raise Exception(error_msg)
                error_manager.logger.error(error_msg)

        else:
            # Enhanced error handling for unsupported store
            error_msg = f"Unsupported store: {store}"
            if error_manager:
                context = ErrorContext(
                    store=store,
                    action=action,
                    stage_action=stage,
                    stage='INITIALIZATION',
                    additional_data={'supported_stores': ['FOR', 'LOA', 'LOG', 'JLRN', 'JLRB', 'JLRG', 'POR', 'PNW', 'CAD', 'HON', 'MANHEIM', 'MBG']}
                )
                error_manager.capture_error(
                    Exception(error_msg),
                    context,
                    ErrorSeverity.MEDIUM,  # Not critical - just configuration issue
                    ErrorCategory.VALIDATION_ERROR,
                    app_id="invoice_downloader"
                )
            return {
                "statusCode": 200,
                "body": json.dumps({"message": error_msg})
            }

    except StoreLoginException as handled_error:
        # Store-level login error already handled and notified by enhanced system
        print(f"Store login failed - notification already sent with error ID: {handled_error.error_id}")
        print(f"Original error: {handled_error.original_error}")
        
        body = {}
        if len(vins) > 0:
            for i in vins:
                body[str(i)] = {
                    'error': str(handled_error.original_error),
                    'stored': False,
                    'stage': 'LOGIN'
                }
        else:
            body = {'error': str(handled_error.original_error)}
        return {
            "statusCode": 500,
            "body": json.dumps(body)
        }

    except StoreProcessingException as handled_error:
        # Store-level processing error already handled and notified by enhanced system
        print(f"Store processing failed - notification already sent with error ID: {handled_error.error_id}")
        print(f"Original error: {handled_error.original_error}")
        
        # Try to get tracker from store_site if available
        body = {}
        if store_site and hasattr(store_site, 'tracker'):
            body = store_site.tracker
        elif len(vins) > 0:
            for i in vins:
                body[str(i)] = {
                    'error': str(handled_error.original_error),
                    'stored': False,
                    'stage': getattr(store_site, 'stage', None) if store_site else None
                }
        else:
            body = {'error': str(handled_error.original_error)}
        return {
            "statusCode": 500,
            "body": json.dumps(body)
        }

    except Exception as e:
        # System-level initialization error
        print("System initialization failed")
        print(f"Error: {traceback.format_exc()}")
        
        # Enhanced system-level error notification
        if error_manager:
            context = ErrorContext(
                store=store,
                stage_action=stage,
                stage='SYSTEM_INITIALIZATION',
                action=action,
                additional_data={
                    'brand': brand,
                    'vins_count': len(vins),
                    'is_reset': is_reset,
                    'error_level': 'system'
                }
            )
            error_manager.capture_error(
                e, context, ErrorSeverity.CRITICAL, ErrorCategory.SYSTEM_ERROR, 
                app_id="invoice_downloader"
            )
        
        body = {}
        if len(vins) > 0:
            for i in vins:
                body[str(i)] = {
                    'error': str(e),
                    'stored': False,
                    'stage': None
                }
        else:
            body = {'error': str(e)}
        return {
            "statusCode": 500,
            "body": json.dumps(body)
        }

    # Handle different actions
    else:
        if action == "invoice_download":
            try:
                print("Processing VINs:", vins)
                invoices_res = store_site.download_vins(vins)
                print("Results:", invoices_res)

            except StoreProcessingException as handled_error:
                # Processing error already handled at store level with enhanced system
                print(f"Download processing failed - notification already sent with error ID: {handled_error.error_id}")
                
                tracker = store_site.tracker if store_site else {}
                for v in vins:
                    if v not in tracker:
                        tracker[v] = {
                            'error': str(handled_error.original_error), 
                            'stored': False, 
                            'stage': None
                        }
                return {
                    "statusCode": 500,
                    "body": json.dumps(tracker)
                }

            except Exception as e:
                # Unhandled system error during processing
                print("Download invoices failed due to an unhandled system exception")
                print(f"Error: {traceback.format_exc()}")

                # Enhanced system-level error notification for unhandled download errors
                if error_manager:
                    context = ErrorContext(
                        store=store,
                        stage_action=stage,
                        stage='DOWNLOAD_PROCESSING',
                        action=action,
                        additional_data={
                            'vins_count': len(vins),
                            'processed_count': getattr(store_site, 'processed_count', 0),
                            'error_level': 'system'
                        }
                    )
                    error_manager.capture_error(
                        e, context, ErrorSeverity.HIGH, ErrorCategory.SYSTEM_ERROR, 
                        app_id="invoice_downloader"
                    )

                tracker = store_site.tracker if store_site else {}
                for v in vins:
                    if v not in tracker:
                        tracker[v] = {'error': str(e), 'stored': False, 'stage': None}
                return {
                    "statusCode": 500,
                    "body": json.dumps(tracker)
                }

            response = {
                "statusCode": 200,
                "body": json.dumps(invoices_res)
            }
            
        elif action == "reset_password":
            try:
                secret_name = DefaultConfig.env + f"-user_login_{store.lower()}"

                if store in ['LOA', 'LOG', 'FOR']:
                    user, password = DefaultConfig.get_user_password(store)
                    if store == 'FOR':
                        new_password = generate_strong_password(12, 15, special_characters="$#%@!")
                    else:
                        new_password = generate_strong_password()
                    changed = store_site.reset_password_and_refresh_secret(secret_name, store, user, password, new_password)

                    if changed == True:
                        if error_manager:
                            error_manager.logger.info(f"Password reset successful for store {store}")
                            # Send success notification through the enhanced system
                            error_manager.send_password_reset_success(store, user, f"{store}_downloader")
                        return {
                            "statusCode": 200,
                            "body": json.dumps({'message': f'Password reset successfully for store {store}'})
                        }
                        
                elif store in ['HON']:
                    user, password, dealer_code, otp = DefaultConfig.get_user_password(store)
                    new_password = generate_strong_password(10, 15)
                    changed = store_site.reset_password_and_refresh_secret(secret_name, store, user, password, new_password, dealer_code, otp)

                    if changed == True:
                        if error_manager:
                            error_manager.logger.info(f"Password reset successful for Honda store {store}")
                            # Send success notification through the enhanced system
                            error_manager.send_password_reset_success(store, user, f"{store}_downloader")
                        return {
                            "statusCode": 200,
                            "body": json.dumps({'message': f'Password reset successfully for store {store}'})
                        }

                return {
                    "statusCode": 200,
                    "body": json.dumps({'message': f'Password not reset for store {store}'})
                }

            except StoreProcessingException as handled_error:
                # Password reset error already handled at store level with enhanced system
                print(f"Password reset failed - notification already sent with error ID: {handled_error.error_id}")
                return {
                    "statusCode": 500,
                    "body": json.dumps({'error': str(handled_error.original_error)})
                }

            except Exception as e:
                # Unhandled system error during password reset
                print("Password reset failed due to an unhandled system exception")
                print(f"Error: {e}")

                # Check if this is actually a re-raised handled error
                if "Ford password reset login failed" in str(e):
                    # This is likely a handled error that got re-raised somehow
                    print("This appears to be a handled error - not sending duplicate notification")
                    return {
                        "statusCode": 500,
                        "body": json.dumps({'error': str(e)})
                    }
                if "Honda password reset login failed" in str(e):
                    print("This appears to be a handled Honda error - not sending duplicate notification")
                    return {
                        "statusCode": 500,
                        "body": json.dumps({'error': str(e)})
                    }

                # Enhanced system-level error notification for unhandled password reset errors
                if error_manager:
                    context = ErrorContext(
                        store=store,
                        stage_action=stage,
                        stage='PASSWORD_RESET_SYSTEM',
                        action=action,
                        additional_data={'error_level': 'system'}
                    )
                    error_manager.capture_error(
                        e, context, ErrorSeverity.HIGH, ErrorCategory.SYSTEM_ERROR, 
                        app_id="invoice_downloader"
                    )
                
                return {
                    "statusCode": 500,
                    "body": json.dumps({'error': str(e)})
                }
            
        elif action == "download_honda_pricing_guide":
            if store == 'HON' or store == "ACU":
                try:
                    store_site.get_pricing_sheet()
                    if error_manager:
                        error_manager.logger.info("Honda pricing sheet downloaded successfully")
                    return {
                        "statusCode": 200,
                        "body": json.dumps({'message': f'Successfully downloaded price sheet'})
                    }
                
                except StoreProcessingException as handled_error:
                    # Pricing guide error already handled at store level with enhanced system
                    print(f"Pricing guide download failed - notification already sent with error ID: {handled_error.error_id}")
                    return {
                        "statusCode": 500,
                        "body": json.dumps({'error': str(handled_error.original_error)})
                    }

                except Exception as e:
                    # Unhandled system error during pricing guide download
                    print("Couldn't download the pricing sheet - system error")
                    print(f"Error: {e}")

                    if "Selenium operation" in str(e) and "failed - returned False" in str(e):
                        print("This appears to be a handled Honda error - not sending duplicate notification")
                        return {
                            "statusCode": 500,
                            "body": json.dumps({'error': str(e)})
                        }
                    
                    # Enhanced system-level error notification
                    if error_manager:
                        context = ErrorContext(
                            store=store,
                            stage_action=stage,
                            stage='PRICING_DOWNLOAD_SYSTEM',
                            action=action,
                            additional_data={'error_level': 'system'}
                        )
                        error_manager.capture_error(
                            e, context, ErrorSeverity.MEDIUM, ErrorCategory.SYSTEM_ERROR, 
                            app_id="invoice_downloader"
                        )
                    
                    return {
                        "statusCode": 500,
                        "body": json.dumps({'error': str(e)})
                    }
                
        elif action == "download_new_vehicles_report":
            if store == 'POR' or store == "PNW":
                try:
                    store_site.download_new_vehicles_report(store)
                    if error_manager:
                        error_manager.logger.info("New vehicles report downloaded successfully")
                    return {
                        "statusCode": 200,
                        "body": json.dumps({'message': f'Successfully downloaded new vehicles report'})
                    }
                
                except StoreProcessingException as handled_error:
                    # Report download error already handled at store level with enhanced system
                    print(f"New vehicles report download failed - notification already sent with error ID: {handled_error.error_id}")
                    return {
                        "statusCode": 500,
                        "body": json.dumps({'error': str(handled_error.original_error)})
                    }

                except Exception as e:
                    # Unhandled system error during report download
                    print("Couldn't download the new vehicles report - system error")
                    print(f"Error: {e}")
                    
                    # Enhanced system-level error notification
                    if error_manager:
                        context = ErrorContext(
                            store=store,
                            stage_action=stage,
                            stage='REPORT_DOWNLOAD_SYSTEM',
                            action=action,
                            additional_data={'error_level': 'system'}
                        )
                        error_manager.capture_error(
                            e, context, ErrorSeverity.MEDIUM, ErrorCategory.SYSTEM_ERROR, 
                            app_id="invoice_downloader"
                        )
                    
                    return {
                        "statusCode": 500,
                        "body": json.dumps({'error': str(e)})
                    }
        
        elif action == "manual_trigger":
            return {
                "statusCode": 200,
                "body": json.dumps({'message': f'Successfully tested'})
            }

        else:
            # Unknown action - enhanced system validation error
            error_msg = f"Unknown action: {action}"
            if error_manager:
                context = ErrorContext(
                    store=store,
                    action=action,
                    stage_action=stage,
                    stage='ACTION_VALIDATION',
                    additional_data={
                        'supported_actions': ['invoice_download', 'reset_password', 'download_honda_pricing_guide', 'download_new_vehicles_report'],
                        'error_level': 'system'
                    }
                )
                error_manager.capture_error(
                    Exception(error_msg),
                    context,
                    ErrorSeverity.LOW,  # Low severity for configuration issues
                    ErrorCategory.VALIDATION_ERROR,
                    app_id="invoice_downloader"
                )
            return {"statusCode": 400, "body": json.dumps({'error': error_msg})}

    # Enhanced cleanup and session summary
    if error_manager:
        try:
            # Send session summary only if there were significant errors or issues
            if hasattr(error_manager, 'session_errors') and error_manager.session_errors:
                # Check if we have any significant errors that warrant a summary
                significant_errors = [
                    e for e in error_manager.session_errors 
                    if e.severity in [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL]
                ]
                
                # Calculate session statistics
                session_stats = {
                    'store': store,
                    'action': action,
                    'total_vins': len(vins),
                    'successful_downloads': sum(1 for v in (store_site.tracker if store_site and hasattr(store_site, 'tracker') else {}).values() if v.get('stored', False)),
                    'failed_downloads': sum(1 for v in (store_site.tracker if store_site and hasattr(store_site, 'tracker') else {}).values() if not v.get('stored', False)),
                    'session_type': 'system_level',
                    'total_errors': len(error_manager.session_errors),
                    'critical_errors': len([e for e in error_manager.session_errors if e.severity == ErrorSeverity.CRITICAL]),
                    'high_errors': len([e for e in error_manager.session_errors if e.severity == ErrorSeverity.HIGH])
                }
                
                # Only send summary if we have significant errors or many total errors
                if significant_errors or len(error_manager.session_errors) >= 5:
                    error_manager.send_session_summary(session_stats, app_id="invoice_downloader")
                else:
                    error_manager.logger.info(f"Session summary skipped - {len(error_manager.session_errors)} minor errors only")
            
            # Close the error manager
            error_manager.close()
            
        except Exception as cleanup_error:
            print(f"Error during enhanced cleanup: {cleanup_error}")

    # Close store site if it was created
    if store_site and hasattr(store_site, 'close'):
        try:
            store_site.close()
        except Exception as e:
            print(f"Error closing store site: {e}")

    return response

# Testing functions remain the same but with enhanced error handling
def Honda_Pwd_reset():
    for _ in range(1):
        lambda_handler({
            "stage": "title",
            "action": "download_honda_pricing_guide",
            # "action": "manual_trigger",
            # "action": "reset_password",
            # "action": "invoice_download",
            "store": "HON",
            "data":{
                "brand": "HONDA",
                "vins": ["7FARW1H86NE019056"]
            }
            }, None)

def Lexus_Pwd_reset():
    for _ in range(1):
        lambda_handler({
            "stage": "pre-inventory",
            # "action": "download_new_vehicles_report",
            # "action": "reset_password",
            "action": "manual_trigger",
            "store": "LOG"
            }, None)

def Ford_Pwd_reset():
    for _ in range(1):
        lambda_handler({
            "stage": "pre-inventory",
            # "action": "download_new_vehicles_report",
            "action": "manual_trigger",
            # "action": "reset_password",
            # "action": "invoice_download",
            "store": "FOR"
            }, None)

def call_loa():
    for _ in range(1):
        lambda_handler({
  "stage": "used-cars",
  "data": {
    # "vins": [
    #   "SAL1L9FU0SA605166",
    #   "SAL1L9FU3SA603962"
    # ],
    "vins": ["5LMPJ8K46SJ964618"],
    "brand": "HONDA"
  },
   "action": "invoice_download",
# "action": "manual_trigger",
# "action": "reset_password",
  "store": "FOR"
#   "store": "HON"
#   "store": "MBG"
# "store": "LOA"
# "store": "JLRN"
# "store": "POR"
# "store": "CAD"
# "store": "MANHEIM"
  
}, None)

if __name__ == "__main__":
    # t1 = threading.Thread(target=Honda_Pwd_reset)
    # t1 = threading.Thread(target=Lexus_Pwd_reset)
    # t1 = threading.Thread(target=Ford_Pwd_reset)
    
    t1 = threading.Thread(target=call_loa)

    # Start threads
    t1.start()
    #t2.start()

    # Wait for both to finish
    t1.join()
    #t2.join()