import boto3
from botocore.exceptions    import ClientError
import os
class Boto3Utils():
    def __init__(self):
        self.s3 = boto3.client('s3')
        self.sm = boto3.client('secretsmanager')

    def upload_file(self, file_path, bucket, key):
        try:
            self.s3.upload_file(file_path, bucket, key)
        except ClientError as e:
            raise e
        
    def get_secret(self, secret_name):
        try:
            # Attempt to get the specified secret
            response = self.sm.get_secret_value(SecretId=secret_name)
            return response['SecretString']

        except ClientError as e:
            print(f"Error retrieving secret '{secret_name}': {e}")
            raise e
        
    def update_secret(self, name, value):
        try:
            response = self.sm.update_secret(
                SecretId=name,
                SecretString=value
            )
            if response.get('ResponseMetadata',''):
                return True if response['ResponseMetadata'].get('HTTPStatusCode') == 200 else False
            else:
                return False            
        except Exception as e:
            print(f'Error: {e}')
            return False