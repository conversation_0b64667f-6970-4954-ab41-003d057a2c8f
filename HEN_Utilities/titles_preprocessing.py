def generate_input_dict_titles(titles_from_mongo):
    titles_to_be_processed = []
    for idx, title_vals in enumerate(titles_from_mongo):
        
        title = {
            "aria_wi_id": title_vals["aria_wi_id"],
            "title_date": title_vals["read_at"], 
            "is_mso": title_vals["fields"]["is_mso"]["value"],
            "vin": title_vals["fields"]["vin"]["value"],
            "make": title_vals["fields"]["make"].get("value", ""),
            "store": title_vals["fields"]["store"]["value"],
            "reynolds_model": title_vals["fields"].get("reynolds_model", {}).get("value", None),
        }

        titles_to_be_processed.append(title)

    return titles_to_be_processed
