import os
import json
import requests
from typing import Dict, Any, Optional
from datetime import datetime
import base64

class APIClient:
    def __init__(self, api_endpoint: str, api_token: str):
        if not api_endpoint:
            raise ValueError("API_ENDPOINT must be provided.")
        if not api_token:
            raise ValueError("API_TOKEN must be provided.")
        
        self.api_endpoint = api_endpoint
        self.api_token = api_token

    def get_headers(self) -> Dict[str, str]:
        """Returns headers for API requests."""
        return {
            "Authorization": f"Bearer {self.api_token}",
            "Content-Type": "application/json"
        }

    def make_request(self, action: str, data: Dict[str, Any] = None, file_b64: Dict[str, Any] = None, secret_data: Dict[str, Any] = None, database: Optional[str] = None, collection: Optional[str] = None, filter_data: Optional[Dict[str, Any]] = None, **kwargs) -> Optional[Dict[str, Any]]:
        """
        Generic function to make API requests.
        
        :param action: The action to perform (e.g., 'get_one', 'update_one', 'get_many').
        :param data: The general data payload (for queue operations).
        :param secret_data: The secret-specific data payload.
        :param database: Database name.
        :param collection: Collection name.
        :param filter_data: Optional filter criteria for database queries.
        :return: JSON response or None if an error occurs.
        """
        if secret_data:
            endpoint = f"{self.api_endpoint}/secrets_handler"
        elif data:
            endpoint = f"{self.api_endpoint}/queues_handler"
        elif file_b64:
            endpoint = f"{self.api_endpoint}/ocr_handler"
        else:
            endpoint = self.api_endpoint

        payload = {"action": action}
        
        if data:
            payload["data"] = data
        if file_b64:
            payload["file_b64"] = file_b64
        if secret_data:
            payload["secret_data"] = secret_data
        if database:
            payload["database_name"] = database
        if collection:
            payload["collection_name"] = collection
        if filter_data:
            payload["filter"] = filter_data
        limit = kwargs.get("limit")
        ascending = kwargs.get("ascending")
        sort_field = kwargs.get("sort_field")
        if limit:
            payload["limit"] = limit
        if ascending:
            payload['ascending'] = ascending
        if sort_field:
            payload["sort_field"] = sort_field

        try:
            response = requests.post(endpoint, json=payload, headers=self.get_headers())
            response.raise_for_status()  # Raise an error for non-2xx responses
            print(f"✅ Success: {action}")
            try:
                response = response.json()
            except:
                try:
                    response = json.loads(response.text.replace("\'", "\""))
                except:
                    response = response.text
            return response
        
        except requests.exceptions.RequestException as e:
            print(f"⚠️ Request failed: {e}")
            return None
    
    def get_secret(self, secret_name: str) -> Optional[Dict[str, Any]]:
        return self.make_request("get", secret_data={secret_name: ""})
    
    def get_text(self, image_path, get_lines = False) -> Optional[Dict[str, Any]]:
        with open(image_path, 'rb') as image_file:
            encoded_string = base64.b64encode(image_file.read()).decode('utf-8')
        return self.make_request("", file_b64=encoded_string, filter_data={"get_lines": get_lines})

    def update_secret(self, secret_name: str, user: str, old_pass: str, actual_pass: str) -> Optional[Dict[str, Any]]:
        return self.make_request("update", secret_data={secret_name: {"user": user, "old_password": old_pass, "actual_password": actual_pass}})
    
    def get_cars_to_stock_in_post_inventory(self, db_name, limit) -> Optional[Dict[str, Any]]:
        return self.make_request("get_many", data={"$or": [{"flows.post-inventory.status": 7}, {"flows.post-inventory.status": 9}]}, database=db_name, collection="vin", limit=limit, sort_field="flows.post-inventory.updated_at")

    def get_cars_to_stock_in_used_cars(self, db_name, limit) -> Optional[Dict[str, Any]]:
        return self.make_request("get_many", data={"$or": [{"flows.used-cars.status": 7}, {"flows.used-cars.status": 9}]}, database=db_name, collection="vin", limit=limit, sort_field="flows.used-cars.updated_at")

    def get_titles_to_stock_in_post_inventory(self, db_name, limit) -> Optional[Dict[str, Any]]:
        return self.make_request("get_many", data={"$or": [{"status": "Ready for DMS"}, {"status": "Partially completed"}]}, database=db_name, collection="title", limit=limit)
    
    def get_title_by_aria_wi_id(self, db_name, aria_wi_id) -> Optional[Dict[str, Any]]:
        return self.make_request("get_one", data={"aria_wi_id": aria_wi_id}, database=db_name, collection="title")[0]

    def get_cars_to_stock_in_pre_inventory(self, db_name, limit) -> Optional[Dict[str, Any]]:
        return self.make_request("get_many", data={"$or": [{"flows.pre-inventory.status": 7}, {"flows.pre-inventory.status": 9}]}, database=db_name, collection="vin", limit=limit, sort_field="flows.pre-inventory.updated_at")

    def update_vin_status_in_mongo(self, vin: str, stage: str, status: int, db_name, finished: bool = False) -> Optional[Dict[str, Any]]:
        return self.make_request(
            "update_one", 
            data={
                "$set": {
                        f"flows.{stage}.status": status,
                        f"flows.{stage}.docs.invoice.aria_data.status": "Completed" if finished else "Needs Attention",
                    }, 
                "$push": {
                    f"flows.{stage}.status_history": status,
                    f"flows.{stage}.docs.invoice.aria_data.status_history": "Completed" if finished else "Needs Attention"
                }}, 
            database=db_name, 
            collection="vin", 
            filter_data={"vin": vin}
        )
    
    def update_title_in_mongo(self, db_name, aria_wi_id, data_to_set, data_to_push) -> Optional[Dict[str, Any]]:
        return self.make_request("update_one", data={"$set": data_to_set, "$push": data_to_push}, database=db_name, collection="title", filter_data={"aria_wi_id": aria_wi_id})

    def update_vin_in_mongo(self, db_name, vin, data_to_set, data_to_push) -> Optional[Dict[str, Any]]:
        return self.make_request("update_one", data={"$set": data_to_set, "$push": data_to_push}, database=db_name, collection="vin", filter_data={"vin": vin})
    
    def update_invoice_vin_status(self, vin: str, db_name, stage) -> Optional[Dict[str, Any]]:
        return self.make_request("update_one", data={
            "$set": {
                f"flows.{stage}.docs.invoice.aria_data.status": "Completed",
                f"flows.{stage}.docs.invoice.updated_at": datetime.now().isoformat()
            },
            "$push": {
                f"flows.{stage}.docs.invoice.aria_data.status_history": "Completed"
            }
        }, database=db_name, collection="vin", filter_data={"vin": vin})
    
    def get_completed_status_uuid(self, invoice_app_id, db_name) -> Optional[str]:
        response = self.make_request("get_one", data={"app_id": invoice_app_id}, database=db_name, collection="aria_status")
        
        if response:
            status_info = response[0].get("status", {})
            for k, v in status_info.items():
                if v.get("label") == "Completed":
                    return k
        return None
    
    def get_needs_status_uuid(self, invoice_app_id, db_name) -> Optional[str]:
        response = self.make_request("get_one", data={"app_id": invoice_app_id}, database=db_name, collection="aria_status")
        
        if response:
            status_info = response[0].get("status", {})
            for k, v in status_info.items():
                if v.get("label") == "Needs Attention":
                    return k
        return None
    
    def get_not_in_stock_status_uuid(self, invoice_app_id, db_name) -> Optional[str]:
        response = self.make_request("get_one", data={"app_id": invoice_app_id}, database=db_name, collection="aria_status")
        
        if response:
            status_info = response[0].get("status", {})
            for k, v in status_info.items():
                if v.get("label") == "Not in Stock":
                    return k
        return None

    def get_partially_completed_status_uuid(self, invoice_app_id, db_name) -> Optional[str]:
        response = self.make_request("get_one", data={"app_id": invoice_app_id}, database=db_name, collection="aria_status")
        
        if response:
            status_info = response[0].get("status", {})
            for k, v in status_info.items():
                if v.get("label") == "Partially completed":
                    return k
        return None