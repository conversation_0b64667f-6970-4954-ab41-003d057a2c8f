import sys
import argparse
from handlers.lambda_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from handlers.stepfunctions_handler import <PERSON><PERSON><PERSON>ctionsHandler
from handlers.iam_handler import IAMHandler
from handlers.layer_handler import <PERSON>daLayer<PERSON>andler
from handlers.api_handler import APIGatewayHandler
from handlers.api_rest_handler import APIGatewayRestHandler
from config.layers_config import get_layers_config
from config.lambdas_config import get_lambda_config
from config.roles_config import get_iam_config
from config.stepfunctions_config import get_stepfunctions_config
from config.api_config import get_api_config

LAYERS_TO_DEPLOY = []
LAMBDAS_TO_DEPLOY = []
STEPFUNCTIONS_TO_DEPLOY = []

if __name__ == "__main__":

    parser = argparse.ArgumentParser(description="Deploy Lambda functions.")
    
    # Positional arguments
    parser.add_argument("env", help="The environment (e.g., snd-hen, prd-hen)")
    parser.add_argument("profile", help="The AWS profile to use")

    # Optional argument
    parser.add_argument("--function", help="Deploy only a specific function by name", default=None)
    parser.add_argument("--stepfunction", help="Deploy only a specific stepfunction by name", default=None)

    args = parser.parse_args()

    ENV = args.env
    PROFILE_TO_DEPLOY = args.profile
    TARGET_FUNCTION = args.function
    TARGET_STEPFUNCTION = args.stepfunction


    LAYERS_ARN = {}
    LAYERS = get_layers_config()
    ROLES_ARN = get_iam_config()
    LAMBDAS = get_lambda_config(ENV)
    STEPFUNCTIONS = get_stepfunctions_config(ENV)
    API = get_api_config(ENV)

    if ((not TARGET_FUNCTION) and (not TARGET_STEPFUNCTION)):
        LAMBDAS_TO_DEPLOY = list(LAMBDAS.keys())
        STEPFUNCTIONS_TO_DEPLOY = list(STEPFUNCTIONS.keys())
    else:
        if TARGET_FUNCTION:
            LAMBDAS_TO_DEPLOY = TARGET_FUNCTION.split(",")
        if TARGET_STEPFUNCTION:
            STEPFUNCTIONS_TO_DEPLOY = TARGET_STEPFUNCTION.split(",")

    layer_handler = LambdaLayerHandler(PROFILE_TO_DEPLOY)
    lambda_handler = LambdaHandler(PROFILE_TO_DEPLOY)
    stepfunctions_handler = StepFunctionsHandler(PROFILE_TO_DEPLOY)
    iam_handler = IAMHandler(PROFILE_TO_DEPLOY)
    api_handler = APIGatewayHandler(PROFILE_TO_DEPLOY)
    api_rest_handler = APIGatewayRestHandler(PROFILE_TO_DEPLOY)

    print("🚀 Starting deployment...")

    print("Deploying to environment:", ENV)
    print("Using profile:", PROFILE_TO_DEPLOY)

    print("🔍 Getting layers ARN...")
    for layer in LAYERS:
        layer_name = layer['layer_name']
        description = layer['description']
        zip_file_path = layer['zip_file_path']
        runtimes = layer['runtimes']
        
        # Create or update the Lambda layer
        if layer_name in LAYERS_TO_DEPLOY:
            print(f"\t🔄 Creating or updating layer: {layer_name}")
            LAYERS_ARN[layer_name] = layer_handler.create_or_update_lambda_layer(layer_name, description, zip_file_path, runtimes)
        else:
            print(f"\t🔗 Fetching existing ARN for layer: {layer_name}")
            LAYERS_ARN[layer_name] = layer_handler.get_layer_arn(layer_name)
    
    print("\n\n")
    print(f"✅ Layers processed")
    print("\n\n")

    print("🔒 Creating/Updating roles...")
    for key, value in ROLES_ARN.items():
        role_name = key
        yaml_path = value
        
        print(f"\t🔧 Processing role: {role_name} with config at {yaml_path}")
        # Load policies from YAML and create or update the IAM role
        iam_handler.create_update_policies(ENV, yaml_path)
    
    print("\n\n")
    print(f"✅ Roles processed")
    print("\n\n")

    print("🐑 Creating/Updating lambdas...")
    for name, lambda_info in LAMBDAS.items():
        function_name = lambda_info['function_name']
        description = lambda_info['description']
        zip_file_path = lambda_info['zip_file_path']
        handler = lambda_info['handler']
        role_name = f"{ENV}-{lambda_info['role_name']}"
        runtime = lambda_info['runtime']
        security_group_ids = lambda_info['security_group_ids']
        subnet_ids = lambda_info['subnet_ids']
        environment_variables = lambda_info['environment_variables'][ENV]
        timeout = lambda_info['timeout']
        memory_size = lambda_info['memory_size']
        layers = [LAYERS_ARN[layer] for layer in lambda_info['layers']]
        ephemeral_storage = lambda_info.get("ephemeral_storage", 512)

        # Create or update the Lambda function
        if name in LAMBDAS_TO_DEPLOY:
            print(f"\t🔄 Creating or updating lambda: {function_name}")
            lambda_handler.create_or_update_lambda_function(
                function_name, description, zip_file_path, handler, role_name, runtime,
                security_group_ids, subnet_ids, environment_variables, timeout, memory_size, layers, ephemeral_storage_mb=ephemeral_storage
            )

    print("\n\n")
    print("✅ Lambdas process completed.")
    print("\n\n")

    print("🔄 Creating/Updating step functions...")
    for name, stepfunction in STEPFUNCTIONS.items():
        step_name = stepfunction['name']
        definition_json_path = stepfunction['definition_json_path']
        role_name = f"{ENV}-{stepfunction['role_name']}"        
        # Create or update the Step Function
        if name in STEPFUNCTIONS_TO_DEPLOY:
            print(f"\t🔄 Creating or updating step function: {name}")
            stepfunctions_handler.create_or_update_step_function(
                ENV, step_name, definition_json_path, role_name
           )

    print("\n\n")
    print("✅ Stepfunctions process completed.")
    print("\n\n")


    print("🌐 Creating/Updating endpoints...")
    authorizers_ids = {}
    for endpoint_info in API:
        lambda_to_execute = endpoint_info["lambda_to_execute"]
        authorizer = endpoint_info["authorizer"]
        action = endpoint_info["action"]
        route = endpoint_info["endpoint"]
        api_name = endpoint_info["api_name"]
        
        print(f"\t🔗 Processing API endpoint: {route} with action {action} for API {api_name}")
        authorizers_ids[authorizer] = api_handler.create_or_get_authorizer(api_name, authorizer)

        api_handler.create_or_update_http_api(api_name, lambda_to_execute, authorizers_ids[authorizer], route, action)

    print("\n\n\n\n\n\n")
    print("✅ Deployment process completed.")
    print("\n\n")