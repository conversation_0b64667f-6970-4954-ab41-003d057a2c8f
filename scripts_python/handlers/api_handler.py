import boto3
import time

class APIGatewayHandler:

    def __init__(self, profile):
        self.boto3 = boto3.Session(profile_name=profile, region_name='us-east-1')

    def get_account_id(self):
        # Initialize the STS client
        sts_client = self.boto3.client('sts')
        identity = sts_client.get_caller_identity()
        return identity['Account']
        

    def create_or_get_authorizer(self, api_name, authorizer_name):
        """
        Create or return a Lambda (REQUEST) Authorizer for an API Gateway HTTP API.
        """
        # Check for existing, return it if found
        apig_client = self.boto3.client('apigatewayv2')
        api_id = self.get_api_id(apig_client, api_name)

        existing = apig_client.get_authorizers(ApiId=api_id).get('Items', [])
        for a in existing:
            if a['Name'] == authorizer_name:
                #print(f"Found existing authorizer: {a['AuthorizerId']}")
                return a['AuthorizerId']
            
        lambda_function_arn = f"arn:aws:lambda:us-east-1:{self.get_account_id()}:function:{authorizer_name}"

        # CORRECT AuthorizerUri for Lambda authorizer
        authorizer_uri = (
            f"arn:aws:apigateway:us-east-1:lambda:path/2015-03-31/functions/{lambda_function_arn}/invocations"
        )

        #print(f"Creating new authorizer with URI: {authorizer_uri}")

        resp = apig_client.create_authorizer(
            ApiId=api_id,
            AuthorizerType='REQUEST',  # Always REQUEST for HTTP Lambda authorizer
            Name=authorizer_name,
            AuthorizerUri=authorizer_uri,  # Fixed: Use the correct URI
            IdentitySource=["$request.header.Authorization"],  # Header to extract
            AuthorizerPayloadFormatVersion='2.0',  # <-- Enables 'Simple' mode
            EnableSimpleResponses=True,            # <-- Explicitly "Simple" response mode
            AuthorizerResultTtlInSeconds=300
        )

        authorizer_id = resp['AuthorizerId']
        #print(f"Created authorizer: {authorizer_id}")
        
        # Wait a moment for authorizer to be ready
        time.sleep(2)
        
        return authorizer_id
    
    def get_api_id(self, apig_client, api_name):
        # Check if API exists or create new
        try:
            response = apig_client.get_apis()
            for api in response['Items']:
                if api['Name'] == api_name:
                    api_id = api['ApiId']
                    #print(f"Found existing API: {api_name} ({api_id})")
                    return api_id
            
            # API doesn't exist, create new one
            #print(f"Creating new API: {api_name}")
            response = apig_client.create_api(
                Name=api_name,
                ProtocolType='HTTP'
            )
            api_id = response['ApiId']
            #print(f"Created API: {api_name} ({api_id})")
            return api_id
            
        except Exception as e:
            print(f"Error with API: {e}")
            return None

    def create_or_update_http_api(self, api_name, lambda_name, authorizer_id, api_path, request_type):
        apig_client = self.boto3.client('apigatewayv2')

        lambda_arn = f"arn:aws:lambda:us-east-1:{self.get_account_id()}:function:{lambda_name}"
        api_id = self.get_api_id(apig_client, api_name)
        
        if not api_id:
            print("Failed to get or create API")
            return

        #print(f"Using API ID: {api_id}")
        #print(f"Creating route with authorizer_id: {authorizer_id}")

        # Add Lambda permissions for both authorizer and integration
        authorizer_lambda_name = self.get_authorizer_lambda_name(apig_client, api_id, authorizer_id)
        if authorizer_lambda_name:
            self.add_lambda_permission_for_authorizer(authorizer_lambda_name, api_id)
        
        self.add_lambda_permission_for_integration(lambda_name, api_id)

        # Check for existing integrations
        integrations = apig_client.get_integrations(ApiId=api_id)['Items']
        integration_id = None
        integration_uri = f"arn:aws:apigateway:us-east-1:lambda:path/2015-03-31/functions/{lambda_arn}/invocations"

        for integration in integrations:
            if integration['IntegrationUri'] == integration_uri:
                integration_id = integration['IntegrationId']
                #print(f"Found existing integration: {integration_id}")
                break

        if not integration_id:
            # Create integration
            #print(f"Creating new integration for Lambda: {lambda_name}")
            integration_response = apig_client.create_integration(
                ApiId=api_id,
                IntegrationType='AWS_PROXY',
                IntegrationUri=integration_uri,
                PayloadFormatVersion='2.0',
                IntegrationMethod='POST'
            )
            integration_id = integration_response['IntegrationId']
            #print(f"Created integration: {integration_id}")

        # Create or update a route
        routes = apig_client.get_routes(ApiId=api_id)['Items']
        route_key = f'{request_type} /{api_path}'
        route_exists = False

        for route in routes:
            if route['RouteKey'] == route_key:
                route_exists = True
                #print(f"Route already exists: {route_key}")
                break
        
        if not route_exists:
            #print(f"Creating new route: {route_key}")
            try:
                route_response = apig_client.create_route(
                    ApiId=api_id,
                    RouteKey=route_key,
                    AuthorizationType='CUSTOM',
                    AuthorizerId=authorizer_id,
                    Target=f'integrations/{integration_id}'
                )
                #print(f"Created route: {route_response['RouteId']}")
            except Exception as e:
                print(f"Error creating route: {e}")
                return

        # Create or update the $default stage with auto-deploy
        try:
            #print("Creating $default stage with auto-deploy...")
            apig_client.create_stage(
                ApiId=api_id,
                StageName='$default',
                AutoDeploy=True
            )
            #print("Created $default stage with auto-deploy")
        except apig_client.exceptions.ConflictException:
            # Stage exists, update it to ensure auto-deploy is enabled
            #print("$default stage exists, updating to enable auto-deploy...")
            try:
                apig_client.update_stage(
                    ApiId=api_id,
                    StageName='$default',
                    AutoDeploy=True
                )
                #print("Updated $default stage with auto-deploy")
            except Exception as e:
                print(f"Error updating stage: {e}")

        # Print the endpoint URL (default stage doesn't need stage name in URL)
        endpoint_url = f"https://{api_id}.execute-api.us-east-1.amazonaws.com/{api_path}"
        #print(f"API Endpoint: {endpoint_url}")
        
        return {
            'api_id': api_id,
            'endpoint_url': endpoint_url,
            'authorizer_id': authorizer_id
        }

    def get_authorizer_lambda_name(self, apig_client, api_id, authorizer_id):
        """
        Get the Lambda function name from an authorizer
        """
        try:
            authorizer = apig_client.get_authorizer(ApiId=api_id, AuthorizerId=authorizer_id)
            authorizer_uri = authorizer['AuthorizerUri']
            # Extract function name from URI like: arn:aws:apigateway:us-east-1:lambda:path/2015-03-31/functions/arn:aws:lambda:us-east-1:123456789012:function:my-function/invocations
            lambda_arn = authorizer_uri.split('/functions/')[1].split('/invocations')[0]
            function_name = lambda_arn.split(':')[-1]
            return function_name
        except Exception as e:
            print(f"Error getting authorizer Lambda name: {e}")
            return None

    def add_lambda_permission_for_authorizer(self, lambda_function_name, api_id):
        """
        Add permission for API Gateway to invoke the authorizer Lambda function
        """
        lambda_client = self.boto3.client('lambda')
        
        try:
            lambda_client.add_permission(
                FunctionName=lambda_function_name,
                StatementId=f'apigateway-authorizer-{api_id}',
                Action='lambda:InvokeFunction',
                Principal='apigateway.amazonaws.com',
                SourceArn=f'arn:aws:execute-api:us-east-1:{self.get_account_id()}:{api_id}/authorizers/*'
            )
            #print(f"Added authorizer permission for Lambda: {lambda_function_name}")
        except lambda_client.exceptions.ResourceConflictException:
            #print(f"Permission already exists for Lambda: {lambda_function_name}")
            pass
        except Exception as e:
            print(f"Error adding Lambda permission: {e}")
        """
        Add permission for API Gateway to invoke the authorizer Lambda function
        """
        lambda_client = self.boto3.client('lambda')
        
        try:
            lambda_client.add_permission(
                FunctionName=lambda_function_name,
                StatementId=f'apigateway-authorizer-{api_id}',
                Action='lambda:InvokeFunction',
                Principal='apigateway.amazonaws.com',
                SourceArn=f'arn:aws:execute-api:us-east-1:{self.get_account_id()}:{api_id}/authorizers/*'
            )
            print(f"Added authorizer permission for Lambda: {lambda_function_name}")
        except lambda_client.exceptions.ResourceConflictException:
            #print(f"Permission already exists for Lambda: {lambda_function_name}")
            pass
        except Exception as e:
            print(f"Error adding Lambda permission: {e}")

    def add_lambda_permission_for_integration(self, lambda_function_name, api_id):
        """
        Add permission for API Gateway to invoke the integration Lambda function
        """
        lambda_client = self.boto3.client('lambda')
        
        try:
            lambda_client.add_permission(
                FunctionName=lambda_function_name,
                StatementId=f'apigateway-integration-{api_id}',
                Action='lambda:InvokeFunction',
                Principal='apigateway.amazonaws.com',
                SourceArn=f'arn:aws:execute-api:us-east-1:{self.get_account_id()}:{api_id}/*/*'
            )
            #print(f"Added integration permission for Lambda: {lambda_function_name}")
        except lambda_client.exceptions.ResourceConflictException:
            #print(f"Permission already exists for Lambda: {lambda_function_name}")
            pass
        except Exception as e:
            print(f"Error adding Lambda permission: {e}")

# Example usage:
if __name__ == "__main__":
    # Initialize handler
    handler = APIGatewayHandler('your-aws-profile')
    
    # Example: Create authorizer and API
    api_name = "snd-hen-hennesy"
    authorizer_name = "snd-hen-lambda_authorizer"
    lambda_name = "your-backend-lambda"
    
    # Create or get authorizer
    authorizer_id = handler.create_or_get_authorizer(api_name, authorizer_name)
    
    # Add Lambda permissions
    handler.add_lambda_permission_for_authorizer(authorizer_name, handler.get_api_id(handler.boto3.client('apigatewayv2'), api_name))
    handler.add_lambda_permission_for_integration(lambda_name, handler.get_api_id(handler.boto3.client('apigatewayv2'), api_name))
    
    # Create the API route
    result = handler.create_or_update_http_api(
        api_name=api_name,
        lambda_name=lambda_name,
        authorizer_id=authorizer_id,
        api_path="your-endpoint",
        request_type="GET"
    )
    
    #print(f"Setup complete: {result}")