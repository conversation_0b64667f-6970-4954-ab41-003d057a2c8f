import boto3

class LambdaLayerHandler():

    def __init__(self, profile):
        self.boto3 = boto3.Session(profile_name=profile, region_name='us-east-1')

    def get_latest_layer_version(self, lambda_client, layer_name):
        try:
            response = lambda_client.list_layer_versions(LayerName=layer_name)
            versions = response.get('LayerVersions')
            if versions:
                return max(versions, key=lambda x: x['Version'])['Version']
            return None
        except lambda_client.exceptions.ResourceNotFoundException:
            return None
        
    def get_layer_arn(self, layer_name, region='us-east-1'):
        lambda_client = self.boto3.client('lambda')

        try:
            response = lambda_client.list_layer_versions(LayerName=layer_name)
            versions = response.get('LayerVersions')
            if versions:
                # Get latest version ARN
                return versions[0]['LayerVersionArn']
            else:
                print(f"No versions found for layer '{layer_name}'")
                return None
        except lambda_client.exceptions.ResourceNotFoundException:
            print(f"Layer '{layer_name}' not found.")
            return None

    def create_or_update_lambda_layer(self, layer_name, description, zip_file_path, runtimes):
        # Initialize the Lambda client
        lambda_client = self.boto3.client('lambda')

        # Open the layer ZIP file
        with open(zip_file_path, 'rb') as f:
            layer_data = f.read()

        # Check if the layer exists and get the latest version
        latest_version = self.get_latest_layer_version(lambda_client, layer_name)

        # Create or update the Lambda layer
        response = lambda_client.publish_layer_version(
            LayerName=layer_name,
            Description=description,
            Content={
                'ZipFile': layer_data
            },
            CompatibleRuntimes=runtimes,
            LicenseInfo='MIT'
        )

        # Print the ARN of the created or updated layer
        return response['LayerVersionArn']

