    
def get_layers_config():
    return [
        {
            'layer_name': 'msal_2',
            'description': 'Msal layer',
            'zip_file_path': 'layers/msal.zip',
            'runtimes': ['python3.11', 'python3.12']
        },
        {
            'layer_name': 'open_ai_2',
            'description': 'OpenAi layer',
            'zip_file_path': 'layers/open_ai.zip',
            'runtimes': ['python3.10', 'python3.11']
        },
        {
            'layer_name': 'openpyxl_2',
            'description': 'openpyxl layer',
            'zip_file_path': 'layers/openpyxl.zip',
            'runtimes': ['python3.11']
        },
        {
            'layer_name': 'paramiko_2',
            'description': 'paramiko layer',
            'zip_file_path': 'layers/paramiko.zip',
            'runtimes': ['python3.11']
        },
        {
            'layer_name': 'pymongoAWS_2',
            'description': 'pymongoAWS layer',
            'zip_file_path': 'layers/pymongoAWS.zip',
            'runtimes': ['python3.10', 'python3.11']
        },
        {
            'layer_name': 'PyPDF2_2',
            'description': 'PyPDF2 layer',
            'zip_file_path': 'layers/PyPDF2.zip',
            'runtimes': ['python3.11']
        },
        {
            'layer_name': 'requests_2',
            'description': 'requests layer',
            'zip_file_path': 'layers/requests.zip',
            'runtimes': ['python3.10', 'python3.11']
        },
        {
            'layer_name': 'vininfo_2',
            'description': 'vininfo layer',
            'zip_file_path': 'layers/vininfo.zip',
            'runtimes': ['python3.11']
        },
        {
            'layer_name': 'pandas_2',
            'description': 'pandas layer',
            'zip_file_path': 'layers/pandas.zip',
            'runtimes': ['python3.11']
        },
        {
            'layer_name': 'pydantic_2',
            'description': 'pydantic layer',
            'zip_file_path': 'layers/pydantic.zip',
            'runtimes': ['python3.11', 'python3.12']
        },
        {
            'layer_name': 'pymupdf_2',
            'description': 'pymupdf layer',
            'zip_file_path': 'layers/pymupdf.zip',
            'runtimes': ['python3.11', 'python3.12']
        },
        {
            'layer_name': 'reportlab_2',
            'description': 'reportlab layer',
            'zip_file_path': 'layers/reportlab.zip',
            'runtimes': ['python3.11', 'python3.12']
        },
        {
            'layer_name': 'jinja2_2',
            'description': 'jinja2 layer',
            'zip_file_path': 'layers/jinja2.zip',
            'runtimes': ['python3.11', 'python3.12']
        },
        {
            'layer_name': 'lxml_2',
            'description': 'lxml layer',
            'zip_file_path': 'layers/lxml.zip',
            'runtimes': ['python3.11', 'python3.12']
        }
    ]