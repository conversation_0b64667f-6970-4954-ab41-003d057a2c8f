image: docker:latest

services:
  - docker:dind  # Enable Docker-in-Docker

variables:
  IMAGE_NAME: pyautomationaws/selenium
  AWS_REGION: us-east-1
  ECR_REPO: $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$IMAGE_NAME
  LAMBDA_NAME: snd-hen-selenium-downloader

stages:
  - build
  - deploy

before_script:
  - apk add --no-cache python3 py3-pip jq aws-cli
  - echo "Configuring AWS credentials..."
  - |
    if [ "$CI_COMMIT_BRANCH" == "main" ]; then
      export AWS_ROLE_ARN="$AWS_ROLE_ARN_PROD"
      export AWS_ACCOUNT_ID="$AWS_ACCOUNT_ID_PROD"
      echo "Using production AWS credentials"
    else
      export AWS_ROLE_ARN="$AWS_ROLE_ARN_DEV"
      export AWS_ACCOUNT_ID="$AWS_ACCOUNT_ID_DEV"
      echo "Using dev AWS credentials"
    fi
  - echo "AWS_ROLE_ARN:" && echo $AWS_ROLE_ARN
  - docker info

build:
  stage: build
  script:
    - echo "Logging into AWS ECR..."
    - cat $AWS_WEB_IDENTITY_TOKEN_FILE
    - aws sts get-caller-identity
    - aws sts assume-role-with-web-identity --role-arn $AWS_ROLE_ARN --role-session-name gitlab-ci --web-identity-token file://$AWS_WEB_IDENTITY_TOKEN_FILE > aws_creds.json
    - echo "Token file path:" && echo $AWS_WEB_IDENTITY_TOKEN_FILE
    - aws ecr describe-repositories --repository-names $IMAGE_NAME --region $AWS_REGION || aws ecr create-repository --repository-name $IMAGE_NAME --region $AWS_REGION
    - aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com
    - echo "Building Docker image..."
    - docker build -t $IMAGE_NAME .
    - docker tag $IMAGE_NAME $ECR_REPO:latest
    - echo "Pushing image to AWS ECR..."
    - docker push $ECR_REPO:latest

deploy:
  stage: deploy
  script:
    - echo "Checking if Lambda function exists..."
    - |
      if aws lambda get-function --function-name $LAMBDA_NAME --region $AWS_REGION; then
        echo "Updating existing Lambda function..."
        aws lambda update-function-code --function-name $LAMBDA_NAME --image-uri $ECR_REPO:latest
      else
        echo "Creating new Lambda function..."
        aws lambda create-function --function-name $LAMBDA_NAME \
          --package-type Image \
          --code ImageUri=$ECR_REPO:latest \
          --role arn:aws:iam::${AWS_ACCOUNT_ID}:role/lamda_admin \
          --region $AWS_REGION \
          --memory-size 1024 \
          --timeout 600
      fi
