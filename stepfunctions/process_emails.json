{"StartAt": "EmailWatcherH", "States": {"EmailWatcherH": {"Type": "Task", "Resource": "arn:aws:lambda:##REGION##:##ACCOUNT_ID##:function:##ENV##-email_watcher", "ResultPath": "$.result", "Catch": [{"ErrorEquals": ["States.ALL"], "ResultPath": "$.errorInfo", "Next": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "Next": "CheckStatusCode"}, "CheckStatusCode": {"Type": "Choice", "Choices": [{"Variable": "$.result.statusCode", "NumericEquals": 200, "Next": "MapProcessEmail"}], "Default": "BuildManualError"}, "BuildManualError": {"Type": "Pass", "Parameters": {"errorInfo": {"Error": "HandledError", "Cause": "Lambda returned non-200 statusCode", "OriginalOutput.$": "$.result"}}, "ResultPath": "$.errorInfo", "Next": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "MapProcessEmail": {"Type": "Map", "ItemsPath": "$.result.body.emails_to_be_processed", "ResultPath": null, "Parameters": {"email_id.$": "$$.Map.Item.Value", "stage.$": "$.result.stage"}, "Iterator": {"StartAt": "ProcessingEmail", "States": {"ProcessingEmail": {"Type": "Task", "Resource": "arn:aws:lambda:##REGION##:##ACCOUNT_ID##:function:##ENV##-process_email", "ResultPath": null, "End": true}}}, "Catch": [{"ErrorEquals": ["States.ALL"], "ResultPath": "$.errorInfo", "Next": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "Next": "DetermineAppType"}, "DetermineAppType": {"Type": "Choice", "Choices": [{"Variable": "$.result.stage", "StringEquals": "post-inventory", "Next": "SetAppToBol"}], "Default": "SetAppToInvoice"}, "SetAppToBol": {"Type": "Pass", "Result": {"app": "bol"}, "ResultPath": "$.app", "Next": "WaitToProcessAllItems"}, "SetAppToInvoice": {"Type": "Pass", "Result": {"app": "invoice"}, "ResultPath": "$.app", "Next": "WaitToProcessAllItems"}, "WaitToProcessAllItems": {"Type": "Wait", "Seconds": 600, "Next": "LoadingStuckWiReport"}, "LoadingStuckWiReport": {"Type": "Task", "Resource": "arn:aws:lambda:##REGION##:##ACCOUNT_ID##:function:##ENV##-loading_wi_report", "Parameters": {"app.$": "$.app.app", "stage.$": "$.result.stage"}, "End": true}, "ErrorHandling": {"Type": "Task", "Resource": "arn:aws:lambda:##REGION##:##ACCOUNT_ID##:function:##ENV##-send_email_error", "Parameters": {"error_info.$": "$.errorInfo", "execution_id.$": "$$.Execution.Id", "state_machine_name.$": "$$.StateMachine.Name"}, "End": true}}}