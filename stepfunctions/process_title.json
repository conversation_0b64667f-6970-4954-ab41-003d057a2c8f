{"StartAt": "TitleDownloader", "States": {"TitleDownloader": {"Type": "Task", "Resource": "arn:aws:lambda:##REGION##:##ACCOUNT_ID##:function:##ENV##-title_sftp_to_s3", "ResultPath": "$.result", "Catch": [{"ErrorEquals": ["States.ALL"], "ResultPath": "$.errorInfo", "Next": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "Next": "CheckStatusCode"}, "CheckStatusCode": {"Type": "Choice", "Choices": [{"Variable": "$.result.statusCode", "NumericEquals": 200, "Next": "ProcessTitleDocument"}], "Default": "BuildManualError"}, "BuildManualError": {"Type": "Pass", "Parameters": {"errorInfo": {"Error": "HandledError", "Cause": "Lambda returned non-200 statusCode", "OriginalOutput.$": "$.result"}}, "ResultPath": "$.errorInfo", "Next": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ProcessTitleDocument": {"Type": "Map", "ItemsPath": "$.result.body", "Iterator": {"StartAt": "PdfProcessor", "States": {"PdfProcessor": {"Type": "Task", "Resource": "arn:aws:lambda:##REGION##:##ACCOUNT_ID##:function:##ENV##-pdf_utils", "Next": "ProcessTitleMap", "Catch": [{"ErrorEquals": ["States.ALL"], "ResultPath": "$.errorInfo", "Next": "ErrorHandlingProcessPDF"}]}, "ProcessTitleMap": {"Type": "Map", "ItemsPath": "$.body.pages_to_be_processed", "Parameters": {"title_page_to_process.$": "$$.Map.Item.Value", "from_file.$": "$.body.title_file_path", "is_mso.$": "$.body.is_mso"}, "Iterator": {"StartAt": "TitleToAria", "States": {"TitleToAria": {"Type": "Task", "Resource": "arn:aws:lambda:##REGION##:##ACCOUNT_ID##:function:##ENV##-title_to_aria", "Catch": [{"ErrorEquals": ["States.ALL"], "ResultPath": "$.errorInfo", "Next": "ErrorHandlingPdfProcessor"}], "End": true}, "ErrorHandlingPdfProcessor": {"Type": "Pass", "Parameters": {"error_info.$": "$.errorInfo", "execution_id.$": "$$.Execution.Id", "state_machine_name.$": "$$.StateMachine.Name", "timestamp.$": "$$.State.EnteredTime", "original_input.$": "$", "status": "error_logged"}, "End": true}}}, "End": true}, "ErrorHandlingProcessPDF": {"Type": "Pass", "Parameters": {"error_info.$": "$.errorInfo", "execution_id.$": "$$.Execution.Id", "state_machine_name.$": "$$.StateMachine.Name", "timestamp.$": "$$.State.EnteredTime", "original_input.$": "$", "status": "error_logged"}, "End": true}}}, "Catch": [{"ErrorEquals": ["States.ALL"], "ResultPath": "$.errorInfo", "Next": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "End": true}, "ErrorHandling": {"Type": "Task", "Resource": "arn:aws:lambda:##REGION##:##ACCOUNT_ID##:function:##ENV##-send_email_error", "Parameters": {"error_info.$": "$.errorInfo", "execution_id.$": "$$.Execution.Id", "state_machine_name.$": "$$.StateMachine.Name"}, "End": true}}}