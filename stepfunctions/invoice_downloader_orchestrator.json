{"StartAt": "OrchestratorDownloader", "States": {"OrchestratorDownloader": {"Type": "Task", "Parameters": {"stage.$": "$.stage"}, "Resource": "arn:aws:lambda:##REGION##:##ACCOUNT_ID##:function:##ENV##-orchestrator_downloader", "ResultPath": "$.result", "Catch": [{"ErrorEquals": ["States.ALL"], "ResultPath": "$.errorInfo", "Next": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "Next": "StoreMap"}, "StoreMap": {"Type": "Map", "ItemsPath": "$.result.body", "Parameters": {"store.$": "$$.Map.Item.Value.store", "batches.$": "$$.Map.Item.Value.batches", "stage.$": "$.result.stage"}, "Iterator": {"StartAt": "VinBatchMap", "States": {"VinBatchMap": {"Type": "Map", "ItemsPath": "$.batches", "MaxConcurrency": 1, "Parameters": {"stage.$": "$.stage", "store.$": "$.store", "data.$": "$$.Map.Item.Value"}, "Iterator": {"StartAt": "SeleniumDownloader", "States": {"SeleniumDownloader": {"Type": "Task", "Resource": "arn:aws:lambda:##REGION##:##ACCOUNT_ID##:function:##ENV##-selenium_downloader", "ResultPath": "$.result", "End": true}}}, "End": true}}}, "Catch": [{"ErrorEquals": ["States.ALL"], "ResultPath": "$.errorInfo", "Next": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "Next": "OrchestratorDownloadUpdate"}, "OrchestratorDownloadUpdate": {"Type": "Task", "Resource": "arn:aws:lambda:##REGION##:##ACCOUNT_ID##:function:##ENV##-orchestrator_download_update", "Catch": [{"ErrorEquals": ["States.ALL"], "ResultPath": "$.errorInfo", "Next": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "End": true}, "ErrorHandling": {"Type": "Task", "Resource": "arn:aws:lambda:##REGION##:##ACCOUNT_ID##:function:##ENV##-send_email_error", "Parameters": {"error_info.$": "$.errorInfo", "execution_id.$": "$$.Execution.Id", "state_machine_name.$": "$$.StateMachine.Name"}, "End": true}}}