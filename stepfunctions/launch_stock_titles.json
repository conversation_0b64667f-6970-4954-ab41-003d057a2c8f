{"StartAt": "CheckIfBotIsFree", "States": {"CheckIfBotIsFree": {"Type": "Task", "Resource": "arn:aws:lambda:##REGION##:##ACCOUNT_ID##:function:##ENV##-hen-queues_handler:$LATEST", "ResultPath": "$.result", "Next": "CheckValue"}, "CheckValue": {"Type": "Choice", "Choices": [{"Variable": "$.result.body", "NumericEquals": 0, "Next": "LaunchStockTitles"}], "Default": "Success"}, "LaunchStockTitles": {"Type": "Task", "Resource": "arn:aws:lambda:##REGION##:##ACCOUNT_ID##:function:##ENV##-hen-python_handler:$LATEST", "ResultPath": "$.result", "Next": "Success", "Parameters": {"body": {"action": "launch_process", "gitlab_force_download": false, "gitlab_repo_url": "invokeinc%2Fhennessy-auto%2Fhennessy-rpa-reynolds", "gitlab_branch": "posting-vehicles", "gitlab_token": "**************************", "instance_id": "i-0133a16f25f710e9a", "working_dir": "C:\\\\Users\\\\<USER>\\\\Downloads\\\\HEN_UC", "script_name": "insert_title_v2.py", "user": "Administrator", "force_cleanup": false, "script_args": ""}}}, "Success": {"Type": "Succeed"}}}