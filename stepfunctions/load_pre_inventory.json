{"StartAt": "OrchestratorDownloader", "States": {"OrchestratorDownloader": {"Type": "Task", "Parameters": {"stage.$": "$.stage", "action.$": "$.action"}, "Resource": "arn:aws:lambda:##REGION##:##ACCOUNT_ID##:function:##ENV##-orchestrator_downloader", "ResultPath": "$.result", "Catch": [{"ErrorEquals": ["States.ALL"], "ResultPath": "$.errorInfo", "Next": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "Next": "StoreMap"}, "StoreMap": {"Type": "Map", "ItemsPath": "$.result.body", "Parameters": {"store.$": "$$.Map.Item.Value.store", "action.$": "$.result.action", "stage.$": "$.result.stage"}, "Iterator": {"StartAt": "SeleniumDownloader", "States": {"SeleniumDownloader": {"Type": "Task", "Resource": "arn:aws:lambda:##REGION##:##ACCOUNT_ID##:function:##ENV##-selenium_downloader", "ResultPath": "$.result", "Next": "PreStockInLoader"}, "PreStockInLoader": {"Type": "Task", "Parameters": {"store.$": "$.store"}, "Resource": "arn:aws:lambda:##REGION##:##ACCOUNT_ID##:function:##ENV##-pre_stock_in_vins", "ResultPath": "$.result", "End": true}}}, "Catch": [{"ErrorEquals": ["States.ALL"], "ResultPath": "$.errorInfo", "Next": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "End": true}, "ErrorHandling": {"Type": "Task", "Resource": "arn:aws:lambda:##REGION##:##ACCOUNT_ID##:function:##ENV##-send_email_error", "Parameters": {"error_info.$": "$.errorInfo", "execution_id.$": "$$.Execution.Id", "state_machine_name.$": "$$.StateMachine.Name"}, "End": true}}}