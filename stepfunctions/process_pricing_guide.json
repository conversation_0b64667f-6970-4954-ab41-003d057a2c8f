{"StartAt": "DownloadPricingGuide", "States": {"DownloadPricingGuide": {"Type": "Task", "Resource": "arn:aws:lambda:##REGION##:##ACCOUNT_ID##:function:##ENV##-selenium_downloader", "ResultPath": "$.downloadResult", "Catch": [{"ErrorEquals": ["States.ALL"], "ResultPath": "$.errorInfo", "Next": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "Next": "CheckDownloadStatus"}, "CheckDownloadStatus": {"Type": "Choice", "Choices": [{"Variable": "$.downloadResult.statusCode", "NumericEquals": 200, "Next": "ProcessPriceGuide"}], "Default": "BuildManualError"}, "BuildManualError": {"Type": "Pass", "Parameters": {"errorInfo": {"Error": "HandledError", "Cause": "Lambda returned non-200 statusCode", "OriginalOutput.$": "$.result"}}, "ResultPath": "$.errorInfo", "Next": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ProcessPriceGuide": {"Type": "Task", "Parameters": {"store.$": "$.store"}, "Resource": "arn:aws:lambda:##REGION##:##ACCOUNT_ID##:function:##ENV##-load_pricing_guide", "ResultPath": "$.result", "Catch": [{"ErrorEquals": ["States.ALL"], "ResultPath": "$.errorInfo", "Next": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "Next": "ProcessPagesPricingGuide"}, "ProcessPagesPricingGuide": {"Type": "Map", "ItemsPath": "$.result.pages_to_process", "Parameters": {"store.$": "$.store", "items.$": "$$.Map.Item.Value"}, "Iterator": {"StartAt": "ProcessPagePricingGuide", "States": {"ProcessPagePricingGuide": {"Type": "Task", "Resource": "arn:aws:lambda:##REGION##:##ACCOUNT_ID##:function:##ENV##-load_pricing_guide_extractor", "End": true}}}, "Catch": [{"ErrorEquals": ["States.ALL"], "ResultPath": "$.errorInfo", "Next": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "End": true}, "ErrorHandling": {"Type": "Task", "Resource": "arn:aws:lambda:##REGION##:##ACCOUNT_ID##:function:##ENV##-send_email_error", "Parameters": {"error_info.$": "$.errorInfo", "execution_id.$": "$$.Execution.Id", "state_machine_name.$": "$$.StateMachine.Name"}, "End": true}}}