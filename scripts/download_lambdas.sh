#!/bin/bash

# Set the AWS profile and region
AWS_PROFILE="hennessy_snd"
AWS_REGION="us-east-1"



# Get the list of all Lambda functions
FUNCTION_NAMES=$(aws lambda list-functions --profile $AWS_PROFILE --query 'Functions[*].FunctionName' --output text --region $AWS_REGION)

# Iterate over each function and download the code
for FUNCTION_NAME in $FUNCTION_NAMES; do
    echo "Downloading Lambda function: $FUNCTION_NAME"

    # Get the download URL for the function's code
    DOWNLOAD_URL=$(aws lambda get-function --function-name $FUNCTION_NAME --profile $AWS_PROFILE --query 'Code.Location' --output text --region $AWS_REGION)

    # Download the code ZIP file
    curl -o "${FUNCTION_NAME}.zip" "$DOWNLOAD_URL"

    # Remove 'prd-' prefix if present in the function name
    FOLDER_NAME=${FUNCTION_NAME#qa-}

    # Create a directory for the function and extract the contents
    mkdir -p "$FOLDER_NAME"
    unzip "${FUNCTION_NAME}.zip" -d "$FOLDER_NAME"

    # Remove the ZIP file after extraction
    rm "${FUNCTION_NAME}.zip"

    echo "Download and extraction completed for: $FUNCTION_NAME"
done

