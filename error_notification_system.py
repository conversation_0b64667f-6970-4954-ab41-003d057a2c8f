"""
Enhanced Error Notification System with Intelligent Throttling
"""

import os
import re
import sys
import json
import traceback
import logging
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, asdict
from enum import Enum
from collections import defaultdict
from mongo_utils import MongoClientUtils


class ErrorSeverity(Enum):
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    CRITICAL = "CRITICAL"

class ErrorCategory(Enum):
    LOGIN_FAILURE = "LOGIN_FAILURE"
    NETWORK_ERROR = "NETWORK_ERROR"
    SELENIUM_ERROR = "SELENIUM_ERROR"
    DATA_NOT_FOUND = "DATA_NOT_FOUND"
    FILE_PROCESSING_ERROR = "FILE_PROCESSING_ERROR"
    S3_UPLOAD_ERROR = "S3_UPLOAD_ERROR"
    MFA_FAILURE = "MFA_FAILURE"
    TIMEOUT_ERROR = "TIMEOUT_ERROR"
    VALIDATION_ERROR = "VALIDATION_ERROR"
    SYSTEM_ERROR = "SYSTEM_ERROR"
    BATCH_PROCESSING_ERROR = "BATCH_PROCESSING_ERROR"

@dataclass
class ErrorContext:
    """Detailed error context information"""
    store: str
    vin: Optional[str] = None
    stage: Optional[str] = None
    stage_action: Optional[str] = None
    action: Optional[str] = None
    url: Optional[str] = None
    browser_logs: Optional[List[Dict]] = None
    screenshot_path: Optional[str] = None
    stack_trace: Optional[str] = None
    additional_data: Optional[Dict] = None

@dataclass
class ErrorNotification:
    """Error notification structure"""
    error_id: str
    timestamp: datetime
    severity: ErrorSeverity
    category: ErrorCategory
    title: str
    description: str
    context: ErrorContext
    resolution_steps: List[str]
    affected_vins: List[str]
    error_hash: str = ""

@dataclass
class ErrorThrottleEntry:
    """Track error throttling information"""
    error_hash: str
    first_occurrence: datetime
    last_occurrence: datetime
    count: int
    last_notification: Optional[datetime] = None
    severity: ErrorSeverity = ErrorSeverity.LOW

class IntelligentErrorNotificationSystem:
    """Enhanced error notification system with intelligent throttling"""
    
    def __init__(self, send_email_function=None):
        """
        Initialize with external email function
        send_email_function should accept (subject, html_content, attachments=None)
        """
        self.send_email_function = send_email_function
        self.mongo_client = MongoClientUtils()
        self.setup_logging()
        
        # Session tracking
        self.error_count = 0
        self.session_errors = []
        self.processed_vins = set()
        
        # Intelligent throttling
        self.error_cache: Dict[str, ErrorThrottleEntry] = {}
        self.session_error_hashes: Set[str] = set()
        
        # Configuration
        self.throttle_config = {
            ErrorSeverity.LOW: {
                'initial_delay': timedelta(hours=4),
                'max_notifications_per_day': 2,
                'skip_threshold': 3  # Skip if seen 3+ times in session
            },
            ErrorSeverity.MEDIUM: {
                'initial_delay': timedelta(hours=2),
                'max_notifications_per_day': 4,
                'skip_threshold': 2
            },
            ErrorSeverity.HIGH: {
                'initial_delay': timedelta(minutes=30),
                'max_notifications_per_day': 8,
                'skip_threshold': 1
            },
            ErrorSeverity.CRITICAL: {
                'initial_delay': timedelta(minutes=0),  # No delay for critical
                'max_notifications_per_day': 20,
                'skip_threshold': 0  # Never skip critical errors
            }
        }
        
        # Categories that should be downgraded in severity
        self.severity_downgrades = {
            ErrorCategory.DATA_NOT_FOUND: ErrorSeverity.LOW,
            ErrorCategory.TIMEOUT_ERROR: ErrorSeverity.MEDIUM,
            ErrorCategory.SELENIUM_ERROR: ErrorSeverity.MEDIUM,
        }

        # Action-specific tracking to prevent duplicates
        self.notifications_sent = set()  # Track sent notification hashes
        self.batch_notification_sent = False  # Prevent duplicate batch notifications
        
        # Action-specific severity rules
        self.action_severity_rules = {
            'manual_trigger': {
                'LOGIN': ErrorSeverity.CRITICAL,
                'default': ErrorSeverity.HIGH
            },
            'reset_password': {
                'LOGIN': ErrorSeverity.CRITICAL,
                'CHANGE_PASSWORD': ErrorSeverity.HIGH,
                'default': ErrorSeverity.HIGH
            },
            'invoice_download': {
                'LOGIN': ErrorSeverity.CRITICAL,  # Blocks all VINs
                'SEARCH_INVOICE': ErrorSeverity.LOW,  # VIN not found is common
                'SAVE_INVOICE': ErrorSeverity.MEDIUM,
                'UPLOAD_TO_S3': ErrorSeverity.HIGH,
                'default': ErrorSeverity.MEDIUM
            },
            'download_new_vehicles_report': {
                'LOGIN': ErrorSeverity.CRITICAL,
                'default': ErrorSeverity.MEDIUM
            },
            'download_honda_pricing_guide': {
                'LOGIN': ErrorSeverity.CRITICAL,
                'default': ErrorSeverity.MEDIUM
            }
        }
        
    def setup_logging(self):
        """Setup enhanced logging"""
        log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        logging.basicConfig(
            level=logging.INFO,
            format=log_format,
            handlers=[
                logging.FileHandler('/tmp/automation_errors.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def _generate_error_hash(self, error: Exception, context: ErrorContext, category: ErrorCategory) -> str:
        """Generate a unique hash for error throttling"""
        # Create hash based on error type, message pattern, store, and category
        error_type = type(error).__name__
        error_msg = str(error)
        
        # Normalize error message to group similar errors
        normalized_msg = self._normalize_error_message(error_msg)
        
        hash_components = [
            error_type,
            normalized_msg,
            context.store,
            context.action or "",
            category.value
        ]
        
        hash_string = "|".join(hash_components)
        return hashlib.md5(hash_string.encode()).hexdigest()[:12]
    
    def _normalize_error_message(self, error_msg: str) -> str:
        """Normalize error message to group similar errors"""        
        try:
            # Remove VIN-specific information
            error_msg = re.sub(r'\b[A-Z0-9]{17}\b', '[VIN]', error_msg)
            # Remove timestamps (FIXED: \T to \t)
            error_msg = re.sub(r'\d{4}-\d{2}-\d{2}[\s\t]\d{2}:\d{2}:\d{2}', '[TIMESTAMP]', error_msg)
            # Remove file paths
            error_msg = re.sub(r'/[^\s]+', '[PATH]', error_msg)
            # Remove numbers that might be session-specific
            error_msg = re.sub(r'\b\d{3,}\b', '[NUMBER]', error_msg)
            
            return error_msg.lower().strip()
        except Exception as e:
            # Fallback if regex fails
            self.logger.error(f"Error normalizing message: {e}")
            return str(error_msg).lower().strip()
    
    def _get_error_template(self, template_type: str, severity: str = None, app_id: str = None) -> Optional[Dict]:
        """
        Get error notification template from MongoDB with proper connection handling
        """
        try:
            # Ensure we have a fresh connection
            if self.mongo_client is None:
                self.mongo_client = MongoClientUtils()
            
            query = {"template_type": template_type, "active": True}
            self.logger.info(f"Using app+severity-specific error template for {template_type}-{app_id}-{severity}")
            # Try severity and app-specific template first
            if app_id and severity:
                specific_query = {**query, "app_id": app_id, "severity": severity.upper()}
                template = self.mongo_client.db.EmailTemplates.find_one(specific_query)
                if template:
                    self.logger.info(f"Using app+severity-specific error template for {app_id}-{severity}")
                    try:
                        self.mongo_client.close_connection()
                        self.mongo_client = None
                    except:
                        pass
                    return template
            
            # Try app-specific template
            if app_id:
                app_query = {**query, "app_id": app_id}
                template = self.mongo_client.db.EmailTemplates.find_one(app_query)
                if template:
                    self.logger.info(f"Using app-specific error template for {app_id}")
                    try:
                        self.mongo_client.close_connection()
                        self.mongo_client = None
                    except:
                        pass
                    return template
            
            # Try severity-specific template
            if severity:
                severity_query = {**query, "severity": severity.upper()}
                template = self.mongo_client.db.EmailTemplates.find_one(severity_query)
                if template:
                    self.logger.info(f"Using severity-specific error template for {severity}")
                    try:
                        self.mongo_client.close_connection()
                        self.mongo_client = None
                    except:
                        pass
                    return template
            
            # Fall back to default template
            default_query = {**query, "app_id": None, "severity": None}
            template = self.mongo_client.db.EmailTemplates.find_one(default_query)
            
            try:
                self.mongo_client.close_connection()
                self.mongo_client = None
            except:
                pass

            if template:
                self.logger.info(f"Using default error template for {template_type}")
                return template
            else:
                self.logger.error(f"No error template found for {template_type}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error fetching error notification template for {template_type}: {str(e)}")
            self.mongo_client = None
            return None

    def _process_error_template_variables(self, template_str: str, notification: ErrorNotification, additional_vars: Dict = None) -> str:
        """Enhanced template processing with MongoDB template variables"""
        try:
            app_id = additional_vars.get('app_id', 'default') if additional_vars else 'default'
            action = notification.context.action
            severity = notification.severity.value

                # Get additional context safely
            additional_context = {}
            if additional_vars and 'additional_context' in additional_vars:
                additional_context = additional_vars['additional_context']
            elif notification.context.additional_data:
                additional_context = notification.context.additional_data
            
            # Build action-specific content
            action_details = self._build_action_details(action, notification, additional_context)
            
            # Get the current template's variables if available
            template_vars = {}
            if hasattr(self, 'current_template') and self.current_template:
                template_vars = self.current_template.get('template_variables', {})
            
            # Extract severity-specific variables from template
            severity_config = {}
            if 'severity_configs' in template_vars and severity in template_vars['severity_configs']:
                severity_config = template_vars['severity_configs'][severity]
            
            # Extract app-specific variables
            app_section = ''
            if 'app_sections' in template_vars:
                app_section = template_vars['app_sections'].get(app_id, template_vars['app_sections'].get('default', ''))
            
            # Build complete variables dict
            variables = {
                # Basic notification data
                'error_id': notification.error_id,
                'title': notification.title,
                'description': notification.description,
                'severity_value': severity,
                'category_value': notification.category.value,
                'timestamp_formatted': notification.timestamp.strftime('%Y-%m-%d %H:%M:%S UTC'),
                
                # Context data
                'store': notification.context.store,
                'vin': notification.context.vin or 'N/A',
                'stage': notification.context.stage or 'N/A', 
                'action': notification.context.action or 'N/A',
                'url': notification.context.url or 'N/A',
                'stack_trace': notification.context.stack_trace or 'No stack trace available',
                
                # Resolution data
                'resolution_steps_html': ''.join(f"<li>{step}</li>" for step in notification.resolution_steps),
                'affected_vins_count': len(notification.affected_vins),
                
                # Dynamic variables from template config
                'severity_icon': severity_config.get('severity_icon', self._get_default_severity_icon(severity)),
                'action_text': severity_config.get('action_text', self._get_default_action_text(severity)),
                'bg_color': severity_config.get('bg_color', '#f8f9fa'),
                'accent_color': severity_config.get('accent_color', '#6c757d'),
                'header_gradient': severity_config.get('header_gradient', 'linear-gradient(135deg, #6c757d 0%, #495057 100%)'),
                'header_title': severity_config.get('header_title', 'Error Alert'),
                
                # App-specific content
                'app_section': app_section,
                
                # Conditional sections
                'vin_section': self._build_vin_section(notification),
                'browser_section': self._build_browser_section(notification),
                'stack_trace_section': self._build_stack_trace_section(notification.context.stack_trace),

                # Action-specific variables
                'action_details_section': action_details['details_section'],
                'impact_assessment': action_details['impact_assessment'],
                'affected_resources': action_details['affected_resources'],
                'next_steps_specific': action_details['next_steps'],
                'urgency_level': action_details['urgency_level'],
                
                # VIN-specific information for invoice downloads
                'vin_batch_info': self._build_vin_batch_info(additional_context),
                'batch_progress_section': self._build_batch_progress_section(additional_context),
                
                **(additional_vars or {})
            }
            
            # Process the template
            processed = template_str
            for key, value in variables.items():
                placeholder = "{" + key + "}"
                if placeholder in processed:
                    processed = processed.replace(placeholder, str(value))
            
            return processed
            
        except Exception as e:
            self.logger.error(f"Error processing template variables: {str(e)}")
            return template_str

    def _build_batch_progress_section(self, context):
        """Build batch progress section for VIN processing"""
        
        if not context.get('total_vins_in_batch'):
            return ''
        
        total = context.get('total_vins_in_batch', 0)
        failed_vins = context.get('failed_vins', [])
        successful_vins = context.get('successful_vins', [])
        
        progress_percentage = (len(successful_vins) / total * 100) if total > 0 else 0
        
        return f'''
        <div class="batch-progress-section">
            <h4>Batch Processing Progress</h4>
            <div class="progress-bar" style="background:#e9ecef;border-radius:4px;margin:10px 0;">
                <div style="background:#28a745;height:20px;width:{progress_percentage}%;border-radius:4px;"></div>
            </div>
            <p><strong>Progress:</strong> {len(successful_vins)}/{total} VINs processed ({progress_percentage:.1f}%)</p>
            <div class="progress-details">
                <p><strong>Successful:</strong> {len(successful_vins)} VINs</p>
                <p><strong>Failed:</strong> {len(failed_vins)} VINs</p>
                <p><strong>Remaining:</strong> {total - len(successful_vins) - len(failed_vins)} VINs</p>
            </div>
        </div>
        '''

    def _build_action_details(self, action, notification, context):
        """Build action-specific details for email content"""
        # print("action, notification, context",action, notification, context)
        # print("notification.context", notification.context)
        # print("notification.context.store", notification.context.store)
        if action == 'manual_trigger':
            return {
                'details_section': self._build_manual_trigger_details(notification),
                'impact_assessment': 'Login function verification failed',
                'affected_resources': f'Store: {notification.context.store}',
                'next_steps': 'Verify credentials and test login manually',
                'urgency_level': 'Medium - Automated testing affected'
            }
        
        elif action == 'reset_password':
            return {
                'details_section': self._build_password_reset_details(notification),
                'impact_assessment': 'Password reset operation failed',
                'affected_resources': f'Store: {notification.context.store} credentials',
                'next_steps': 'Manual password reset required',
                'urgency_level': 'High - Credential management affected'
            }
        
        elif action == 'invoice_download':
            additional_data = context.get('additional_data', {})
        
            if additional_data.get('error_level') == 'batch':
                return {
                    'details_section': self._build_batch_download_details(notification, additional_data),
                    'impact_assessment': self._assess_batch_impact(additional_data),
                    'affected_resources': self._build_batch_affected_summary(additional_data),
                    'next_steps': self._build_batch_next_steps(additional_data),
                    'urgency_level': self._determine_batch_urgency(additional_data)
                }
            else:
                # Handle single VIN errors as before
                return self._build_single_vin_details(notification, context)
        
        else:
            return {
                'details_section': f'<p>Error in {action} operation</p>',
                'impact_assessment': 'Operation failed',
                'affected_resources': 'Unknown scope',
                'next_steps': 'Manual investigation required',
                'urgency_level': 'Medium'
            }

    def _build_single_vin_details(self, notification, context):
        """Build details for single VIN errors (fallback)"""
        return {
            'details_section': self._build_invoice_download_details(notification, context),
            'impact_assessment': f'Single VIN processing failed: {notification.context.vin}',
            'affected_resources': f'VIN: {notification.context.vin}',
            'next_steps': self._build_invoice_next_steps(notification, context),
            'urgency_level': 'Medium - Single VIN affected'
        }
    
    def _build_batch_download_details(self, notification, additional_data):
        """Build details for batch download errors"""
        batch_size = additional_data.get('batch_size', 0)
        failed_vins = additional_data.get('failed_vins', [])
        successful_vins = additional_data.get('successful_vins', [])
        error_summary = additional_data.get('error_summary', {})
        
        return f'''
        <div class="batch-download-details">
            <h4>Batch Download Processing Results</h4>
            <div class="batch-overview">
                <p><strong>Batch Size:</strong> {batch_size} VINs</p>
                <p><strong>Success Rate:</strong> {len(successful_vins)}/{batch_size} ({len(successful_vins)/batch_size*100:.1f}%)</p>
                <p><strong>Failure Rate:</strong> {len(failed_vins)}/{batch_size} ({len(failed_vins)/batch_size*100:.1f}%)</p>
            </div>
            
            <div class="error-breakdown">
                <h5>Error Breakdown:</h5>
                {self._format_error_categories(error_summary.get('categories', {}))}
            </div>
            
            <div class="failed-vins-list">
                <h5>Failed VINs:</h5>
                <p>{', '.join(failed_vins[:10])}{'...' if len(failed_vins) > 10 else ''}</p>
                {f'<p>Showing 10 of {len(failed_vins)} failed VINs</p>' if len(failed_vins) > 10 else ''}
            </div>
        </div>
        '''

    def _format_error_categories(self, categories):
        """Format error categories for display"""
        if not categories:
            return '<p>No error categorization available</p>'
        
        category_html = '<ul>'
        for category, vins in categories.items():
            category_html += f'<li><strong>{category}:</strong> {len(vins)} VINs</li>'
        category_html += '</ul>'
        
        return category_html


    def _build_invoice_next_steps(self, notification, context):
        """Build invoice-specific next steps"""
        stage = notification.context.stage
        
        if stage == 'LOGIN':
            return '''
            <ul>
                <li>Verify store credentials in AWS Secrets Manager</li>
                <li>Test manual login to confirm account status</li>
                <li>Check for MFA requirements or security updates</li>
                <li>Retry batch processing after login resolution</li>
            </ul>
            '''
        elif 'not found' in str(notification.description).lower():
            return '''
            <ul>
                <li>Verify VIN format and validity</li>
                <li>Check data availability date ranges</li>
                <li>Confirm VIN exists in dealer system</li>
                <li>No immediate action required for missing VINs</li>
            </ul>
            '''
        else:
            return '''
            <ul>
                <li>Check network connectivity and site status</li>
                <li>Review browser compatibility and updates</li>
                <li>Monitor for website maintenance windows</li>
                <li>Retry processing after resolving technical issues</li>
            </ul>
            '''

    # def _determine_invoice_urgency(self, notification, context):
    #     """Determine urgency level for invoice download errors"""
    #     stage = notification.context.stage
    #     total_vins = context.get('total_vins_in_batch', 0)
    #     failed_vins = context.get('failed_vins', [])
        
    #     if stage == 'LOGIN':
    #         return 'CRITICAL - Login failure blocks all processing'
    #     elif total_vins > 0 and len(failed_vins) == total_vins:
    #         return 'HIGH - Complete batch failure'
    #     elif len(failed_vins) > total_vins * 0.5:
    #         return 'MEDIUM - Majority of batch affected'
    #     else:
    #         return 'LOW - Minor processing issues'

    def _build_manual_trigger_details(self, notification):
        """Build details for manual trigger errors"""
        return f'''
        <div class="manual-trigger-details">
            <h4>Manual Login Test Context</h4>
            <p><strong>Test Type:</strong> Automated login verification</p>
            <p><strong>Purpose:</strong> Validate store credentials and connectivity</p>
            <p><strong>Error Stage:</strong> {notification.context.stage}</p>
            <p><strong>Test Result:</strong> Failed - {notification.description}</p>
        </div>
        '''

    def _build_password_reset_details(self, notification):
        """Build details for password reset errors"""
        return f'''
        <div class="password-reset-details">
            <h4>Password Reset Operation</h4>
            <p><strong>Operation:</strong> Automated password rotation</p>
            <p><strong>Stage:</strong> {notification.context.stage}</p>
            <p><strong>Error:</strong> {notification.description}</p>
            <p><strong>Security Impact:</strong> Password rotation failed, old credentials still active</p>
        </div>
        '''

    def _build_invoice_download_details(self, notification, context):
        """Build detailed information for invoice download errors"""
        
        stage = notification.context.stage
        total_vins = context.get('total_vins_in_batch', 0)
        failed_vins = context.get('failed_vins', [])
        successful_vins = context.get('successful_vins', [])
        
        details = f'''
        <div class="invoice-details">
            <h4>Invoice Download Context</h4>
            <p><strong>Processing Stage:</strong> {stage}</p>
            <p><strong>Batch Size:</strong> {total_vins} VINs</p>
            <p><strong>Progress:</strong> {len(successful_vins)} successful, {len(failed_vins)} failed</p>
        '''
        
        if stage == 'LOGIN':
            details += '''
            <div class="critical-impact">
                <h5>Critical Impact: Login Failure</h5>
                <p>Login failure during invoice download affects entire batch processing.</p>
                <p>All VINs in current batch are affected until login is resolved.</p>
            </div>
            '''
        
        if failed_vins:
            details += f'''
            <div class="failed-vins">
                <h5>Failed VINs:</h5>
                <p>{", ".join(failed_vins[:10])}{"..." if len(failed_vins) > 10 else ""}</p>
                <p>Total failed: {len(failed_vins)}</p>
            </div>
            '''
        
        details += '</div>'
        return details

    # def _assess_invoice_download_impact(self, context):
    #     """Assess the impact of invoice download errors"""
        
    #     total_vins = context.get('total_vins_in_batch', 0)
    #     failed_vins = context.get('failed_vins', [])
        
    #     if total_vins == 0:
    #         return 'Single VIN processing affected'
        
    #     failure_rate = len(failed_vins) / total_vins * 100
        
    #     if failure_rate == 100:
    #         return f'Complete batch failure - All {total_vins} VINs affected'
    #     elif failure_rate > 50:
    #         return f'Major batch failure - {len(failed_vins)}/{total_vins} VINs affected ({failure_rate:.1f}%)'
    #     elif failure_rate > 20:
    #         return f'Partial batch failure - {len(failed_vins)}/{total_vins} VINs affected ({failure_rate:.1f}%)'
    #     else:
    #         return f'Minor impact - {len(failed_vins)}/{total_vins} VINs affected ({failure_rate:.1f}%)'

    # def _build_affected_vins_summary(self, context):
    #     """Build summary of affected VINs"""
        
    #     failed_vins = context.get('failed_vins', [])
    #     total_vins = context.get('total_vins_in_batch', 0)
        
    #     if not failed_vins:
    #         return 'No VINs directly affected'
        
    #     summary = f'Affected VINs: {len(failed_vins)}'
    #     if total_vins > 0:
    #         summary += f' out of {total_vins} total'
        
    #     return summary

    def _build_vin_batch_info(self, context):
        """Build VIN batch information section"""
        
        if not context.get('total_vins_in_batch'):
            return ''
        
        total = context.get('total_vins_in_batch', 0)
        failed = len(context.get('failed_vins', []))
        successful = len(context.get('successful_vins', []))
        
        return f'''
        <div class="vin-batch-info">
            <h4>Batch Processing Status</h4>
            <div class="batch-stats">
                <span class="stat">Total: {total}</span>
                <span class="stat success">Success: {successful}</span>
                <span class="stat failed">Failed: {failed}</span>
            </div>
        </div>
        '''

    # Add these helper methods:
    def _get_default_severity_icon(self, severity: str) -> str:
        icons = {'CRITICAL': '🚨', 'HIGH': '⚠️', 'MEDIUM': '🔸', 'LOW': 'ℹ️'}
        return icons.get(severity, '🔸')

    def _get_default_action_text(self, severity: str) -> str:
        actions = {'CRITICAL': 'IMMEDIATE ACTION REQUIRED', 'HIGH': 'Action Required', 'MEDIUM': 'Review Required', 'LOW': 'Information'}
        return actions.get(severity, 'Review Required')

    def _build_vin_section(self, notification: ErrorNotification) -> str:
        if notification.context.vin:
            return f'''<div class="info-card"><strong>Vehicle Information</strong><br>VIN: {notification.context.vin}<br>Affected Count: {len(notification.affected_vins)}</div>'''
        return ''

    def _build_browser_section(self, notification: ErrorNotification) -> str:
        if notification.context.url:
            return f'''<div class="info-card"><strong>Browser Context</strong><br>URL: {notification.context.url}<br>Browser State: Available</div>'''
        return '''<div class="info-card"><strong>Browser Context</strong><br>URL: Not Available<br>Browser State: System Level Error</div>'''

    def _build_stack_trace_section(self, stack_trace: str) -> str:
        if stack_trace and stack_trace != 'No stack trace available':
            escaped_trace = stack_trace.replace('<', '&lt;').replace('>', '&gt;')
            return f'''<div class="stack-trace-section"><h4>Stack Trace</h4><pre style="color: #4fc3f7; font-size: 11px;">{escaped_trace}</pre></div>'''
        return ''
    
    # Helper methods for dynamic template variables
    def _fix_template_css(self, html_content: str) -> str:
        """Fix double curly braces in CSS that break email styling"""
        try:
            # Find CSS blocks and fix double braces
            def fix_css_block(match):
                css_content = match.group(1)
                # Replace double braces with single braces in CSS only
                fixed_css = re.sub(r'(\w+)\{\{([^}]+)\}\}', r'\1{\2}', css_content)
                return f'<style>{fixed_css}</style>'
            
            # Apply fix to style blocks
            fixed_html = re.sub(r'<style>(.*?)</style>', fix_css_block, html_content, flags=re.DOTALL)
            
            return fixed_html
            
        except Exception as e:
            self.logger.error(f"Error fixing CSS in template: {e}")
            return html_content

    # def send_password_reset_success(self, store: str, user: str, app_id: str = None):
    #     """Send a success notification for password reset"""
    #     if not self.send_email_function:
    #         self.logger.error("No email function provided - cannot send password reset notification")
    #         return
            
    #     try:
    #         # Simple success notification
    #         subject = f"Password Reset Success - {store}"
    #         html_content = f"""
    #             <html>
    #             <body>
    #                 <h2>Password Reset Successful</h2>
    #                 <p>The password has been successfully reset for:</p>
    #                 <ul>
    #                     <li>Store: {store}</li>
    #                     <li>User: {user}</li>
    #                     <li>Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}</li>
    #                 </ul>
    #                 <p>The new password has been securely stored in AWS Secrets Manager.</p>
    #             </body>
    #             </html>
    #         """
            
    #         # Send email
    #         self.send_email_function(subject, html_content, [])
    #         self.logger.info(f"Password reset success notification sent for {store}")

    #     except Exception as e:
    #         self.logger.error(f"Failed to send password reset success notification: {e}")


    def send_password_reset_success(self, store: str, user: str, app_id: str = None):
        """Send a success notification for password reset using MongoDB template"""
        if not self.send_email_function:
            self.logger.error("No email function provided - cannot send password reset success notification")
            return
            
        try:
            # Get success template from MongoDB
            template = self._get_error_template('password_reset_success', app_id=app_id)
            
            if not template:
                self.logger.warning("No success template found, using fallback")
                self._send_fallback_success_notification(store, user)
                return
            
            # Create template variables
            variables = {
                'client': os.environ.get('CUSTOMER', 'System'),
                'store': store,
                'user': user,
                'timestamp_formatted': datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC'),
                'app_section': self._get_app_section(app_id or f"{store}_downloader"),
            }
            
            # Process template
            subject_template = template.get('subject', 'Password Reset Successful: {store}')
            subject = subject_template.format(**variables)
            
            html_content_template = template.get('html_content', '<html><body><p>Password reset successful</p></body></html>')
            html_content = html_content_template.format(**variables)
            
            # Send email
            self.send_email_function(subject, html_content, [])
            self.logger.info(f"Password reset success notification sent for {store}")

        except Exception as e:
            self.logger.error(f"Failed to send password reset success notification: {e}")

    def _send_fallback_success_notification(self, store: str, user: str):
        """Send basic fallback success notification"""
        subject = f"Password Reset Successful - {store}"
        html_content = f"""
            <html>
            <body style="font-family: Arial, sans-serif; margin: 20px; background-color: #e8f5e8;">
                <div style="background: white; border-radius: 8px; padding: 20px; border: 2px solid #4caf50;">
                    <div style="background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%); color: white; padding: 20px; text-align: center; border-radius: 5px; margin-bottom: 20px;">
                        <h2>✅ Password Reset Successful</h2>
                        <p><strong>Time:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}</p>
                    </div>
                    
                    <div style="background: #e8f5e8; border-left: 4px solid #4caf50; padding: 15px; margin: 15px 0; border-radius: 5px;">
                        <h3>Password Reset Details</h3>
                        <p><strong>Store:</strong> {store}</p>
                        <p><strong>User:</strong> {user}</p>
                        <p><strong>Status:</strong> Successfully completed</p>
                    </div>
                    
                    <div style="background: #e3f2fd; border: 1px solid #2196f3; padding: 15px; margin: 15px 0; border-radius: 5px;">
                        <h3>Security Information</h3>
                        <p>The new password has been securely stored in AWS Secrets Manager and is immediately active for all automation systems.</p>
                    </div>
                </div>
            </body>
            </html>
        """
        
        self.send_email_function(subject, html_content, [])

    def _get_app_section(self, app_id: str) -> str:
        """Get app-specific badge for success notifications"""
        sections = {
            'FOR_downloader': '<div class="app-badge" style="background:rgba(255,255,255,0.2);padding:8px 16px;border-radius:20px;display:inline-block;margin-bottom:10px;">FORD DEALERSHIP</div>',
            'HON_downloader': '<div class="app-badge" style="background:rgba(255,255,255,0.2);padding:8px 16px;border-radius:20px;display:inline-block;margin-bottom:10px;">HONDA DEALERSHIP</div>',
            'invoice_downloader': '<div class="app-badge" style="background:rgba(255,255,255,0.2);padding:8px 16px;border-radius:20px;display:inline-block;margin-bottom:10px;">INVOICE DOWNLOADER</div>'
        }
        return sections.get(app_id, '')

    def _determine_action_severity(self, error: Exception, context: ErrorContext, category: ErrorCategory) -> ErrorSeverity:
        """Determine severity based on action type and stage"""
        action = context.action or 'unknown'
        stage = context.stage or 'default'
        
        # Get action-specific rules
        if action in self.action_severity_rules:
            rules = self.action_severity_rules[action]
            if stage in rules:
                return rules[stage]
            return rules.get('default', ErrorSeverity.MEDIUM)
        
        # Default severity logic
        if category == ErrorCategory.LOGIN_FAILURE:
            return ErrorSeverity.CRITICAL
        elif category == ErrorCategory.DATA_NOT_FOUND:
            return ErrorSeverity.LOW
        
        return ErrorSeverity.MEDIUM

    def _should_send_notification(self, error_hash: str, severity: ErrorSeverity, context: ErrorContext) -> tuple[bool, str]:
        """Enhanced notification logic with action-specific rules"""
        action = context.action or 'unknown'
        
        # For critical actions, create a content-based key to prevent duplicates
        if action in ['manual_trigger', 'reset_password', 'download_honda_pricing_guide']:
            # Use store + action + basic error content (not hash which can vary)
            base_error = str(context.additional_data.get('original_error', '')) if context.additional_data else ''
            if not base_error:
                # Extract from description or stack trace
                stack_trace = context.stack_trace or ''
                if 'Ford password reset login failed' in stack_trace:
                    base_error = 'Ford password reset login failed'
                elif 'Login Failed' in stack_trace:
                    base_error = 'Login Failed'
                else:
                    base_error = action
            
            # Create a simple, consistent key
            notification_key = f"{action}_{context.store}_{base_error.split(':')[0]}"
            
            # Log for debugging
            self.logger.info(f"Checking notification key: {notification_key}")
            self.logger.info(f"Already sent: {notification_key in self.notifications_sent}")
            
            if notification_key not in self.notifications_sent:
                self.notifications_sent.add(notification_key)
                self.logger.info(f"Added to sent list: {notification_key}")
                return True, f"Critical action '{action}' first notification for {context.store}"
            return False, f"Already notified for critical action '{action}' in {context.store} - key: {notification_key}"
    

        # For invoice_download, use batch logic
        if action == 'invoice_download':
            # Don't send individual VIN notifications, wait for batch
            if context.vin and not context.additional_data.get('error_level') == 'batch':
                # return False, "Individual VIN error - will be included in batch notification"
                # Special case: Don't even track VIN not found for notifications
                if context.stage == 'SEARCH_INVOICE' and 'not found' in str(context.additional_data.get('error', '')).lower():
                    return False, "VIN not found - normal occurrence, no notification needed"
                return False, "Individual VIN error - will be included in batch notification"
            
            # Send batch notification only once
            if context.additional_data.get('error_level') == 'batch':

                # Check if all failures are just VIN not found
                failed_vins = context.additional_data.get('failed_vins', [])
                vin_not_found_count = context.additional_data.get('vin_not_found_count', 0)

            
                if len(failed_vins) == 0 and vin_not_found_count > 0:
                    return False, "All failures are VIN not found - no notification needed"
            
                if not self.batch_notification_sent:
                    self.batch_notification_sent = True
                    return True, "Batch notification - first occurrence"
                return False, "Batch notification already sent"
        
        # Original throttling logic for other cases
        return self._apply_throttling_rules(error_hash, severity)

    def _apply_throttling_rules(self, error_hash: str, severity: ErrorSeverity) -> tuple[bool, str]:
        """Apply standard throttling rules"""
        now = datetime.now()
        config = self.throttle_config[severity]
        
        # Check if we've seen this error before
        if error_hash not in self.error_cache:
            self.error_cache[error_hash] = ErrorThrottleEntry(
                error_hash=error_hash,
                first_occurrence=now,
                last_occurrence=now,
                count=1,
                severity=severity
            )
            return True, "First occurrence"
        
        entry = self.error_cache[error_hash]
        entry.last_occurrence = now
        entry.count += 1
        
        # Check if enough time has passed since last notification
        if entry.last_notification:
            time_since_last = now - entry.last_notification
            if time_since_last < config['initial_delay']:
                return False, f"Throttled: {time_since_last} < {config['initial_delay']}"
        
        return True, "Throttling rules allow notification"

    def _build_action_specific_description(self, error: Exception, context: ErrorContext, category: ErrorCategory) -> str:
        """Build action-specific error descriptions"""
        action = context.action or 'unknown'
        base_error = str(error)
        
        if action == 'manual_trigger':
            return f"Login verification failed for store {context.store}: {base_error}"
        
        elif action == 'reset_password':
            if 'login' in base_error.lower():
                return f"Password reset failed - cannot authenticate with current credentials for {context.store}: {base_error}"
            return f"Password reset operation failed for store {context.store}: {base_error}"
        
        elif action == 'invoice_download':
            if context.stage == 'LOGIN':
                affected_vins = context.additional_data.get('failed_vins', []) if context.additional_data else []
                vin_count = len(affected_vins) if affected_vins else 1
                return f"Invoice download blocked by login failure - {vin_count} VINs affected for store {context.store}: {base_error}"
            
            elif context.stage == 'SEARCH_INVOICE' and 'not found' in base_error.lower():
                return f"VIN {context.vin} not found in {context.store} system - this is normal for some VINs"
            
            elif context.additional_data and context.additional_data.get('error_level') == 'batch':
                failed_count = len(context.additional_data.get('failed_vins', []))
                total_count = context.additional_data.get('batch_size', 0)
                return f"Batch processing completed with {failed_count}/{total_count} VINs failed for store {context.store}"
        
        elif action in ['download_new_vehicles_report', 'download_honda_pricing_guide']:
            if context.stage == 'LOGIN':
                return f"Report download blocked by login failure for store {context.store}: {base_error}"
            return f"Report download failed for store {context.store}: {base_error}"
        
        return base_error

    def capture_error(self, 
                    error: Exception, 
                    context: ErrorContext, 
                    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                    category: ErrorCategory = ErrorCategory.SYSTEM_ERROR,
                    app_id: str = None) -> str:
        """Capture and intelligently process error with action-specific handling"""

        # Filter out Manheim store errors
        if context.store and context.store.upper() == 'MANHEIM':
            self.logger.info(f"Skipping notification for Manheim store error: {str(error)}")
            return f"IGNORED_MANHEIM_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        if context.action == 'reset_password':
            session_key = f"reset_password_{context.store}"
            
            # Use a class-level attribute that persists across instances
            if not hasattr(self.__class__, '_global_session_notifications'):
                self.__class__._global_session_notifications = set()
            
            if session_key in self.__class__._global_session_notifications:
                self.logger.info(f"Password reset notification already sent for {context.store} - skipping duplicate")
                return f"DUPLICATE_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            self.__class__._global_session_notifications.add(session_key)
        
        error_id = f"ERR_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{self.error_count:04d}"
        self.error_count += 1
        
        # Determine severity based on action and stage
        action_severity = self._determine_action_severity(error, context, category)
        final_severity = action_severity if action_severity.value >= severity.value else severity
        
        # Generate action-specific description
        action_description = self._build_action_specific_description(error, context, category)
        
        # Generate error hash for throttling
        error_hash = self._generate_error_hash(error, context, category)
        
        # Generate error notification
        notification = ErrorNotification(
            error_id=error_id,
            timestamp=datetime.now(),
            severity=final_severity,
            category=category,
            title=f"{os.environ.get('CUSTOMER', 'System')} [{context.store} - {context.stage_action or 'Unknown'} - {context.action or 'Unknown'}] {category.value}",
            description=action_description,
            context=context,
            resolution_steps=self._get_action_specific_resolution_steps(category, context.action, context.stage),
            affected_vins=[context.vin] if context.vin else [],
            error_hash=error_hash
        )
        
        # Add stack trace
        notification.context.stack_trace = traceback.format_exc()
        
        # Track for session
        self.session_errors.append(notification)
        if context.vin:
            self.processed_vins.add(context.vin)
        
        # Log the error
        self.logger.error(f"Error {error_id} [{context.action}]: {notification.description}")
        
        # Check if we should send notification
        should_send, reason = self._should_send_notification(error_hash, final_severity, context)
        
        if should_send:
            self.logger.info(f"Sending notification for {error_id}: {reason}")
            self._send_immediate_notification(notification, app_id)
        else:
            self.logger.info(f"Notification skipped for {error_id}: {reason}")
        
        return error_id


    def _get_action_specific_resolution_steps(self, category: ErrorCategory, action: str, stage: str) -> List[str]:
        """Get action and stage specific resolution steps"""
        
        if action == 'manual_trigger':
            return [
                "Verify store credentials in AWS Secrets Manager",
                "Test manual login to confirm account status", 
                "Check for MFA requirements or password expiration",
                "Validate network connectivity to store website"
            ]
        
        elif action == 'reset_password':
            if stage == 'LOGIN':
                return [
                    "Verify current password is correct before reset attempt",
                    "Check if account is locked or suspended",
                    "Ensure MFA/OTP codes are accessible",
                    "Contact store support if login issues persist"
                ]
            return [
                "Verify password complexity requirements",
                "Check for special character restrictions",
                "Ensure new password meets store policies",
                "Retry password reset operation"
            ]
        
        elif action == 'invoice_download':
            if stage == 'LOGIN':
                return [
                    "Verify store credentials and account status",
                    "Check for MFA requirements",
                    "Ensure account has invoice download permissions",
                    "Retry processing after login resolution - all VINs affected"
                ]
            elif stage == 'SEARCH_INVOICE':
                return [
                    "Verify VIN format and validity (17 characters)",
                    "Check VIN exists in dealer inventory system",
                    "Confirm invoice date ranges are correct",
                    "VIN not found is normal - no action required"
                ]
            else:
                return [
                    "Check network connectivity and website status",
                    "Verify browser compatibility",
                    "Review download permissions and file paths",
                    "Retry individual VIN processing"
                ]
        
        elif action in ['download_new_vehicles_report', 'download_honda_pricing_guide']:
            if stage == 'LOGIN':
                return [
                    "Verify store credentials and permissions",
                    "Check account has report access rights",
                    "Ensure no account restrictions for reports",
                    "Test manual login and report access"
                ]
            return [
                "Check report availability and schedules",
                "Verify network connectivity",
                "Review file download permissions",
                "Retry report download operation"
            ]
        
        # Fallback to original method
        return self._get_resolution_steps(category)

    def _send_immediate_notification(self, notification: ErrorNotification, app_id: str = None):
        """Send immediate email notification for errors using MongoDB template"""
        if not self.send_email_function:
            self.logger.warning("No email function provided - cannot send notification")
            return
            
        try:
            # Get template from MongoDB based on action/category rather than store
            template_type = self._get_template_type(notification.category, notification.context.action)
            template = self._get_error_template(template_type, notification.severity.value, app_id)
            
            if not template:
                self.logger.warning(f"No template found for {template_type}, using fallback")
                self._send_fallback_error_notification(notification)
                return
            
            # Store current template for variable processing
            self.current_template = template
            print(notification.context)
            # Prepare attachments
            attachments = []
            if notification.context.screenshot_path and os.path.exists(notification.context.screenshot_path):
                attachments.append({
                    'path': notification.context.screenshot_path,
                    'filename': f"error_screenshot_{notification.error_id}.png"
                })

            # Generate email content using template
            subject_template = template.get('subject', 'Error Alert: {title}')
            subject = self._process_error_template_variables(subject_template, notification, {'app_id': app_id})
            
            html_content_template = template.get('html_content', '<html><body><p>Error notification content not available</p></body></html>')
            html_content = self._process_error_template_variables(html_content_template, notification, {'app_id': app_id})
            
            html_content = self._fix_template_css(html_content)
            
            # Send email using external function
            self.send_email_function(subject, html_content, attachments)
            self.logger.info(f"Error notification sent for {notification.error_id}")
            
            # Clean up
            self.current_template = None
            
        except Exception as e:
            self.logger.error(f"Failed to send immediate notification: {e}")
            self.current_template = None

    def _get_template_type(self, category: ErrorCategory, action: str) -> str:
        """Map error category and action to template type with action priority"""
        
        # Action-specific templates take absolute precedence
        action_templates = {
            'manual_trigger': 'login_error_alert',
            'reset_password': 'password_reset_error',
            'invoice_download': 'store_error_dynamic_alert',
            'download_new_vehicles_report': 'store_error_dynamic_alert',
            'download_honda_pricing_guide': 'store_error_dynamic_alert'
        }
        
        return action_templates.get(action, 'store_error_dynamic_alert')


    def send_session_summary(self, session_stats: Dict[str, Any], app_id: str = None):
        """Send intelligent session summary with error grouping"""
        if not self.session_errors or not self.send_email_function:
            self.logger.info("No session errors to report or no email function available")
            return
        
        try:
            # Group errors for better reporting
            error_groups = self._group_session_errors()
            
            # Only send summary if we have significant errors or critical issues
            significant_errors = [e for e in self.session_errors if e.severity in [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL]]
            total_unique_errors = len(error_groups)
            
            if not significant_errors and total_unique_errors < 3:
                self.logger.info("Session summary skipped - no significant errors")
                return
            
            # Get session summary template
            template = self._get_error_template('session_summary', app_id=None)
            
            if not template:
                self._send_fallback_session_summary(session_stats, error_groups)
                return
            
            # Process session data with error grouping
            session_data = self._process_session_data(session_stats, error_groups)
            
            # Generate email content
            subject_template = template.get('subject', 'Session Summary - {total_errors} Error(s)')
            subject = subject_template.format(**session_data)
            
            html_content_template = template.get('html_content', '<html><body><p>Session summary not available</p></body></html>')
            html_content = html_content_template.format(**session_data)
            
            html_content = self._fix_template_css(html_content)

            # Send summary email
            self.logger.info(html_content)  # Log content for debugging
            self.send_email_function(subject, html_content)
            self.logger.info("Session summary sent successfully")
                
        except Exception as e:
            self.logger.error(f"Failed to send session summary: {e}")

    def _group_session_errors(self) -> Dict[str, Dict]:
        """Group session errors by hash for better reporting"""
        groups = defaultdict(lambda: {
            'errors': [],
            'count': 0,
            'severity': ErrorSeverity.LOW,
            'affected_vins': set(),
            'first_occurrence': None,
            'last_occurrence': None
        })
        
        for error in self.session_errors:
            group = groups[error.error_hash]
            group['errors'].append(error)
            group['count'] += 1
            
            # Track highest severity
            if error.severity.value > group['severity'].value:
                group['severity'] = error.severity
            
            # Track affected VINs
            if error.context.vin:
                group['affected_vins'].add(error.context.vin)
            
            # Track timing
            if not group['first_occurrence'] or error.timestamp < group['first_occurrence']:
                group['first_occurrence'] = error.timestamp
            if not group['last_occurrence'] or error.timestamp > group['last_occurrence']:
                group['last_occurrence'] = error.timestamp
        
        # Convert sets to lists for JSON serialization
        for group in groups.values():
            group['affected_vins'] = list(group['affected_vins'])
        
        return dict(groups)

    def _process_session_data(self, session_stats: Dict[str, Any], error_groups: Dict) -> Dict[str, Any]:
        """Process session data with intelligent grouping"""
        error_by_severity = defaultdict(int)
        error_by_category = defaultdict(int)
        
        for group in error_groups.values():
            # Count by highest severity in group
            severity = group['severity'].value
            error_by_severity[severity] += group['count']
            
            # Count by category (use first error's category)
            if group['errors']:
                category = group['errors'][0].category.value
                error_by_category[category] += group['count']
        
        return {
            'total_errors': len(self.session_errors),
            'unique_error_types': len(error_groups),
            'total_vins_processed': len(self.processed_vins),
            'successful_downloads': session_stats.get('successful_downloads', 0),
            'session_timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC'),
            'error_by_severity_json': json.dumps(dict(error_by_severity), indent=2),
            'error_by_category_json': json.dumps(dict(error_by_category), indent=2),
            'error_groups_summary': self._format_error_groups_summary(error_groups),
            'session_stats_json': json.dumps(session_stats, indent=2)
        }

    def _format_error_groups_summary(self, error_groups: Dict) -> str:
        """Format error groups for email display"""
        summary_lines = []
        
        for error_hash, group in error_groups.items():
            first_error = group['errors'][0]
            summary_lines.append(
                f"• {first_error.category.value} ({group['severity'].value}): "
                f"{group['count']} occurrences, {len(group['affected_vins'])} VINs affected"
            )
        
        return "<br>".join(summary_lines)

    def _get_resolution_steps(self, category: ErrorCategory) -> List[str]:
        """Get suggested resolution steps based on error category"""
        resolution_map = {
            ErrorCategory.LOGIN_FAILURE: [
                "Verify credentials are correct and not expired",
                "Check if account is locked or requires password reset",
                "Validate MFA settings and email accessibility"
            ],
            ErrorCategory.DATA_NOT_FOUND: [
                "Verify VIN format and validity",
                "Check if VIN exists in the system",
                "Review search date ranges"
            ],
            ErrorCategory.SELENIUM_ERROR: [
                "Update Chrome and ChromeDriver versions",
                "Check for website layout changes",
                "Review element selectors"
            ],
            ErrorCategory.DATA_NOT_FOUND: [
                "Verify VIN format and validity",
                "Check search date ranges and filters",
                "Confirm data availability on the website",
                "Review search criteria and logic"
            ],
            ErrorCategory.FILE_PROCESSING_ERROR: [
                "Check file permissions and disk space",
                "Verify file format and integrity",
                "Review download timeout settings",
                "Check antivirus interference"
            ],
            ErrorCategory.S3_UPLOAD_ERROR: [
                "Verify AWS credentials and permissions",
                "Check S3 bucket existence and policies",
                "Review network connectivity to AWS",
                "Validate file size and format restrictions"
            ],
            ErrorCategory.MFA_FAILURE: [
                "Check email delivery and spam folders",
                "Verify email parsing logic for codes",
                "Review MFA timeout settings",
                "Confirm email account accessibility"
            ],
            ErrorCategory.TIMEOUT_ERROR: [
                "Increase timeout values for slow operations",
                "Check for website performance issues",
                "Review network latency and stability",
                "Implement retry mechanisms with backoff"
            ]
        }
        
        return resolution_map.get(category, [
            "Review error logs and stack trace",
            "Check system resources and dependencies",
            "Contact technical support if issue persists"
        ])

    def _send_fallback_error_notification(self, notification: ErrorNotification):
        """Send basic fallback error notification"""
        severity_colors = {
            ErrorSeverity.LOW: "#28a745",
            ErrorSeverity.MEDIUM: "#ffc107", 
            ErrorSeverity.HIGH: "#fd7e14",
            ErrorSeverity.CRITICAL: "#dc3545"
        }
        
        color = severity_colors.get(notification.severity, "#6c757d")
        subject = f"{notification.severity.value} Alert: {notification.title}"
        
        html_content = f"""
        <html>
        <body style="font-family: Arial, sans-serif; margin: 20px;">
            <div style="background-color: {color}; color: white; padding: 15px; border-radius: 5px;">
                <h2>Automation Error Alert</h2>
                <p><strong>Error ID:</strong> {notification.error_id}</p>
                <p><strong>Time:</strong> {notification.timestamp.strftime('%Y-%m-%d %H:%M:%S UTC')}</p>
            </div>
            
            <div style="margin: 20px 0;">
                <h3>Error Details</h3>
                <p><strong>Severity:</strong> {notification.severity.value}</p>
                <p><strong>Category:</strong> {notification.category.value}</p>
                <p><strong>Store:</strong> {notification.context.store}</p>
                <p><strong>VIN:</strong> {notification.context.vin or 'N/A'}</p>
                <p><strong>Description:</strong> {notification.description}</p>
                
                <h3>Suggested Actions</h3>
                <ol>
                    {"".join(f"<li>{step}</li>" for step in notification.resolution_steps)}
                </ol>
            </div>
        </body>
        </html>
        """
        
        self.send_email_function(subject, html_content)

    def _send_fallback_session_summary(self, session_stats: Dict[str, Any]):
        """Send basic fallback session summary"""
        subject = f"Session Summary - {len(self.session_errors)} Error(s)"
        
        html_content = f"""
        <html>
        <body style="font-family: Arial, sans-serif; margin: 20px;">
            <h2>Automation Session Summary</h2>
            <p><strong>Completed:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}</p>
            
            <div style="background-color: #f8f9fa; padding: 15px; margin: 15px 0;">
                <h3>Statistics</h3>
                <ul>
                    <li>Total VINs: {session_stats.get('total_vins', 0)}</li>
                    <li>Successful Downloads: {session_stats.get('successful_downloads', 0)}</li>
                    <li>Total Errors: {len(self.session_errors)}</li>
                </ul>
            </div>
            
            <h3>Error List</h3>
            <table border="1" cellpadding="8" style="border-collapse: collapse; width: 100%;">
                <tr>
                    <th>Time</th><th>Store</th><th>VIN</th><th>Category</th><th>Description</th>
                </tr>
                {"".join(f'''
                <tr>
                    <td>{error.timestamp.strftime('%H:%M:%S')}</td>
                    <td>{error.context.store}</td>
                    <td>{error.context.vin or 'N/A'}</td>
                    <td>{error.category.value}</td>
                    <td>{error.description[:100]}{'...' if len(error.description) > 100 else ''}</td>
                </tr>
                ''' for error in self.session_errors)}
            </table>
        </body>
        </html>
        """
        
        self.send_email_function(subject, html_content)

    def capture_browser_state(self, chrome_driver) -> Dict[str, Any]:
        """Capture comprehensive browser state for debugging"""
        try:
            state = {
                'current_url': chrome_driver.driver.current_url,
                'page_title': chrome_driver.driver.title,
                'window_handles': len(chrome_driver.driver.window_handles),
                'browser_logs': [],
                'console_errors': [],
                'network_errors': []
            }
            
            # Capture browser logs
            try:
                logs = chrome_driver.driver.get_log('browser')
                state['browser_logs'] = [
                    {'level': log['level'], 'message': log['message'], 'timestamp': log['timestamp']}
                    for log in logs[-10:]  # Last 10 logs
                ]
            except Exception:
                pass
            
            # Capture performance logs for network errors
            try:
                perf_logs = chrome_driver.driver.get_log('performance')
                for log in perf_logs[-20:]:  # Last 20 performance logs
                    message = json.loads(log['message'])
                    if 'Network' in message.get('message', {}).get('method', ''):
                        state['network_errors'].append(message)
            except Exception:
                pass
                
            return state
        except Exception as e:
            self.logger.warning(f"Failed to capture browser state: {e}")
            return {}

    def take_error_screenshot(self, chrome_driver, error_id: str) -> Optional[str]:
        """Take screenshot for error documentation"""
        try:
            screenshot_path = f"/tmp/error_screenshots/{error_id}.png"
            os.makedirs(os.path.dirname(screenshot_path), exist_ok=True)
            
            if chrome_driver.take_screenshot(screenshot_path):
                return screenshot_path
        except Exception as e:
            self.logger.warning(f"Failed to take error screenshot: {e}")
        return None

    def close(self):
        """Close MongoDB connection and cleanup resources"""
        try:
            if hasattr(self, 'mongo_client') and self.mongo_client is not None:
                self.mongo_client.close_connection()
                self.mongo_client = None
            self.logger.info("Error notification system closed successfully")
        except Exception as e:
            self.logger.warning(f"Error during notification system cleanup: {e}")