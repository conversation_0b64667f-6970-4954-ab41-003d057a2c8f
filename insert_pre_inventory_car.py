from Process_Utils.reynolds_app import <PERSON><PERSON><PERSON>, NoRecordsFoundException, ModelNotSelected, ColorNotFound, VinAlreadyStockedInPreInventory, FactoryAccessoriesTableNotEmpty, InvalidAccessoryCode, ModelSelectedNotFound, ErrorWhenRetrievingModelOptions, get_screenshot
from dotenv import load_dotenv
import traceback
from HEN_Utilities.aria_utils import ARIA
import time
from HEN_Utilities.utilities_handler import APIClient
from HEN_Utilities.cars_preprocessing import generate_input_dict_pre_inventory
import pyautogui
import base64
from datetime import datetime
import requests
import json

load_dotenv()

# API_ENDPOINT = "https://n26dxl5s43.execute-api.us-east-1.amazonaws.com/snd-hen/"
# API_TOKEN = "H9BPXBgmqEK95R2XqVjzJxNa27W3dk"
# SECRET = "snd-hen-reynolds_env_vars_snd"

API_ENDPOINT = "https://3aehxmmp2b.execute-api.us-east-1.amazonaws.com/prd-hen/"
API_TOKEN = "dwgMcKW9alRxy0HilyapDRlTIA8DCt"
SECRET = "prd-hen-reynolds_env_vars_prd"

def process_vehicles():    

    api_client = APIClient(
        api_endpoint=API_ENDPOINT,
        api_token=API_TOKEN
    )

    env_vars = api_client.get_secret(SECRET) 
    params = api_client.get_secret(env_vars['CREDENTIALS_SECRET_USER_3'])
    limit = env_vars.get('BATCH_SIZE_PRE_INVENTORY', 0)
    stage = "pre-inventory"

    uuid_completed_status = api_client.get_completed_status_uuid(env_vars["ARIA_INVOICE_APP_ID_PRE_INVENTORY"], env_vars["DB_NAME"])
    bre_response = {
        "aria_status":{
               "value": uuid_completed_status
            }
        }
    
    uuid_needs_status = api_client.get_needs_status_uuid(env_vars["ARIA_INVOICE_APP_ID_PRE_INVENTORY"], env_vars["DB_NAME"])

    aria = ARIA(env_vars["ARIA_BASE_URL"], env_vars["ARIA_TOKEN"], env_vars["ARIA_INVOICE_APP_ID_PRE_INVENTORY"])

    cars = []
    response = api_client.get_cars_to_stock_in_pre_inventory(env_vars["DB_NAME"], limit)
    if response is not None:
        cars = generate_input_dict_pre_inventory(response)

    reynolds_closed = False
    processed_vins = [car['VIN'] for car in cars]
    
    

    if cars and uuid_completed_status:

        reynolds = ReynoldsApp(api_client)
        reynolds_closed = True
         
        previous_store = ""
        actual_store = ""
        for index, new_car in enumerate(cars):
            
            try:
                print("VIN",new_car["vin"])
                aria.create_event(
                    item_id=new_car["aria_wi_id"],
                    title="The bot has started stocking.",
                    status=0
                )
                api_client.update_vin_in_mongo(env_vars["DB_NAME"], new_car["vin"],
                                                 {"flows.pre-inventory.updated_at": datetime.now().isoformat()}, {})
                
                actual_store = new_car["Brand"] 
                store = new_car["Store"] 

                if reynolds_closed == True:
                    reynolds.open_reynolds()
                    if index == 0:
                        reynolds.update_reynolds()                    
                    reynolds.login(params['user'],params['actual_password'])
                    reynolds_closed = False
                
                branch = reynolds.get_branch_by_store(store)
                reynolds.go_to_vms(actual_store,branch=branch)
                reynolds.insert_pre_inventory_vehicle(new_car)
                
                aria.bre_reply(new_car["aria_wi_id"], bre_response)
                time.sleep(2)
                aria.create_event(
                    item_id=new_car["aria_wi_id"],
                    title="This invoice has been stocked in",
                    status=0
                )
                api_client.update_vin_status_in_mongo(new_car["VIN"], stage, 11, env_vars["DB_NAME"], finished=True)
                api_client.update_invoice_vin_status(new_car["VIN"], env_vars["DB_NAME"], "pre-inventory")
            
            except ModelSelectedNotFound as e:
                reynolds.logger.log("INFO", f"Reynolds model field not found into Reynolds model options {new_car["VIN"]}: {traceback.format_exc()}")
                api_client.update_vin_status_in_mongo(new_car["VIN"], stage, 10, env_vars["DB_NAME"])   
                aria.exception_aria_reply(new_car["aria_wi_id"], uuid_needs_status, "Error when doing stock in: Reynolds model field not found into Reynolds model options", e.screenshot)
                
                reynolds.close_reynolds()
                reynolds_closed = True

            except ErrorWhenRetrievingModelOptions as e:
                reynolds.logger.log("INFO", f"Error when retrieving Reynolds model options {new_car["VIN"]}: {traceback.format_exc()}")
                api_client.update_vin_status_in_mongo(new_car["VIN"], stage, 9, env_vars["DB_NAME"])   
                aria.exception_aria_reply(new_car["aria_wi_id"], uuid_needs_status, "Error when doing stock in: Error when retrieving Reynolds model options", e.screenshot)
                
                reynolds.close_reynolds()
                reynolds_closed = True

            except VinAlreadyStockedInPreInventory as e:
                reynolds.logger.log("INFO", f"VIN already stocked in {new_car["VIN"]}: {traceback.format_exc()}")
                api_client.update_vin_status_in_mongo(new_car["VIN"], stage, 10, env_vars["DB_NAME"])   
                aria.exception_aria_reply(new_car["aria_wi_id"], uuid_needs_status, "Error when doing stock in: VIN already stocked in", e.screenshot)
                
                reynolds.close_reynolds()
                reynolds_closed = True

            except ModelNotSelected as e:
                reynolds.logger.log("INFO", f"Model not selected for VIN {new_car["VIN"]}: {traceback.format_exc()}")
                api_client.update_vin_status_in_mongo(new_car["VIN"], stage, 10, env_vars["DB_NAME"])   
                aria.exception_aria_reply(new_car["aria_wi_id"], uuid_needs_status, "Error when doing stock in: Model not selected for VIN", e.screenshot)
                
                reynolds.close_reynolds()
                reynolds_closed = True

            except ColorNotFound as e:
                reynolds.logger.log("INFO", f"Extracted color not found {new_car["VIN"]}: {traceback.format_exc()}")
                api_client.update_vin_status_in_mongo(new_car["VIN"], stage, 10, env_vars["DB_NAME"])   
                aria.exception_aria_reply(new_car["aria_wi_id"], uuid_needs_status, "Error when doing stock in: Extracted color not found", e.screenshot)
                
                reynolds.close_reynolds()
                reynolds_closed = True

            except NoRecordsFoundException as e:
                reynolds.logger.log("INFO", f"VIN not exists when searching in VMS {new_car["VIN"]}: {traceback.format_exc()}")
                api_client.update_vin_status_in_mongo(new_car["VIN"], stage, 10, env_vars["DB_NAME"])   
                aria.exception_aria_reply(new_car["aria_wi_id"], uuid_needs_status, "Error when doing stock in: VIN not exists when searching in VMS", e.screenshot)
                
                reynolds.close_reynolds()
                reynolds_closed = True

            except FactoryAccessoriesTableNotEmpty as e:
                reynolds.logger.log("INFO", f"Factory accessories table already filled {new_car["VIN"]}: {traceback.format_exc()}")
                api_client.update_vin_status_in_mongo(new_car["VIN"], stage, 10, env_vars["DB_NAME"])   
                aria.exception_aria_reply(new_car["aria_wi_id"], uuid_needs_status, "Error when doing stock in: Factory accessories table already filled", e.screenshot)
                
                reynolds.close_reynolds()
                reynolds_closed = True

            except InvalidAccessoryCode as e:
                reynolds.logger.log("INFO", f"Invalid accesory code for model {new_car["VIN"]}: {traceback.format_exc()}")
                api_client.update_vin_status_in_mongo(new_car["VIN"], stage, 10, env_vars["DB_NAME"])   
                aria.exception_aria_reply(new_car["aria_wi_id"], uuid_needs_status, "Error when doing stock in: Invalid accesory code for model", e.screenshot)
                
                reynolds.close_reynolds()
                reynolds_closed = True
            
            except Exception as e:
                print(f"Error when doing the stock in of {new_car["VIN"]}: ", traceback.format_exc())
                reynolds.logger.log("INFO", f"Error when doing the stock in of {new_car["VIN"]}: {traceback.format_exc()}")
                api_client.update_vin_status_in_mongo(new_car["VIN"], stage, 10, env_vars["DB_NAME"])
                screenshot = get_screenshot()
                aria.exception_aria_reply(new_car["aria_wi_id"], uuid_needs_status, "Error when doing stock in", screenshot)

                reynolds.close_reynolds()
                reynolds_closed = True
                actual_store = ""

            finally:
                previous_store = actual_store

        reynolds.close_reynolds()
        reynolds.invoke_rpa.close()

    return processed_vins


if __name__ == "__main__":
    processed_vins = process_vehicles()
    
    if processed_vins:
        headers = {
            'Authorization': f'Bearer {API_TOKEN}',
        }
        body = json.dumps({"vins": processed_vins, "type": "stock_in", "stage": "pre-inventory"})
        response = requests.post(API_ENDPOINT + "report_to_aria", json=body, headers=headers)
