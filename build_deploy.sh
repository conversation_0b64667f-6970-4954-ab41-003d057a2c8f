# Define environment variables
export IMAGE_NAME=pyautomationaws/selenium
#export IMAGE_NAME=test_honda/automation
export AWS_REGION=us-east-1
#export AWS_ACCOUNT_ID=************
export AWS_ACCOUNT_ID=************
export ECR_REPO=$AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$IMAGE_NAME
export LAMBDA_NAME=prd-hen-selenium_downloader

# Clean up any leftover apt files
rm -rf /var/lib/apt/lists/* /var/cache/apt/*

echo "Configuring AWS credentials..." &&
export AWS_ACCESS_KEY_ID="$AWS_ACCESS_KEY_ID" &&
export AWS_SECRET_ACCESS_KEY="$AWS_SECRET_ACCESS_KEY" &&
export AWS_SESSION_TOKEN="$AWS_SESSION_TOKEN" &&

echo "Logging into AWS ECR..." &&
aws ecr describe-repositories --repository-names $IMAGE_NAME --region $AWS_REGION || \
aws ecr create-repository --repository-name $IMAGE_NAME --region $AWS_REGION &&
echo "Cleaning up old Docker images..." &&
# TODO

aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $ECR_REPO &&

echo "Building Docker image..." &&
docker buildx build --platform linux/amd64 --provenance=false -t $IMAGE_NAME .
docker tag $IMAGE_NAME $ECR_REPO:latest &&

echo "Pushing image to AWS ECR..." &&
docker push $ECR_REPO:latest &&

echo "Checking if Lambda function exists..." &&
if aws lambda get-function --function-name $LAMBDA_NAME --region $AWS_REGION; then
    echo "Updating existing Lambda function..." &&
    aws lambda update-function-code --function-name $LAMBDA_NAME --image-uri $ECR_REPO:latest --architectures x86_64
else
    echo "Creating new Lambda function..." &&
    aws lambda create-function --function-name $LAMBDA_NAME \
        --package-type Image \
        --code ImageUri=$ECR_REPO:latest \
        --role arn:aws:iam::${AWS_ACCOUNT_ID}:role/lamda_admin \
        --region $AWS_REGION \
        --memory-size 1024 \
        --timeout 600 
        
fi

echo "Done!"