import os
import sys
import time
import json
import requests

from datetime               import datetime, timedelta

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
import invokeRPA
from Invoices.downloader import Downloader
from Invoices.config     import DefaultConfig

class MANHEIM(Downloader):
    def __init__(self, store, stage, action):
        super().__init__(store, stage, action)
        self.chrome.navigate_to_url('https://home.manheim.com')
        # Initialize VIN variable
        self.vin = ''
        self.label = 'manheim'
        self._init_outlook(stage)

    def login(self, user, password, retry=True):
        """
        Login to the Porsche portal.
        1. Enter credentials
        2. Complete MFA
        """
        self.stage = 'LOGIN'
        self.user = user
        self.password = password
        try:
            self.chrome.get_element('ID','user_username')
            # Login
            self.chrome.type_value('ID','user_username', user)
            self.chrome.type_value('ID','user_password', password, password=True)

            self.chrome.click_button('ID','submit')
            self._log_info('Credentials entered, starting MFA')

            # Select doing MFA using email
            self.chrome.click_button('XPATH', '/html/body/div/div/div[1]/div/div/div[2]/div[2]/div/div[1]')
            
            #Waiting for verification page
            self.chrome.get_element("XPATH", '//*[@id="passcode"]')


            # Wait for MFA email
            self._log_info('Waiting for MFA email...')
            self.utilities.sleep(2)
            mfa_codes = self.__get_mfa()
            
            if not mfa_codes:
                raise Exception("MFA email not received within the timeout period.")
            
            self._log_info(f'MFA codes received: {mfa_codes}')

            for mfa_code in mfa_codes:
                self._log_info(f'Entering MFA code: {mfa_code["code"]}')
                self.chrome.type_value('XPATH', '//*[@id="passcode"]', mfa_code['code'])
                self.chrome.click_button('ID', 'sign-on')
                self.utilities.sleep(4)


                # Was redirected?
                if self.chrome.get_element("XPATH", '//a[contains(text(), "Your Manheim Account")]') != False:
                    self.outlook.delete_email(mfa_code['id'])
                    self.utilities.sleep(3)
                    self._log_info('MFA completed')
                    self._log_info('Succeded!')
                    return
                
                self._log_error(f'MFA code {mfa_code["code"]} did not work for this user')

            raise Exception('Could not complete MFA')
        except Exception as e:
            self._handle_error(e)

    def __get_mfa(self):
        """
        Get PingID code for MFA from email

        Returns:
            str: The MFA codes.

        Raises:
            Exception: If no appropriate MFA email is received within the timeout period.
        """
        timeout = 15
        # Store all codes received, delete only the one that works
        mfa_codes = []
        ids = []
        poll_interval = 2
        start_time = time.time()

        while time.time() - start_time < timeout:
            # Use strftime to get a datetime string without microseconds.
            min_time = (datetime.utcnow() - timedelta(minutes=1)).strftime("%Y-%m-%dT%H:%M:%SZ")
            # Email has subject 'New Authentication Request from PPN PROD'
            filter_str = (
                f"receivedDateTime ge {min_time} and "
                f"subject eq 'Manheim OTP Verification'"
            )
            for e_id in ids:
                filter_str += f" and internetMessageId ne '{e_id}'"

            emails = self.outlook.get_emails_from_folder("Inbox", top_fetch=1, filter_value=filter_str)
            if emails:
                for e in emails:
                    # Retrieve code frome email body
                    content = e.get("body", {}).get("content", "")
                    try:
                        mfa_code = content.split("</div>")[-1].split(" ")[0]
                        self._log_info("Code found in email")
                        mfa_codes.append({'id': e['internetMessageId'], 'code': mfa_code})
                        ids.append(e['internetMessageId'])
                    except Exception:
                        self._log_error("Code not found in email with id: " + e['internetMessageId'])

            time.sleep(poll_interval)
        
        return mfa_codes
        

    def download_vins(self, vins):
        """
        Download invoices
        """
        self.navigate_to_search_menu()
        return super().download_vins(vins)

    def navigate_to_search_menu(self, retry=True):
        """Navigate to the search menu"""
        self.stage = 'SEARCH_MENU'
        try:
            self._log_info('Start')
            # Go to PVMS America
            self.chrome.navigate_to_url('https://account.manheim.com/customer/#/invoices/purchases/all/5536647')
            self.utilities.sleep(2)
            self.chrome.get_element("XPATH", '//h1[text()="Purchases"]')

            self._log_info('Accessed to the download vins site')
            self._log_info('Success')

        except Exception as e:
            self._handle_error(e)

    def search_by_vin(self, vin, count):
        """Search invoice"""
        self.stage = 'SEARCH_VIN'
        try:
            self.vin = vin
            if count > 1:
                self.navigate_to_search_menu()
                self._log_info('Back to search menu')
                
            # Select last 90 days
            self.chrome.select_option_by_value('XPATH', '//*[@id="cp-app-root"]/div/div/div[5]/div/div/div[1]/div[2]/div[2]/div/form/div[1]/div[1]/div/select', 'LAST_90_DAYS')
            
            # Search in all stores
            self.chrome.get_element("ID", 'searchAcrossAllAccounts')
            self.chrome.click_button("ID", 'searchAcrossAllAccounts')
                
            #Type VIN
            self.chrome.type_value("XPATH", '//input[@placeholder="Enter at least 6 characters"]', vin)

            self.chrome.click_button("XPATH", '//*[@id="cp-app-root"]/div/div/div[5]/div/div/div[1]/div[2]/div[1]/form/div[2]/div[2]/button')


            not_exists_vehicle = self.chrome.get_element("XPATH", "//span[contains(text(), 'No results found')]")

            if not_exists_vehicle != False:
                self._log_error(f'VIN: {vin} not found')
                return False
            
            self._log_info(f'{vin} found')
            return True
        
        except Exception as e:
            self._handle_error(e)

    def save_invoice(self):
        """Download invoice"""
        self.stage = 'SAVE_INVOICE'
        try:
            self._log_info('Start')

            # Click on vehicle
            self.chrome.click_button('XPATH', '//a[contains(@href, "/customer/#/purchases/vehicledetail/purchase?")]')
            
            documents = self.chrome.get_element("XPATH", '//button[contains(text(), "Sale Documents")]')
            if documents == False:
                raise Exception('Cant access to documents tab')
            
            # Click on Sale documents 
            self.chrome.click_button("XPATH", '//button[contains(text(), "Sale Documents")]')

            # Click on Invoice
            self.chrome.click_button("XPATH", '//input[contains(@id, "invoices_")]')

            # Click on View PDF
            self.chrome.click_button("XPATH", '//button[contains(text(), "View")]/span')

            # Wait for the invoice to download
            self._log_info('Download started')
            today_path = self.utilities.get_today('%Y/%m/%d')
            self.get_file_from_download(extension=".pdf", name=self.vin, s3_path=f"invoices_used_cars/{today_path}/", stage='SAVE_INVOICE')

            self._log_info('Success')

        except Exception as e:
            self._handle_error(e)

    def download_new_vehicle_report(self, store):
        """Navigate to the search menu"""

        self.navigate_to_search_menu()

        self.stage = 'SEARCH_REPORT'

        self.chrome.get_element('ID', 'Vehicle2_VehSearch_MODYEAR')


        try:
            # Type Years to look for models
            years = [str(datetime.now().year - 1), str(datetime.now().year), str(datetime.now().year + 1)]
            self.chrome.type_value('ID', 'Vehicle2_VehSearch_MODYEAR', ",".join(years))
            

            # Type Vehicle Status
            self.chrome.type_value('ID', 'Vehicle2_VehSearch_MMSTA', "v300")


            self.chrome.click_button('XPATH', '//a[@onclick="js.search()"]')
            self.utilities.sleep(2) 

            default_time_out = self.chrome.timeout
            self.chrome.timeout = 120
            # Assert 1 element is found
            table = self.chrome.get_element('XPATH', ".//tbody[@id='Vehicle2_VehList_result_tbody_3']")
            if not table:
                self._log_error(f'Not founds vins to download new vehicles report')
                return False
        
            self.chrome.timeout = default_time_out

            self.chrome.click_button('NAME', 'Vehicle2_VehList_result-sel')
            time.sleep(30)

            self.chrome.click_button('XPATH', '//a[@onclick="js.exportData()"]')

            today_path = self.utilities.get_today('%Y/%m/%d')
            self.get_file_from_download(extension=".xls", name=f"new_vehicles_{store}", s3_path=f"new_vehicles_reports/{today_path}/", stage='SAVE_REPORT')

        except Exception:
            self._log_error("Error when downloading the report")
            return False

        self._log_info('Success')

        return True