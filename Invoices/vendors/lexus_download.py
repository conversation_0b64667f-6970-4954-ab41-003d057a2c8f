import os
import re
import sys
import time
from datetime import datetime, timedelta

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))
from Invoices.downloader import Downloader

# Make sure the Outlook class (from the refactored module above) is available.
# For example, if saved in outlook.py:
# from outlook import Outlook

class LEXUS(Downloader):
    def __init__(self, store, current_flow, action):
        """
        Initialize the LEXUS automation class.

        Parameters:
            email_client_id (str): Client ID for the Outlook API.
            email_client_secret (str): Client secret for the Outlook API.
            email_tenant_id (str): Tenant ID for the Outlook API.
            email_user_id (str): User ID (or email address) for mailbox operations.
        """
        # Enable logging and launch the browser automation.
        super().__init__(store, current_flow, action)
        self._init_outlook()
        self.vin = ''
        self.chrome.navigate_to_url('https://dealer.toyota.com/')

        self.label = 'lexus'

    def __get_mfa(self):
        """
        Polls the Outlook mailbox for an MFA <NAME_EMAIL> received in the last minute.
        When found, extracts a 4- to 6-digit MFA code from the email body.

        Returns:
            str: The MFA code.

        Raises:
            Exception: If no appropriate MFA email is received within the timeout period.
        """
        timeout = 30  # Total timeout in seconds
        poll_interval = 5  # Seconds between polling attempts
        start_time = time.time()
        mfa_codes = []
        ids = []

        while time.time() - start_time < timeout:
            # Use strftime to get a datetime string without microseconds.
            min_time = (datetime.utcnow() - timedelta(minutes=1)).strftime("%Y-%m-%dT%H:%M:%SZ")
            filter_str = (
                f"from/emailAddress/address eq '<EMAIL>' and "
                f"receivedDateTime ge {min_time}"
            )
            for e_id in ids:
                filter_str += f" and internetMessageId ne '{e_id}'"
            emails = self.outlook.get_emails_from_folder("Inbox", top_fetch=1, filter_value=filter_str)
            if emails:
                for e in emails:
                    content = e.get("body", {}).get("content", "")
                    match = re.search(r"\b(\d{4,6})\b", content)
                    if match:
                        mfa_code = match.group(1)
                        self.logger.log(f"MFA code found: {mfa_code}")
                        mfa_codes.append({'id': e['internetMessageId'], 'code': mfa_code})
                        ids.append(e['internetMessageId'])
                    else:
                        self._log_error("Code not found in email with id: " + e['internetMessageId'])

            time.sleep(poll_interval)

        return mfa_codes

    def login(self, user, password, trys=3):
        """
        Performs the Toyota login process including MFA.

        Parameters:
            user (str): The username/email for the Toyota site.
            password (str): The password for the Toyota site.

        Raises:
            Exception: If any step in the login process fails.
        """
        self.stage = 'LOGIN'
        try:
            # Enter user credentials.
            self._log_info('Signing in...')
            self.chrome.type_value('ID', 'idToken1', user)
            self.chrome.type_value('ID', 'idToken2', password, password=True)
            self.chrome.click_button('ID', 'tandC')
            self.chrome.click_button('ID', 'loginButton_0')
            self.chrome.get_element('XPATH', "//label[text()='Choose a validation method']")
            self.chrome.click_button('ID', 'loginButton_0')
            self._log_info('Credentials entered, waiting for MFA')
            
            mfa_codes = self.__get_mfa()

            if not mfa_codes:
                raise Exception("MFA email not received within the timeout period.")

            self._log_info(f'MFA codes received: {mfa_codes}')
            print(f'MFA codes received: {mfa_codes}')
            for mfa_code in mfa_codes:
                self._log_info(f'Entering MFA code: {mfa_code["code"]}')
                self.chrome.type_value('ID', 'idToken1', mfa_code['code'])
                self.chrome.click_button('ID', 'loginButton_0')
                self.utilities.sleep(5)

                # Was redirected?
                if self.chrome.get_element('XPATH', "//div[@name='module-name-display' and text()='Dashboard']") != False:
                    self.outlook.delete_email(mfa_code['id'])
                    self.utilities.sleep(1)
                    self._log_info('MFA completed')
                    self._log_info('Succeded!')
                    print('MFA completed')
                    print('Succeded!')
                    return
                
                # else:
                #     self.chrome.refresh_page()
                #     self.utilities.sleep(5)
                #     self._log_info('Retrying navigation...')
                #     navigated = self.chrome.click_button('XPATH', "//div[@name='module-name-display' and text()='Dashboard']")
                #     if navigated:
                #         self.outlook.delete_email(mfa_code['id'])
                #         self._log_info('MFA completed')
                #         self._log_info('Succeded!')
                #         print('MFA completed')
                #         print('Succeded!')
                #         return
                
                if self.chrome.get_element('XPATH', "//*[@id='content']/div/a") != False:
                    self.chrome.click_button('XPATH', "//*[@id='content']/div/a")

                # Check if we are in the login page again
                if self.chrome.get_element('XPATH', "//*[@id='idToken2']") != False and trys > 0:
                    self.login(user, password, mfa_codes, trys-1)
                    
                self._log_error(f'MFA code {mfa_code["code"]} did not work for this user')
                print(f'MFA code {mfa_code["code"]} did not work for this user')

            raise Exception('Could not complete MFA')
        except Exception as e:
            self._handle_error(e)

    def download_vins(self, vins):
        self.navigate_to_vin()
        return super().download_vins(vins)

    def navigate_to_vin(self):
        """Navigate to the invoice download section."""
        self.stage = 'NAVIGATE_TO_VIN'
        try:
            self._log_info('Navigating to VIN table...')
            for _ in range(2):
                self.utilities.sleep(2)  # Allow for loading
                self.chrome.get_element('XPATH', "//div[@name='module-name-display' and text()='Dashboard']")
                self.chrome.click_button('XPATH', "//div[@name='module-name-display' and text()='Dashboard']")
                self.utilities.sleep(1)
                self.chrome.click_button('XPATH', "//span[@class='ng-star-inserted' and text()='Office']")
                self.utilities.sleep(1)
                self.chrome.click_button('XPATH', "//a[text()='Vehicle Invoice/Credit Publication']")
                self._log_info('Opened Vehicle Invoice/Credit Publication')
                self.utilities.sleep(2)  # Allow for loading
            self.chrome.switch_to_next_tab()
            self.utilities.sleep(2)  # Allow for loading
            self._log_info('Navigated to VIN table')
        except Exception as e:
            self._handle_error(e)

    def search_by_vin(self, vin, count=None):
        """Searches for an invoice by VIN."""
        self.stage = 'SEARCH_INVOICE'
        try:
            self._log_info(f"Searching for VIN...")
            self.vin = vin
            self.chrome.get_element('ID', 'gs_VIN')
            self.utilities.sleep(1)  # Allow for loading
            self.chrome.type_value('ID', 'gs_VIN', vin)
            self._log_info('Entered information, searching...')
            self.chrome.send_keys('ID', 'gs_VIN', 'ENTER')

            # Assert 1 element is found
            self.utilities.sleep(1)  # Allow for loading
            table = self.chrome.get_element('ID', "dd_VehicleInvoice_jqTable")
            rows = self.chrome.get_table_elements(table, 'XPATH', './/tbody/tr[not(@class="jqgfirstrow")]')

            if len(rows) == 0:
                self._log_error(f'VIN: {vin} not found')
                return False
            if len(rows) > 1:
                self._log_error(f'VIN: {vin} found more than one invoice')
                return False

            self._log_info('VIN found!')
            self.chrome.click_button('ID', 'cb_dd_VehicleInvoice_jqTable')

            self._log_info('Downloading invoice from website...')
            self.chrome.click_button('ID', 'viewInvoices')
            return True
        except Exception as e:
            self._handle_error(e)
            return False

    def save_invoice(self):
        """Downloads and renames the invoice based on the VIN."""
        self.stage = 'SAVE_INVOICE'
        try:
            self.utilities.sleep(4)  # Allow navigation delay
            self.get_file_from_download()
        except Exception as e:
            self._handle_error(e)


    def reset_password(self, user, actual_password, new_password):
        """Reset password with basic validation and proper delays."""
        try:
            self.stage = 'CHANGE_PASSWORD'
            self.chrome.navigate_to_url("https://dealer.toyota.com/dashboard/universal/overview")
            self.utilities.sleep(10)
            
            # Click user profile
            self.chrome.click_button('XPATH', '/html/body/dd365-platform-shell-root/dd-shell-navbar/div/dd-shell-user-profile/div[1]/ul/li[1]')
            self.utilities.sleep(2)
            
            # Click profile settings
            self.chrome.click_button('XPATH', '//*[@id="mat-menu-panel-0"]/div/button[1]')
            self.utilities.sleep(5)

            # Check if password fields exist
            if self.chrome.get_element('XPATH', '//*[@id="mat-input-3"]') == False:
                raise Exception("Password fields not found")
            
            # Fill password fields
            self.chrome.type_value('XPATH', '//*[@id="mat-input-1"]', actual_password, password=True)
            self.utilities.sleep(1)
            self.chrome.type_value('XPATH', '//*[@id="mat-input-2"]', new_password, password=True)
            self.utilities.sleep(1)
            self.chrome.type_value('XPATH', '//*[@id="mat-input-3"]', new_password, password=True)
            self.utilities.sleep(1)

            # Find and click save button
            save_selectors = [
                '//*[@id="mat-mdc-dialog-1"]/div/div/dd-shell-my-profile/div/mat-sidenav-container/mat-sidenav-content/dd-shell-security/div/div/div[2]/fieldset/form/div/div[6]/button',
                '//button[contains(text(), "Save")]',
                '//button[@type="submit"]'
            ]
            
            for selector in save_selectors:
                if self.chrome.get_element('XPATH', selector) != False:
                    self.chrome.click_button('XPATH', selector)
                    self.utilities.sleep(3)
                    return True
            raise Exception("Save button not found")
            
        except Exception as e:
            self._log_error(f"Password reset failed: {str(e)}")
            return False