import os
import sys
import time
import json
import requests
from PyPDF2 import PdfMerger

from datetime               import datetime, timedelta

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
import invokeRPA
from Invoices.downloader import Downloader
from Invoices.config     import DefaultConfig

class PORSCHE(Downloader):
    def __init__(self, store, current_flow, action):
        super().__init__(store, current_flow, action)
        self.chrome.navigate_to_url('https://ppn.porsche.com/')
        # Initialize VIN variable
        self.vin = ''
        self.label = 'porsche'
        self._init_outlook()

    def login(self, user, password, retry=True):
        """
        Login to the Porsche portal.
        1. Enter credentials
        2. Complete MFA
        """
        self.stage = 'LOGIN'
        self.user = user
        self.password = password
        try:
            self.utilities.sleep(2)

            # Login
            self.chrome.type_value('ID','username', user)
            self.chrome.type_value('ID','password', password, password=True)
            self.chrome.click_button('ID','signOnButton')
            self._log_info('Credentials entered, starting MFA')

            # <PERSON> forgot your device
            self.chrome.click_button('XPATH', '//a[contains(text(), "Forgot your device?")]')
            self.utilities.sleep(2)

            # Click on Next
            self.chrome.click_button('ID', 'NextButton')

            # Wait for MFA email
            self._log_info('Waiting for MFA email...')
            self.utilities.sleep(2)
            mfa_codes = self.__get_mfa()
            
            if not mfa_codes:
                raise Exception("MFA email not received within the timeout period.")
            
            self._log_info(f'MFA codes received: {mfa_codes}')

            current_url = self.chrome.driver.current_url
            for mfa_code in mfa_codes:
                self._log_info(f'Entering MFA code: {mfa_code["code"]}')
                self.chrome.type_value('ID', 'otp', mfa_code['code'])
                self.chrome.click_button('XPATH', '//input[@value="Sign On"]')
                self.utilities.sleep(5)

                # Was redirected?
                redirected_url = self.chrome.driver.current_url
                if redirected_url not in (current_url, current_url + '/finalize'):
                    self.outlook.delete_email(mfa_code['id'])
                    self.utilities.sleep(2)
                    self._log_info('MFA completed')
                    self._log_info('Succeded!')
                    return
                
                self._log_error(f'MFA code {mfa_code["code"]} did not work for this user')

            raise Exception('Could not complete MFA')
        except Exception as e:
            self._handle_error(e)

    def __get_mfa(self):
        """
        Get PingID code for MFA from email

        Returns:
            str: The MFA codes.

        Raises:
            Exception: If no appropriate MFA email is received within the timeout period.
        """
        timeout = 50
        # Store all codes received, delete only the one that works
        mfa_codes = []
        ids = []
        poll_interval = 2
        start_time = time.time()

        while time.time() - start_time < timeout:
            # Use strftime to get a datetime string without microseconds.
            min_time = (datetime.utcnow() - timedelta(minutes=1)).strftime("%Y-%m-%dT%H:%M:%SZ")
            # Email has subject 'New Authentication Request from PPN PROD'
            filter_str = (
                f"receivedDateTime ge {min_time} and "
                f"subject eq 'New Authentication Request from PPN PROD'"
            )
            for e_id in ids:
                filter_str += f" and internetMessageId ne '{e_id}'"

            emails = self.outlook.get_emails_from_folder("Inbox", top_fetch=1, filter_value=filter_str)
            if emails:
                for e in emails:
                    # Retrieve code frome email body
                    content = e.get("body", {}).get("content", "")
                    try:
                        mfa_code = content.split("<b>")[1].split("</b>")[0]
                        self._log_info("Code found in email")
                        mfa_codes.append({'id': e['internetMessageId'], 'code': mfa_code})
                        ids.append(e['internetMessageId'])
                    except Exception:
                        self._log_error("Code not found in email with id: " + e['internetMessageId'])

            time.sleep(poll_interval)
        
        return mfa_codes
        
    def download_vins_post_inventory(self, vins):
        self.navigate_to_search_menu()

        count = 1
        self.logger.log('Downloading invoices...')
        for v in vins:
            self.tracker[v] = {'error': None, 'stored': False, 'stage': None}
            try:

                if count > 1:
                    self.chrome.click_button('XPATH', '//*[@id="Header_Menu"]//a[text()="Search / Marketplace"]')

                count += 1

                self.logger.log(f'Downlading invoice for VIN: {v}')
                found = self.search_by_vin_retail_price(v, count)
                if not found:
                    self.tracker[v]['error'] = 'Build sheet not found'
                    self.tracker[v]['stage'] = self.stage

                    not_found = self.chrome.get_element("XPATH", '//*[@id="js_m_cb"]/a/img')
                    if not_found != False:
                        self.chrome.click_button("XPATH", '//*[@id="js_m_cb"]/a/img')
                        
                    self.chrome.switch_frame(name='diwi')
                    self.chrome.click_button("XPATH", '//a[contains(text(), "Home")]')
                    continue
                # Only increment count if the search was successful

                commision_number = self.chrome.get_element("XPATH", "//td[@style='width:100px']/div[@class='listspan']").text
                self.save_retail_price()

                #self.navigate_to_search_menu()
                self.chrome.switch_frame(name='diwi')
                self.chrome.click_button("XPATH", '//a[contains(text(), "Home")]')
                time.sleep(2)

                self.chrome.click_button("XPATH", '//*[@id="Header_Menu"]/div[3]/a')
                self.chrome.click_button('XPATH', '//*[@id="Header_Menu"]/div/div/a[text()="Dealer Documents"]')

                found_commision = self.search_invoice_by_commision(commision_number)
                if not found_commision:
                    self.tracker[v]['error'] = 'Invoice not found'
                    self.tracker[v]['stage'] = self.stage

                    not_found = self.chrome.get_element("XPATH", '//*[@id="js_m_cb"]/a/img')
                    if not_found != False:
                        self.chrome.click_button("XPATH", '//*[@id="js_m_cb"]/a/img')
                        
                    self.chrome.switch_frame(name='diwi')
                    self.chrome.click_button("XPATH", '//a[contains(text(), "Home")]')                    
                    continue

                self.save_invoice()

                self.chrome.switch_frame(name='diwi')
                self.chrome.click_button("XPATH", '//a[contains(text(), "Home")]')
                time.sleep(2)
  
                merger = PdfMerger()
                merger.append(f"{self.destination_path}/{self.vin}.pdf")
                merger.append(f"{self.destination_path}/{self.commision_number}.pdf")
                merger.write(f"{self.destination_path}/{self.vin}.pdf")
                merger.close()



                try:
                    new_file_path = os.path.join(self.destination_path, f"{self.vin}.pdf")
                    self.upload_to_s3(new_file_path, filename=self.vin, extension=".pdf")
                    self.stored = True
                except Exception as e:
                    self._log_error(f'Error uploading to S3: {e}')
                    self.stored = False
                

                if self.stored:
                    self.tracker[v]['stored'] = self.stored
                else:
                    self.tracker[v]['error'] = 'Failed to store invoice'
                    self.tracker[v]['stage'] = self.stage
            except Exception as e:
                self.logger.error(f'Download invoices failed for VIN {v}: {str(e)}')
                self.tracker[v]['error'] = str(e)
                self.tracker[v]['stage'] = self.stage
            
            self.chrome.switch_frame(name='diwi')
            self.chrome.click_button("XPATH", '//a[contains(text(), "Home")]')

        try:
            del self.chrome
        except Exception as e:
            self.logger.error(f'Failed to close browser: {str(e)}')

        return self.tracker
    
    def download_vins_pre_inventory(self, vins):
        self.navigate_to_search_menu()

        count = 1
        self.logger.log('Downloading invoices...')
        for v in vins:
            self.tracker[v] = {'error': None, 'stored': False, 'stage': None}
            try:
                self.logger.log(f'Downlading invoice for VIN: {v}')

                if count > 1:
                    # Go back to search menu
                    success_navigation = self.chrome.click_button('XPATH', '//a[@onclick="js.back()"]')
                    if not success_navigation:
                        # This error occurs from time to time; refresh the page and try again
                        self._log_info('An error occurred while trying to go back to search menu. Refreshing page...')
                        self.chrome.refresh_page()
                        self.utilities.sleep(3)
                        self.chrome.switch_frame(name='diwi')
                        success_navigation = self.chrome.click_button('XPATH', '//*[@id="Header_Menu"]//a[text()="Search / Marketplace"]')
                    if not success_navigation:
                        raise Exception('Could not navigate back to search menu')
                    self.utilities.sleep(1)
                    self._log_info('Back to search menu')


                found = self.search_by_vin_retail_price(v, count)
                # Only increment count if the search was successful
                count += 1

                if not found:
                    self.tracker[v]['error'] = 'VIN not found'
                    self.tracker[v]['stage'] = self.stage
                    continue
                
                self.save_retail_price(build_sheet_wholesale=True)
                if self.stored:
                    self.tracker[v]['stored'] = self.stored
                else:
                    self.tracker[v]['error'] = 'Failed to store invoice'
                    self.tracker[v]['stage'] = self.stage
            except Exception as e:
                self.logger.error(f'Download invoices failed for VIN {v}: {str(e)}')
                self.tracker[v]['error'] = str(e)
                self.tracker[v]['stage'] = self.stage

        try:
            del self.chrome
        except Exception as e:
            self.logger.error(f'Failed to close browser: {str(e)}')

        return self.tracker
    
    def download_vins(self, vins):
        """
        Download invoices
        """
        if self.current_flow == "post-inventory":
            return self.download_vins_post_inventory(vins)
        elif self.current_flow == "pre-inventory":
            return self.download_vins_pre_inventory(vins)


    def navigate_to_search_menu(self, retry=True):
        """Navigate to the search menu"""
        self.stage = 'SEARCH_MENU'
        try:
            self._log_info('Start')
            # Go to PVMS America
            self.chrome.navigate_to_url('https://ppn.porsche.com/ppnmda/appforward.do?cn=PVMS_North_America_P17')
            self.utilities.sleep(5)
            if not self.chrome.switch_frame(name='diwi'):
                if retry:
                    self._log_info('Could not switch to iframe - Login failed. Retrying...')
                    self.chrome.navigate_to_url('https://ppn.porsche.com/')
                    self.login(self.user, self.password, retry=False)
                    self.navigate_to_search_menu(retry=False)
                else:
                    raise Exception('Could not switch to iframe - Login failed')
            self._log_info('PVMS America Window attached')
            # Wait for body to be loaded
            self.utilities.sleep(1)
            # Click on Search / Marketplace
            self._log_info('Clicking on Search / Marketplace')
            self.chrome.click_button('XPATH', '//*[@id="Header_Menu"]//a[text()="Search / Marketplace"]')
            self._log_info('Success')

        except Exception as e:
            self._handle_error(e)


    def search_by_vin_retail_price(self, vin, count):
        """Search invoice"""
        self.stage = 'SEARCH_VIN'
        try:
            self.vin = vin
                
            # Type VIN
            self.chrome.type_value('ID', 'Vehicle2_VehSearch_VHVIN', vin)
            # Click search
            self.chrome.click_button('XPATH', '//a[@onclick="js.search()"]')
            self.utilities.sleep(2) 

            # Assert 1 element is found
            table = self.chrome.get_element('XPATH', ".//tbody[@id='Vehicle2_VehList_result_tbody_3']")
            if not table:
                self._log_error(f'VIN: {vin} not found')
                return False
            rows = self.chrome.get_table_elements(table, 'XPATH', './tr')

            if len(rows) == 0:
                self._log_error(f'VIN: {vin} not found')
                return False
            if len(rows) > 1:
                self._log_error(f'VIN: {vin} found more than once')
                return False
            
            self._log_info(f'{vin} found')
            return True
        
        except Exception as e:
            self._handle_error(e)

    def search_invoice_by_commision(self, commision_number):
        """Search invoice"""
        self.stage = 'SEARCH_VIN'
        try:
            self.commision_number = commision_number
                
            self.chrome.get_element("XPATH", '//*[@id="Invoice_PERIOD_DAYS"]/option[5]')    
            self.chrome.click_button("XPATH", '//*[@id="Invoice_PERIOD_DAYS"]/option[5]')

            
            self.chrome.type_value('XPATH', '//*[@id="Invoice_KOMMINR"]', self.commision_number)
            self.chrome.click_button('XPATH', '//*[@id="Invoice_DOCTYPE"]/option[3]')

            self.chrome.click_button('XPATH', '//a[@onclick="js.search()"]')
            self.utilities.sleep(2) 

            # Assert 1 element is found
            table = self.chrome.get_element('XPATH', '//div[@id="Invoice_table"]/table/tbody//table[@class="results"]/tbody')
            if not table:
                self._log_error(f'commision_number: {commision_number} not found')
                return False
            rows = self.chrome.get_table_elements(table, 'XPATH', './tr')

            if len(rows) == 0:
                self._log_error(f'commision_number: {commision_number} not found')
                return False
            if len(rows) > 1:
                self._log_error(f'commision_number: {commision_number} found more than once')
                return False
            
            self._log_info(f'{commision_number} found')
            return True
        
        except Exception as e:
            self._handle_error(e)

    def save_retail_price(self, build_sheet_wholesale = False):
        """Download invoice"""
        self.stage = 'SAVE_RETAIL_PRICE'
        try:
            self._log_info('Start')

            # Click on Choose
            self.chrome.click_button('XPATH', './/td/a[contains(text(), "Choose")]')
            self.utilities.sleep(2)

            # Switch to iframe
            if not self.chrome.find_and_switch_frame('XPATH', '//iframe[contains(@src, "P_VMS_ShowActions.htm")]'):
                raise Exception('Could not switch to iframe for building sheet')

            # Click on Build Sheet
            self.chrome.click_button('XPATH', '//a[contains(text(), "Build Sheet")]')
            self.utilities.sleep(2)

            # Switch to iframe
            self.chrome.switch_frame(name='diwi')

            # Select Wholesale and Retail Price
            if build_sheet_wholesale:
                self.chrome.select_option_by_value('ID', 'Action_PRICETYPE', 'YB')
                self.utilities.sleep(1)

            # Click on Execute
            self.chrome.click_button('XPATH', '//a[@onclick="js.save()"]')
            self.utilities.sleep(2)

            # Wait for the invoice to download
            self._log_info('Download started')
            if build_sheet_wholesale:
                self.get_file_from_download()
            else:
                self.get_file_from_download(post_to_s3=False)

            self._log_info('Success')

        except Exception as e:
            self._handle_error(e)

    def save_invoice(self):
        """Download invoice"""
        self.stage = 'SAVE_INVOICE'
        try:
            self._log_info('Start')

            # Click on Choose
            self.chrome.click_button('XPATH', '//*[@id="Invoice_table_table"]/tbody/tr/td/div/table/tbody/tr/td[10]/span/a')
            self.utilities.sleep(2)

            # Wait for the invoice to download
            self._log_info('Download started')
            self.get_file_from_download(extension=".pdf", name=self.commision_number, stage='SAVE_INVOICE', post_to_s3=False)

            self._log_info('Success')

        except Exception as e:
            self._handle_error(e)

    def download_new_vehicle_report(self, store):
        """Navigate to the search menu"""

        self.navigate_to_search_menu()

        self.stage = 'SEARCH_REPORT'

        self.chrome.get_element('ID', 'Vehicle2_VehSearch_MODYEAR')


        try:
            # Type Years to look for models
            years = [str(datetime.now().year - 1), str(datetime.now().year), str(datetime.now().year + 1)]
            self.chrome.type_value('ID', 'Vehicle2_VehSearch_MODYEAR', ",".join(years))
            

            # Type Vehicle Status
            self.chrome.type_value('ID', 'Vehicle2_VehSearch_MMSTA', "v300")


            self.chrome.click_button('XPATH', '//a[@onclick="js.search()"]')
            self.utilities.sleep(2) 

            default_time_out = self.chrome.timeout
            self.chrome.timeout = 120
            # Assert 1 element is found
            table = self.chrome.get_element('XPATH', ".//tbody[@id='Vehicle2_VehList_result_tbody_3']")
            if not table:
                self._log_error(f'Not founds vins to download new vehicles report')
                return False
        
            self.chrome.timeout = default_time_out

            self.chrome.click_button('NAME', 'Vehicle2_VehList_result-sel')
            time.sleep(30)

            self.chrome.click_button('XPATH', '//a[@onclick="js.exportData()"]')

            today_path = self.utilities.get_today('%Y/%m/%d')
            self.get_file_from_download(extension=".xls", name=f"new_vehicles_{store}", s3_path=f"new_vehicles_reports/{today_path}/", stage='SAVE_REPORT')

        except Exception:
            self._log_error("Error when downloading the report")
            return False

        self._log_info('Success')

        return True