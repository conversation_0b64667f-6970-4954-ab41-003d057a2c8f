"""
Enhanced FORD Class with Intelligent Error Handling
"""

import re
import time
from bs4 import BeautifulSoup
import os
import sys
import json

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))
from Invoices.downloader import Downloader
from error_notification_system import ErrorSeverity, ErrorCategory

# Import the external email function
from external_email_interface import send_email_notification

class FORD(Downloader):
    def __init__(self, store, current_flow, action):
        # Pass the email function to the parent downloader
        super().__init__(store, current_flow, action, send_email_notification)
        # vin
        self.vin = ''
        self.base_url = 'https://www.fmcdealer.dealerconnection.com/content/fmcdealer/us/en/Sales/VehicleSalesPrograms/VehicleInvoices.html'
        self._safe_selenium_operation("navigate to base URL", self.chrome.navigate_to_url, self.base_url)
        self.label = 'ford'

    def send_headers(self):
        self.chrome.run_cmd("Network.setExtraHTTPHeaders", {
            "headers": {
                "Referer": "https://www.ford.com/",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                "Accept-Encoding": "gzip, deflate, br",
                "Connection": "keep-alive",
                "Upgrade-Insecure-Requests": "1",
                "DNT": "1",
                "Accept-Language": "en-US,en;q=0.9"
            }
        })

        # Erase webdriver from the website
        self.chrome.run_cmd("Page.addScriptToEvaluateOnNewDocument", {
            "source": """
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined
            });
            """
        })

    def login(self, user, password):
        # Start session tracking if error notifications are enabled
        if self.error_notifications_enabled:
            self.start_session_tracking()
            
        # login
        self.stage = 'LOGIN'
        try:
            time.sleep(2)
            self._log_info('Signing in...')
            # Ford page is blocking chrome in headless mode
            if self.chrome.headless:
                self.send_headers()
                self._safe_selenium_operation("navigate to base URL (headless)", self.chrome.navigate_to_url, self.base_url)
                
            if not self.chrome.type_value('ID','userName', user):
                # Try refreshing the page and retry
                self._safe_selenium_operation("refresh page", self.chrome.refresh_page)
                time.sleep(2)
                self._log_info('Retrying login...')
                self._safe_selenium_operation("enter username (retry)", self.chrome.type_value, 'ID', 'userName', user)
            else:
                self._safe_selenium_operation("enter username", self.chrome.type_value, 'ID', 'userName', user)

            time.sleep(2)
            self._safe_selenium_operation("click Next button", self.chrome.click_button, 'XPATH', '//input[@type = "submit" and @value = "Next"]')
            time.sleep(2)
            self._safe_selenium_operation("enter password", self.chrome.type_value, 'ID', 'password', password, password=True)
            time.sleep(2)

            self._safe_selenium_operation("click sign-in button", self.chrome.click_button, 'ID', 'btn-sign-in', action=True)
            time.sleep(5)
            
            # Check for login error messages before looking for dealer login
            login_error_selectors = [
                ('XPATH', '//*[contains(text(), "User ID and/or password invalid")]')
            ]
            
            for method, selector in login_error_selectors:
                if self.chrome.get_element(method, selector, error=False) != False:
                    try:
                        error_text = self.chrome.extract_element_text(method, selector)
                        error_msg = f"Ford login failed: {error_text}"
                    except:
                        error_msg = "Ford login failed: User ID and/or password invalid (M-FER680)"
                    
                    self._log_error(error_msg)
                    raise Exception(error_msg)
            
            # Additional wait after error check
            time.sleep(5)
            
            self._safe_selenium_operation("find dealer login element", self.chrome.get_element, 'XPATH', '//span[text()="Dealer, Supplier, Other Login"]')
            
            if not self.chrome.click_button('XPATH','//span[text()="Dealer, Supplier, Other Login"]'):
                try:
                    self._safe_selenium_operation("refresh page (dealer login)", self.chrome.refresh_page)
                    time.sleep(2)
                    self._safe_selenium_operation("click dealer login (retry)", self.chrome.click_button, 'XPATH', '//span[text()="Dealer, Supplier, Other Login"]')
                except Exception as e:
                    # Print logs for debugging
                    logs = self.chrome.driver.get_log("performance")
                    for log in logs:
                        print(log)
                    raise e
                    
                raise Exception('Could not complete login')
            self._log_info('Login succeeded!')

        except Exception as e:
            # Handle error with intelligent system - will determine if notification should be sent
            self._handle_error(e, ErrorSeverity.CRITICAL, ErrorCategory.LOGIN_FAILURE)

    def search_by_vin(self, vin, count):
        """Search invoice with enhanced error handling"""
        self.stage = 'SEARCH_INVOICE'
        self.vin = vin
        # Call parent to increment processed count
        super().search_by_vin(vin, count)

        try:
            
            if count > 1:
                # Inspect page
                self._safe_selenium_operation("click back hyperlink", self.chrome.click_button, 'ID', 'BackHyperLink')
                self._log_info("Navigating back to search page...")
            else:
                self._safe_selenium_operation("click search link", self.chrome.click_button, 'XPATH', '//a[@href="/DealerStmt/vehinvsearchcustom.asp"]')
            
            self._log_info(f"Searching for VIN: {vin}")
            self._safe_selenium_operation("enter VIN", self.chrome.type_value, 'XPATH', '//*[@name="VINTEXT"]', vin[-8:])  # last 8 digits
            self._safe_selenium_operation("click search button", self.chrome.click_button, 'XPATH', '//img[@onclick="return SearchFields()"]')
            
            invoice_found = False
            try:
                invoice_found = self._safe_selenium_operation("find invoice button", self.chrome.get_element, 'XPATH', '//*[@id="GVResults_ctl02_PreviewImageButton"]')
            except:
                # Try returning to previous page and retry
                self._log_info("Page ran into an error. Retrying...")
                self._safe_selenium_operation("go back", self.chrome.back_arrow)
                time.sleep(2)
                self._safe_selenium_operation("enter VIN (retry)", self.chrome.type_value, 'XPATH', '//*[@name="VINTEXT"]', vin[-8:])
                self._safe_selenium_operation("click search button (retry)", self.chrome.click_button, 'XPATH', '//img[@onclick="return SearchFields()"]')
                invoice_found = self._safe_selenium_operation("find invoice button (retry)", self.chrome.get_element, 'XPATH', '//*[@id="GVResults_ctl02_PreviewImageButton"]')
            
            if not invoice_found:
                # VIN not found - this is low severity and will be throttled automatically
                self.logger.error(f"VIN {vin} not found")
                return False
                
            self._log_info("VIN found!")
            return True
            
        except Exception as e:
            # Only add to batch_errors for actual system errors, not VIN not found
            if 'not found' not in str(e).lower():
                if not hasattr(self, 'batch_errors'):
                    self.batch_errors = []
                
                self.batch_errors.append({
                    'vin': vin,
                    'error': str(e),
                    'category': self._categorize_error(e),
                    'severity': self._determine_severity(e)
                })
            
            self._log_error(f"Search failed for VIN {vin}: {str(e)}")
            return False

    def _categorize_error(self, error):
        """Categorize error for batch processing"""
        error_str = str(error).lower()
        if 'not found' in error_str:
            return ErrorCategory.DATA_NOT_FOUND
        elif 'selenium' in error_str or 'element' in error_str:
            return ErrorCategory.SELENIUM_ERROR
        elif 'timeout' in error_str:
            return ErrorCategory.TIMEOUT_ERROR
        else:
            return ErrorCategory.SYSTEM_ERROR

    def _determine_severity(self, error):
        """Determine severity for batch processing"""
        error_str = str(error).lower()
        if 'not found' in error_str:
            return ErrorSeverity.LOW
        elif 'selenium' in error_str:
            return ErrorSeverity.MEDIUM
        else:
            return ErrorSeverity.HIGH

    def save_invoice(self):
        """Download invoice with enhanced error handling"""
        self.stage = 'SAVE_INVOICE'
        try:
            html = self.chrome.get_page_source()
            soup = BeautifulSoup(html, 'html.parser')

            input_element = soup.find('input', {'id': 'GVResults_ctl02_PreviewImageButton'})
            if input_element and 'onclick' in input_element.attrs:
                self._log_info('Starting download...')
                onclick_attr = input_element['onclick']
                match = re.search(r"OnDownload\('Preview','(.*?)','(.*?)','(.*?)','(.*?)','(.*?)'\)", onclick_attr)

                if match:
                    self._log_info('Download button found')
                    values = match.groups()
                    hid_selected = ",".join(values)

                    base_url = "https://fordvisions.dealerconnection.com/DealerStmt/VehInvDownload.aspx"
                    full_url = f"{base_url}?hid_selected={hid_selected}|"

                    # Navigate to trigger download
                    self._safe_selenium_operation("navigate to download URL", self.chrome.navigate_to_url, full_url)
                    time.sleep(2)
                    self._log_info('Download triggered')

                    self.get_file_from_download()
                    return True
                
            raise Exception('Download button not found')
            
        except Exception as e:
            # Enhanced error handling with automatic categorization
            error_str = str(e).lower()
            if 'download' in error_str or 'file' in error_str:
                severity = ErrorSeverity.MEDIUM  # File issues are medium priority
                category = ErrorCategory.FILE_PROCESSING_ERROR
            elif 's3' in error_str or 'upload' in error_str:
                severity = ErrorSeverity.HIGH  # Upload failures are more serious
                category = ErrorCategory.S3_UPLOAD_ERROR
            else:
                severity = ErrorSeverity.HIGH
                category = ErrorCategory.SYSTEM_ERROR
                
            self._handle_error(e, severity, category, raise_handled_exception=False)
            return False

    def reset_password(self, user, actual_password, new_password):
        """Reset password with enhanced error handling"""
        try:
            self.stage = 'CHANGE_PASSWORD'
            self.current_action = 'reset_password'  # Set action for proper template selection

            self._safe_selenium_operation("navigate to password reset page", self.chrome.navigate_to_url, "https://www.changepassword.ford.com/signin.aspx")
            time.sleep(10)

            # Check if login fields exist
            if self.chrome.get_element('ID', 'cdsid') == False:
                raise Exception("Password reset page did not load correctly - login fields not found")
            
            # Login with current credentials
            self._safe_selenium_operation("enter username", self.chrome.type_value, 'ID', 'cdsid', user)
            time.sleep(1)
            self._safe_selenium_operation("enter password", self.chrome.type_value, 'ID', 'password', actual_password, password=True)
            time.sleep(1)
            
            # Accept terms if checkbox exists
            if self.chrome.get_element('ID', 'checktermsofuse') != False:
                self._safe_selenium_operation("click terms checkbox", self.chrome.click_button, 'ID', 'checktermsofuse')
                time.sleep(2)
            
            # Submit login
            self._safe_selenium_operation("click login submit", self.chrome.click_button, 'XPATH', '//*[@id="ctl00"]/div[3]/button')
            time.sleep(5)

            # Check for login error message
            login_error_selectors = [
                ('XPATH', '//div[contains(text(), "Login Failed")]'),
                ('XPATH', '//div[contains(text(), "Incorrect userid or password")]')
            ]
            
            for method, selector in login_error_selectors:
                if self.chrome.get_element(method, selector, error=False) != False:
                    try:
                        error_text = self.chrome.extract_element_text(method, selector)
                        error_msg = f"Ford password reset login failed: {error_text}"
                    except:
                        error_msg = "Ford password reset login failed: Incorrect userid or password"
                    
                    self._log_error(error_msg)
                    raise Exception(error_msg)
            
            time.sleep(5)
            
            # Navigate to change password page
            self._safe_selenium_operation("navigate to change password page", self.chrome.navigate_to_url, "https://www.changepassword.ford.com/account/changepassword.aspx")
            time.sleep(10)
            
            # Check if password change fields exist
            if self.chrome.get_element('ID', 'password') == False:
                raise Exception("Password change fields not found")
            
            # Fill new password fields
            self._safe_selenium_operation("enter new password", self.chrome.type_value, 'ID', 'password', new_password, password=True)
            time.sleep(1)
            self._safe_selenium_operation("enter verify password", self.chrome.type_value, 'ID', 'vpassword', new_password, password=True)
            time.sleep(1)

            # Find and click submit button
            submit_selectors = ['//*[@id="ctl00"]/div[3]/div/div[2]/button']
            time.sleep(2)

            submit_clicked = False
            for selector in submit_selectors:
                if self.chrome.get_element('XPATH', selector) != False:
                    try:
                        self._safe_selenium_operation(f"click submit button ({selector})", self.chrome.click_button, 'XPATH', selector)
                        submit_clicked = True
                        # time.sleep(3)
                        
                        # Send success notification
                        # if self.error_manager:
                        #     self.error_manager.send_password_reset_success(self.store, user, f"{self.store}_downloader")
                        
                        return True
                    except Exception as e:
                        self._log_error(f"Failed to click submit button {selector}: {str(e)}")
                        continue
            
            if not submit_clicked:
                raise Exception("Submit button not found or could not be clicked")

        except Exception as e:
            self._log_error(f"Password reset failed: {str(e)}")
            # Enhanced error handling for password reset
            error_str = str(e).lower()
            if 'login failed' in error_str or 'incorrect' in error_str:
                severity = ErrorSeverity.HIGH
                category = ErrorCategory.LOGIN_FAILURE
            else:
                severity = ErrorSeverity.HIGH
                category = ErrorCategory.SYSTEM_ERROR
                
            self._handle_error(e, severity, category, raise_handled_exception=True)
            return False