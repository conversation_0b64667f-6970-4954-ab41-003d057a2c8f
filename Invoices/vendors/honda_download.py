import re
import time
import os
import sys
from selenium.webdriver.common.keys import Keys

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))
import invokeRPA
from Invoices.downloader    import Downloader
from datetime import datetime, timedelta
from Invoices.otp_reader    import OtpReader
import traceback

# Import the external email function
from external_email_interface import send_email_notification

from error_exceptions import StoreLoginException, StoreProcessingException

# Import error notification components if available
try:
    from error_notification_system import ErrorSeverity, ErrorCategory
    ERROR_SYSTEM_AVAILABLE = True
except ImportError:
    ERROR_SYSTEM_AVAILABLE = False

class HONDA(Downloader):
    def __init__(self, store, current_flow, action):
        # Pass the email function to the parent downloader
        super().__init__(store, current_flow, action, send_email_notification)
        # vin
        self.vin = ''
        self.main_url = 'https://www.in.honda.com/RRAAApps/login/asp/rraalog.asp?url=https%3A%2F%2Fwww.in.honda.com%2F'
        # Ford page is blocking chrome in headless mode
        if self.chrome.headless:
            self.send_headers()
        self._safe_selenium_operation("navigate to main URL", self.chrome.navigate_to_url, self.main_url)
        self.brand = "Honda"
        self.label = self.brand.lower()

        self.tracker = {}
        self.vin_list = []

    @classmethod
    def clear_and_fill_input(cls, element, value):
        element.send_keys(Keys.CONTROL + "a")
        element.send_keys(value)

    def apply_date_filter(self, date_range):
        # Calculate today's date and 60 days ago
        today = datetime.today()
        past_date = today - timedelta(days=date_range)

        # Locate the date fields
        from_date_month = self.chrome.get_element('ID', "FromDateMonth")
        from_date_day = self.chrome.get_element('ID', "FromDateDay")
        from_date_year = self.chrome.get_element('ID', "FromDateYear")
        to_date_month = self.chrome.get_element('ID', "ToDateMonth")
        to_date_day = self.chrome.get_element('ID', "ToDateDay")
        to_date_year = self.chrome.get_element('ID', "ToDateYear")

        if all([from_date_month, from_date_day, from_date_year,
                to_date_month, to_date_day, to_date_year]):

            # Fill in "From" date (60 days ago)
            self.clear_and_fill_input(from_date_month, past_date.strftime("%m"))
            self.clear_and_fill_input(from_date_day, past_date.strftime("%d"))
            self.clear_and_fill_input(from_date_year, past_date.strftime("%Y"))

            # Fill in "To" date (today)
            self.clear_and_fill_input(to_date_month, today.strftime("%m"))
            self.clear_and_fill_input(to_date_day, today.strftime("%d"))
            self.clear_and_fill_input(to_date_year, today.strftime("%Y"))
        else:
            raise Exception("Could not get the date filter")

    def login(self, user, password, dealer_code, otp):
        """
        Login process:
          - Navigates to the login page.
          - Fills in the dealer number, user ID, and password fields.
          - Submits the login form.
          - When the MFA page appears, it retrieves the access code from an email.
          - Types the MFA code into the field and verifies.
        """
        # Start session tracking if error notifications are enabled
        if self.error_notifications_enabled:
            self.start_session_tracking()
            
        self.stage = 'LOGIN'
        try:
            self._log_info('Navigating to login page...')

            # Fill in login fields using IDs defined on the page
            self._safe_selenium_operation("enter dealer code", self.chrome.type_value, 'ID', 'txtDlrNo', dealer_code)
            self._safe_selenium_operation("enter user ID", self.chrome.type_value, 'ID', 'txtLogonID', user)
            self._safe_selenium_operation("enter password", self.chrome.type_value, 'ID', 'txtPassword', password, True)
            self._log_info('Submitting login form...')
            self._safe_selenium_operation("click login button", self.chrome.click_button, 'ID', 'btnLogon')

            self.utilities.sleep(5)
            # Check for login error messages before looking for dealer login
            login_error_selectors = [
                ('XPATH', '//label[@class="invalid"]/font')
            ]

            for method, selector in login_error_selectors:
                if self.chrome.get_element(method, selector, error=False) != False:
                    try:
                        error_text = self.chrome.extract_element_text(method, selector)
                        error_msg = f"Honda login failed: {error_text}"
                    except:
                        error_msg = "Honda login failed: Your Login attempt failed."
                    
                    self._log_error(error_msg)
                    raise Exception(error_msg)
            
            self.utilities.sleep(5)

            if self.chrome.get_element('ID', 'reselectMessage', error=False):
                self._log_info("MFA page detected. Selecting OTP method...")
                self._safe_selenium_operation("click reselect message", self.chrome.click_button, 'ID','reselectMessage')
                self._safe_selenium_operation("click TOTP option", self.chrome.click_button, 'ID','totp')
                self._safe_selenium_operation("click choose button", self.chrome.click_button, 'ID','chooseButton')
                # Fill otp
                self._log_info("Retrieving MFA code from OTP...")
                code = OtpReader(otp).get_otp()
                self._log_info(f'OTP code: {code}')
                self._safe_selenium_operation("enter OTP code", self.chrome.type_value, 'XPATH', '//input[@id="otppswd"]', code, True)
                self._safe_selenium_operation("click verify button", self.chrome.click_button, 'XPATH', '//button[@id="verifyButton"]')
            self.utilities.sleep(3)
            self._log_info("Login successful.")

        except Exception as e:
            print(traceback.format_exc())
            # Enhanced error handling with specific categorization
            if ERROR_SYSTEM_AVAILABLE:
                error_str = str(e).lower()
                if 'otp' in error_str or 'mfa' in error_str or 'code' in error_str:
                    severity = ErrorSeverity.HIGH
                    category = ErrorCategory.MFA_FAILURE
                else:
                    severity = ErrorSeverity.CRITICAL
                    category = ErrorCategory.LOGIN_FAILURE
                self._handle_error(e, severity, category, raise_handled_exception=True)
            else:
                self._handle_error(e, raise_handled_exception=True)
        
    def send_headers(self):
        self.chrome.run_cmd("Network.setExtraHTTPHeaders", {
            "headers": {
                "Referer": "https://www.in.honda.com/",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                "Accept-Encoding": "gzip, deflate, br",
                "Connection": "keep-alive",
                "Upgrade-Insecure-Requests": "1",
                "DNT": "1",
                "Accept-Language": "en-US,en;q=0.9"
            }
        })

        # Erase webdriver from the website
        self.chrome.run_cmd("Page.addScriptToEvaluateOnNewDocument", {
            "source": """
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined
            });
            """
        })

    def parse_mfa_email(self, body):
        """
        Parses the MFA email body to extract the MFA code.

        Args:
            body (str): The email body.

        Returns:
            str: The extracted MFA code.
        """
        match = re.search(r'(\d{6})\s+is your MFA', body)
        if match:
            return match.group(1)
        else:
            return None

    def get_pipeline_report(self):
        """
        Pipeline Report process:
          - Assumes the user is already logged in.
          - Navigates through the Business Office > Invoices and Statements > Vehicle Pricing area.
          - Selects the "Full Pricing and Allowance Schedule" link.
          - Initiates the download of the PDF report.
        """
        self.stage = 'PIPELINE_REPORT'
        try:
            self._log_info('Navigating to pipeline report...')

            # Click Business Office menu item
            self._safe_selenium_operation("click Executive Management menu", self.chrome.click_button, 'XPATH', '//td[contains(normalize-space(string(.)), "EXECUTIVE") and contains(normalize-space(string(.)), "MANAGEMENT")]')
            self.utilities.sleep(2)

            # Click Invoices and Statements link
            self._safe_selenium_operation("click Vehicle Status menu", self.chrome.click_button, 'XPATH', '//table[.//td[text()="Vehicle Status"]]')
            self.utilities.sleep(2)

            self._safe_selenium_operation("switch to Content frame", self.chrome.switch_frame, "Content")
            # Select checkboxes
            self._safe_selenium_operation("click stop sale VIN radio button", self.chrome.click_button, 'ID', 'ctl00_ctl00_M_content_stopSaleVINOnlyRadioButton_2')
            self._safe_selenium_operation("click in transit checkbox", self.chrome.click_button, 'ID', 'ctl00_ctl00_M_content_isInTransitCheckBox')
            self._safe_selenium_operation("click on hand checkbox", self.chrome.click_button, 'ID', 'ctl00_ctl00_M_content_isOnHandCheckBox')

            # Select Days Inv
            self._safe_selenium_operation("select days inventory option", self.chrome.select_option_by_value, 'ID', 'ctl00_ctl00_M_content_daysInventoryDropDownList','030')
            # Click on Submit
            self._safe_selenium_operation("click submit button", self.chrome.click_button, 'ID', 'ctl00_ctl00_M_content_submitButton')
            self.utilities.sleep(10)

            # Click on Export as excel
            download_button = self._safe_selenium_operation("get export to excel button", self.chrome.get_element, 'XPATH', '//a[contains(@onclick, "ExportToExcel")]')
            try:
                self.chrome.run_script('arguments[0].click();', download_button)
            except Exception as e:
                self._log_error(f"Error clicking download button: {e}")
                raise e

            self._log_info("Initiating download for pipeline report...")
            today = self.utilities.get_today()
            if self.get_file_from_download(
                extension='.csv',
                name=f'{self.brand}_{self.store}_pipeline_report_{today}',
                s3_path='pipeline_report/',
                stage='SAVE_PIPELINE_REPORT'
            ):
                self._log_info('Pipeline report downloaded!')
                return True
        except Exception as e:
            # Enhanced error handling for report download
            if ERROR_SYSTEM_AVAILABLE:
                severity = ErrorSeverity.MEDIUM
                category = ErrorCategory.FILE_PROCESSING_ERROR
                self._handle_error(e, severity, category)
            else:
                self._handle_error(e)
            raise e

    def download_vins(self,vins):
        self.navigate_to_search()
        return super().download_vins(vins)

    def search_by_vin(self, vin, count):
        # Call parent to increment processed count
        super().search_by_vin(vin, count)
        
        self.vin = vin
        self.stage = 'SEARCH_INVOICE'

        try:
            self._safe_selenium_operation("switch to Content frame", self.chrome.switch_frame, "Content")

            search_input = self.chrome.get_element('XPATH', '/html/body/form/div[1]/table[2]/tbody/tr/td[2]/input')
            if not search_input:
                # VIN not found - this is normal, don't add to batch_errors
                self.logger.info(f"VIN {vin} search input not found (normal)")
                return False
            
            self._safe_selenium_operation("click search input", self.chrome.click_button, 'XPATH', '/html/body/form/div[1]/table[2]/tbody/tr/td[2]/input')
            self.utilities.sleep(1)

            vin_input = self.chrome.get_element('XPATH', '//*[@id="forBorderIE10"]/tbody/tr[4]/td[2]/input')
            if not vin_input:
                self.logger.info(f"VIN {vin} input field not found (normal)")
                return False
                
            self.apply_date_filter(date_range=90)
            self._safe_selenium_operation("enter VIN", self.chrome.type_value, 'XPATH', '//*[@id="forBorderIE10"]/tbody/tr[4]/td[2]/input', vin)
            self.utilities.sleep(1)

            search_button = self.chrome.get_element('XPATH', '//*[@id="SearchFunc"]/div[1]/table[3]/tbody/tr/td/input[1]')
            if not search_button:
                self.logger.info(f"VIN {vin} search button not found (normal)")
                return False
            
            self._safe_selenium_operation("click search button", self.chrome.click_button, 'XPATH', '//*[@id="SearchFunc"]/div[1]/table[3]/tbody/tr/td/input[1]')
            self.utilities.sleep(1)

            invoice_button = self.chrome.get_element('XPATH', '//table/tbody/tr/td/input[@type="button" and @value="Vehicle Invoice"]')
            if not invoice_button:
                self.logger.info(f"VIN {vin} invoice button not found (normal)")
                return False
                
            self._safe_selenium_operation("click Vehicle Invoice button", self.chrome.click_button, 'XPATH', '//table/tbody/tr/td/input[@type="button" and @value="Vehicle Invoice"]')
            self.utilities.sleep(1)

            vin_link = self.chrome.get_element('XPATH', f'//table/tbody/tr[td[text()="{vin}"]]/td[a]/a')
            if not vin_link:
                # VIN not found - this is normal
                self.logger.info(f"VIN {vin} not found in Honda system (normal)")
                return False
            else:
                self._safe_selenium_operation("click VIN link", self.chrome.click_button, 'XPATH', f'//table/tbody/tr[td[text()="{vin}"]]/td[a]/a')
            
            time.sleep(2)
            self._log_info('VIN found!')
            self.chrome.save_frame_as_pdf("Content", f"/tmp/HON/{vin}.pdf")

            time.sleep(2)

            self._safe_selenium_operation("switch to Content frame", self.chrome.switch_frame, "Content")
        
            self._safe_selenium_operation("click back button", self.chrome.click_button, 'XPATH', '//*[@id="RDAAACKL_VI01_div"]/div[1]/input')
            self.utilities.sleep(1)

            self._safe_selenium_operation("click return button", self.chrome.click_button, 'XPATH', '/html/body/form/div[1]/table[3]/tbody/tr/td/input')
            self.utilities.sleep(1)

            self.chrome.switch_to_default()
            return True
            
        except Exception as e:
            # Only add to batch_errors for actual system errors, not VIN not found
            if 'not found' not in str(e).lower() and 'normal' not in str(e).lower():
                if not hasattr(self, 'batch_errors'):
                    self.batch_errors = []
                
                self.batch_errors.append({
                    'vin': vin,
                    'error': str(e),
                    'category': self._categorize_error(e),
                    'severity': self._determine_severity(e)
                })
            
            self._log_error(f"Search failed for VIN {vin}: {str(e)}")
            return False

    def _categorize_error(self, error):
        """Categorize error for batch processing"""
        error_str = str(error).lower()
        if 'not found' in error_str:
            return ErrorCategory.DATA_NOT_FOUND
        elif 'selenium' in error_str or 'element' in error_str:
            return ErrorCategory.SELENIUM_ERROR
        elif 'timeout' in error_str:
            return ErrorCategory.TIMEOUT_ERROR
        elif 'otp' in error_str or 'mfa' in error_str:
            return ErrorCategory.MFA_FAILURE
        else:
            return ErrorCategory.SYSTEM_ERROR

    def _determine_severity(self, error):
        """Determine severity for batch processing"""
        error_str = str(error).lower()
        if 'not found' in error_str:
            return ErrorSeverity.LOW
        elif 'selenium' in error_str:
            return ErrorSeverity.MEDIUM
        elif 'otp' in error_str or 'mfa' in error_str:
            return ErrorSeverity.HIGH
        else:
            return ErrorSeverity.HIGH

    def navigate_to_search(self, entry=1):
        """
        Navigates to the search page.
        This example uses navigation via the Business Office menu.
        Adjust the selectors as needed.
        """
        self.stage = 'NAVIGATE_TO_SEARCH'
        try:
            self._log_info('Navigating to search page...')
            self.chrome.switch_to_default()
            self._safe_selenium_operation("click Business Office menu", self.chrome.click_button, 'XPATH', '//td[contains(normalize-space(string(.)), "BUSINESS") and contains(normalize-space(string(.)), "OFFICE")]')
            self.utilities.sleep(2)
            self._safe_selenium_operation("click Acknowledgements menu", self.chrome.click_button, 'XPATH', '//table[.//td[normalize-space(text())="Acknowledgements"]]')
            self.utilities.sleep(2)
            self.chrome.switch_to_default()
            self._log_info('Navigation to search page succeeded.')
            return
        except Exception as e:
            # Enhanced error handling for navigation issues
            if ERROR_SYSTEM_AVAILABLE:
                severity = ErrorSeverity.HIGH
                category = ErrorCategory.SELENIUM_ERROR
                self._handle_error(e, severity, category)
            else:
                self._handle_error(e)
            raise e
        
    def iterate_vins(self, iterator=1):
        """Iterate over VINs"""
        self.stage = 'ITERATE_VINS'
        try:
            self._safe_selenium_operation("switch to Content frame", self.chrome.switch_frame, "Content")
            table = self.chrome.get_element('CSS_SELECTOR', '#rdaaack-VI01-table2')
            if not table:
                self._log_info('No table found. Skipping...')
                return
            # Retrieve all rows
            rows = self.chrome.get_elements('CSS_SELECTOR', '#rdaaack-VI01-table2 > tbody > tr')
            if not rows:
                return
            
            if iterator >= len(rows):
                self._log_info('All VINs processed!')
                return
            
            # Store VIN list in first iteration
            if iterator == 1:
                # Skip first row (header)
                self._log_info(f'Rows found: {len(rows)-1}')
                self.vin_list = [self.chrome.get_table_elements(rows[i + 1], 'TAG', 'td')[3].text.strip() for i in range(len(rows) - 1)]
                self._log_info(f'VIN list: {self.vin_list}')
                
            cells = self.chrome.get_table_elements(rows[iterator], 'TAG', 'td')
            try:
                self.vin = cells[3].text.strip()
            except Exception as e:
                self._log_error(f'Error retrieving VIN: {e}')
                self.iterate_vins(iterator + 1)
                return
            
            try:
                stored = self.download_vin(iterator)
            except Exception as e:
                self._log_error(f'Could not download VIN {self.vin}: {e}')
                stored = False
            if stored:
                self._log_info(f'VIN {self.vin} downloaded!')
                self.tracker[self.vin] = {'error': None, 'stored': True, 'stage': self.stage}
            else:
                self.tracker[self.vin] = {'error': 'Could not download invoice', 'stored': False, 'stage': self.stage}

            # Iterate next row
            self.iterate_vins(iterator + 1)
            return
            
        except Exception as e:
            # Enhanced error handling for VIN iteration
            if ERROR_SYSTEM_AVAILABLE:
                severity = ErrorSeverity.HIGH
                category = ErrorCategory.SYSTEM_ERROR
                self._handle_error(e, severity, category)
            else:
                self._handle_error(e)

    def download_vin(self, iteration=1):
        """
        Searches for an invoice by VIN:
          - Selects the VIN search mode.
          - Enters the VIN and refines it by using its last eight digits.
          - Selects the proper brand from the dropdown.
          - Opens the invoice; if needed, handles a secondary login.
        """
        self.stage = 'SEARCH_INVOICE'
        # Try to navigate to the invoice page
        try:
            self._safe_selenium_operation("switch to Content frame", self.chrome.switch_frame, "Content")

            # Click on invoice for the VIN
            self._log_info("Navigating to invoice details...")
            xpath = f'//table[@id="rdaaack-VI01-table2"]//tr[td[text()="{self.vin}"]]//a[@id="AckRef"]'
            self._safe_selenium_operation("click invoice details link", self.chrome.click_button, 'XPATH', xpath)
        except Exception as e:
            # Enhanced error handling for invoice navigation
            if ERROR_SYSTEM_AVAILABLE:
                severity = ErrorSeverity.HIGH
                category = ErrorCategory.SELENIUM_ERROR
                self._handle_error(e, severity, category)
            else:
                self._handle_error(e)
            raise e

        # Download the invoice
        try:
            self.utilities.sleep(4)
            
            directory = f'/tmp/{self.store}/'
            frame_html = self.chrome.save_frame_as_pdf('Content', f'{directory}{self.vin}.pdf')
            file_path = f'{directory}{self.vin}.txt'
            with open(f'{file_path}', "w", encoding="utf-8") as f:
                f.write(frame_html)
            self._log_info(f"PDF saved to {file_path}.pdf")
            if not self.upload_to_s3(file_path, s3_path=self.s3_path, filename=self.vin, extension=".pdf"):
                raise Exception("Could not upload PDF to S3")
            
            return True
        except Exception as e:
            # Enhanced error handling for file processing
            if ERROR_SYSTEM_AVAILABLE:
                error_str = str(e).lower()
                if 's3' in error_str or 'upload' in error_str:
                    severity = ErrorSeverity.HIGH
                    category = ErrorCategory.S3_UPLOAD_ERROR
                else:
                    severity = ErrorSeverity.HIGH
                    category = ErrorCategory.FILE_PROCESSING_ERROR
                self._handle_error(e, severity, category)
            else:
                self._handle_error(e)
            raise e
        finally:
            # Go back to the search page
            self._log_info("Navigating back to search page...")
            self.chrome.back_arrow()
            self.utilities.sleep(1)
            self.chrome.switch_to_default()

    def get_pricing_sheet(self):
        """
        Retrieves the pricing sheet:
          - Navigates to Business Office > Invoices and Statements > Vehicle Pricing.
          - Selects the "Full Pricing and Allowance Schedule" link.
          - Initiates the PDF download.
        """
        self.stage = 'PRICING_SHEET'
        try:
            self._log_info("Initiating PDF download for pricing sheet...")
            self.chrome.switch_to_default()
            self._safe_selenium_operation("click Business Office menu", self.chrome.click_button, 'XPATH', '//td[contains(normalize-space(string(.)), "BUSINESS") and contains(normalize-space(string(.)), "OFFICE")]')
            self.utilities.sleep(2)
            self._safe_selenium_operation("click AHM Invoices menu", self.chrome.click_button, 'XPATH', '//table[.//td[text()="AHM Invoices and Statements"]]')
            self.utilities.sleep(2)
            self._safe_selenium_operation("switch to Content frame", self.chrome.switch_frame, "Content")
            self._safe_selenium_operation("click Vehicle Pricing link", self.chrome.click_button, 'XPATH', '//a[text()="Vehicle Pricing"]')
            self.utilities.sleep(2)
            self._safe_selenium_operation("click Full Pricing link", self.chrome.click_button, 'XPATH', '//a[contains(@href, "RedirectVehicleFullPricing")]')
            self.utilities.sleep(2)
            self._safe_selenium_operation("click Open button", self.chrome.click_button, 'XPATH', '//a[button[@id="open-button"]]')
            
            self.utilities.sleep(1)
            today_path = self.utilities.get_today('%Y/%m/%d')
            self.get_file_from_download(
                extension='.pdf',
                name=f'{self.brand}_{self.store}_pricing_sheet',
                s3_path=f'invoices_information/{today_path}/',
                stage='PRICING_SHEET'
            )
            self.chrome.switch_to_default()
            self.utilities.sleep(5)
            return True
        except Exception as e:
            # Enhanced error handling for pricing sheet download
            if ERROR_SYSTEM_AVAILABLE:
                severity = ErrorSeverity.MEDIUM
                category = ErrorCategory.FILE_PROCESSING_ERROR
            #     self._handle_error(e, severity, category)
            # else:
            #     self._handle_error(e)
            #     raise e
                self._handle_error(e, severity, category, raise_handled_exception=True)
            else:
                self._handle_error(e, raise_handled_exception=True)
 
    def save_invoice(self):
        """Downloads and renames the invoice based on the VIN."""
        try:
            self.utilities.sleep(4)  # Allow navigation delay
            self.get_file_from_download()
        except Exception as e:
            # Enhanced error handling for invoice saving
            if ERROR_SYSTEM_AVAILABLE:
                error_str = str(e).lower()
                if 'download' in error_str or 'file' in error_str:
                    severity = ErrorSeverity.HIGH
                    category = ErrorCategory.FILE_PROCESSING_ERROR
                elif 's3' in error_str or 'upload' in error_str:
                    severity = ErrorSeverity.HIGH
                    category = ErrorCategory.S3_UPLOAD_ERROR
                else:
                    severity = ErrorSeverity.HIGH
                    category = ErrorCategory.SYSTEM_ERROR
                self._handle_error(e, severity, category)
            else:
                self._handle_error(e)

    def reset_password(self, user, password, new_password, dealer_code, otp):
        """Reset password with enhanced error handling"""
        self.stage = 'CHANGE_PASSWORD'
        self.current_action = 'reset_password'
        
        try:
            self._log_info('Navigating to login page...')
            self.utilities.sleep(2)
            
            # Check if login page loaded
            if self.chrome.get_element('XPATH', '//th[contains(text(), "User Sign In")]') == False:
                raise Exception("Login page did not load correctly")

            # Fill login credentials
            self._safe_selenium_operation("enter dealer code", self.chrome.type_value, 'ID', 'txtDlrNo', dealer_code)
            self.utilities.sleep(1)
            self._safe_selenium_operation("enter user ID", self.chrome.type_value, 'ID', 'txtLogonID', user)
            self.utilities.sleep(1)
            self._safe_selenium_operation("enter password", self.chrome.type_value, 'ID', 'txtPassword', password, password=True)
            self.utilities.sleep(1)
            
            # Click change password button
            if self.chrome.get_element('ID', 'btnChgPwd') == False:
                raise Exception("Change password button not found")
                
            self._safe_selenium_operation("click change password button", self.chrome.click_button, 'ID', 'btnChgPwd')
            self.utilities.sleep(5)

            # Handle JavaScript alert/confirm popup for dealer validation
            try:
                alert = self.chrome.driver.switch_to.alert
                alert_text = alert.text
                self._log_error(f"Alert detected: {alert_text}")
                
                # Check if it's the dealer number error
                if "Invalid dealer number" in alert_text or "dealer number" in alert_text.lower():
                    alert.accept()  # Click OK
                    self.utilities.sleep(1)
                    raise Exception(f"Honda dealer validation failed: {alert_text}")
                else:
                    # Handle other types of alerts
                    alert.accept()  # Click OK
                    self.utilities.sleep(1)
                    raise Exception(f"Honda login alert: {alert_text}")
                    
            except Exception as alert_error:
                # If no alert present, continue with normal flow
                if "no such alert" not in str(alert_error).lower() and "no alert is active" not in str(alert_error).lower():
                    # There was an actual alert error, re-raise it
                    raise alert_error
                
            # Check for login error messages
            login_error_selectors = [
                ('XPATH', '//label[@class="invalid"]/font')
            ]

            for method, selector in login_error_selectors:
                if self.chrome.get_element(method, selector, error=False) != False:
                    try:
                        error_text = self.chrome.extract_element_text(method, selector)
                        error_msg = f"Honda password reset login failed: {error_text}"
                    except:
                        error_msg = "Honda password reset login failed: Your Login attempt failed."
                    
                    self._log_error(error_msg)
                    raise Exception(error_msg)
            
            self.utilities.sleep(5)
            
            # Handle MFA if required
            if self.chrome.get_element('XPATH', '//a[contains(text(), "verification option")]') != False:
                self._log_info("MFA detected, handling OTP...")
                self._safe_selenium_operation("click reselect message", self.chrome.click_button, 'ID', 'reselectMessage')
                self.utilities.sleep(1)
                self._safe_selenium_operation("click TOTP option", self.chrome.click_button, 'ID', 'totp')
                self.utilities.sleep(1)
                self._safe_selenium_operation("click choose button", self.chrome.click_button, 'ID', 'chooseButton')
                self.utilities.sleep(2)
                
                # Get OTP code
                code = OtpReader(otp).get_otp()
                if not code:
                    raise Exception("Could not retrieve OTP code")
                    
                self._safe_selenium_operation("enter OTP code", self.chrome.type_value, 'XPATH', '//input[@id="otppswd"]', code, password=True)
                self.utilities.sleep(1)
                self._safe_selenium_operation("click verify button", self.chrome.click_button, 'XPATH', '//button[@id="verifyButton"]')
                self.utilities.sleep(5)

            # Check if password change fields exist
            if self.chrome.get_element('ID', 'txtPassword') == False:
                raise Exception("Password change fields not found")
                
            # Fill new password fields
            self._safe_selenium_operation("enter new password", self.chrome.type_value, 'ID', 'txtPassword', new_password, password=True)
            self.utilities.sleep(1)
            self._safe_selenium_operation("enter confirm password", self.chrome.type_value, 'ID', 'txtConfirmPassword', new_password, password=True)
            self.utilities.sleep(2)

            # Find and click submit button
            submit_selectors = [
                ('ID', 'btnChgPwd')
            ]
            
            self.utilities.sleep(5)
            submit_clicked = False
            for method, selector in submit_selectors:
                if self.chrome.get_element(method, selector) != False:
                    try:
                        self._safe_selenium_operation(f"click submit button ({selector})", self.chrome.click_button, method, selector)
                        submit_clicked = True
                        self.utilities.sleep(3)
                        
                        # Send success notification
                        # if self.error_manager:
                        #     self.error_manager.send_password_reset_success(self.store, user, f"{self.store}_downloader")
                        
                        return True
                    except Exception as e:
                        self._log_error(f"Failed to click submit button {selector}: {str(e)}")
                        continue
                    
            if not submit_clicked:
                raise Exception("Submit button not found or could not be clicked")
            
        except Exception as e:
            self._log_error(f"Password reset failed: {str(e)}")
            # Enhanced error handling for password reset - MATCH FORD PATTERN
            error_str = str(e).lower()
            if 'login failed' in error_str or 'incorrect' in error_str or 'dealer validation failed' in error_str:
                severity = ErrorSeverity.HIGH
                category = ErrorCategory.LOGIN_FAILURE
            elif 'otp' in error_str or 'mfa' in error_str or 'code' in error_str:
                severity = ErrorSeverity.HIGH
                category = ErrorCategory.MFA_FAILURE
            else:
                severity = ErrorSeverity.HIGH
                category = ErrorCategory.SYSTEM_ERROR
            
            # CHANGE: Use raise_handled_exception=True to prevent duplicates
            self._handle_error(e, severity, category, raise_handled_exception=True)
            return False