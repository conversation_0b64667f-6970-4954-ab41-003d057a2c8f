import re
import time
import os
import sys
from pymongo import MongoClient, DESCENDING
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))
import invokeRPA
from Invoices.downloader import Downloader
from invokeRPA.Utilities.boto3_utils import Boto3Utils

class JLR(Downloader):
    def __init__(self, store, current_flow, action, brand):
        super().__init__(store, current_flow, action)
        # vin
        self.vin = ''
        self.chrome.navigate_to_url('https://login.sso.jaguarlandrover.com/login.do')
        self.brand = brand
        self.label = 'land_rover'
        self.boto3_utils = Boto3Utils()

    def login(self, user, password):
        # login
        self.stage = 'LOGIN'
        self.user = user
        self.password = password
        try:
            self._log_info('Signing in...')
            self.chrome.type_value('ID','user', user)
            self.chrome.type_value('ID','password', password, password=True)
            self.chrome.click_button('ID','signon')
            time.sleep(1)

            self.chrome.click_button('XPATH', '//button/span[text() = "Single Sign On"]')
            time.sleep(1)
            self.chrome.click_button('XPATH', '//*[@id="bySelection"]/div[4]/img')
            
            self.chrome.navigate_to_url('https://grp.jaguarlandrover.com/en-US/group/jaguar-land-rover-usa/homepage')

            self.chrome.click_button('XPATH','//*[@id="sortable-js"]/div/div/div[2]/div/div[2]/div[1]')
            self._log_info('Succeded!')

            self.utilities.sleep(3)

            element = self.chrome.get_element('ID', 'grp2')
            if element != False:
                self.chrome.click_button('ID', 'grp2')
                self.utilities.sleep(3)

            self._log_info('Navigating to invoice page...')

            self.chrome.switch_tab(1)
            self.chrome.switch_frame("contents")
            self.chrome.click_button('ID','m1')

            self._log_info('Navigating to search table...')
            self.chrome.switch_frame("contents")
            self.chrome.click_button('XPATH','/html/body/p[21]')

            # Wait for the search vin table
            if not self.chrome.get_element('XPATH', '/html/body/p[7]'):
                raise Exception('Could not complete login')
            self._log_info('Succeded!')
            
        except Exception as e:
            self._handle_error(e)

    def search_by_vin(self, vin, count, change_password=False):
        """Search invoice"""
        self.stage = 'SEARCH_INVOICE'
        try:
            self.vin = vin

            if count > 1:
                self._log_info("Navigating back to search page...")
                self.chrome.switch_tab(1)
                self.chrome.switch_frame("main")
                self.chrome.click_button('XPATH', '/html/body/form/table/tbody/tr/td[4]/input')
                self.chrome.switch_frame("contents")
                self.chrome.click_button('XPATH','/html/body/p[21]')
            else:
                self.chrome.switch_to_default()
            
            self._log_info("Searching for VIN...")
            self.chrome.switch_frame("main")

            field = self.chrome.get_element("XPATH", '//*[@id="chassis"]')
            field.clear()
            
            self.chrome.type_value('XPATH','//*[@id="chassis"]', vin[-8:]) # last 8 digits

            # mongo_uri = self.boto3_utils.get_secret(f"{os.environ['ENV']}-mongodb_uri")
            # client = pymongo.MongoClient(mongo_uri, serverSelectionTimeoutMS=5000)
            # db = client[os.environ['DB_NAME']]
            # vin_collection = db.get_collection("vin")
            # vin_data = vin_collection.find_one({"vin": self.vin})
            # print("MIRA EL VIN DATA", vin_data)
            # self._log_info("MIRA EL VIN DATA", vin_data)
            #brand = "Jaguar" if "SAD" in self.vin[:3] else "Land Rover"
            brand = "Jaguar" if "JAGUAR" in self.brand else "Land Rover"

            # Dropdown selection for brand
            self._log_info("Selecting brand...")
            options = self.chrome.get_options('XPATH','//*[@id="retailerLst"]/option')
            selected_option = -1
            for idx, option in enumerate(options):
                if brand.lower().replace(" ", "") in option.text.lower().replace(" ", ""):
                    selected_option = idx + 1
                    break

            if selected_option == -1:
                raise Exception(f'Brand {brand} not found in retailer list')

            self.chrome.click_button('XPATH','//*[@id="retailerLst"]')
            self.chrome.click_button('XPATH',f'//*[@id="retailerLst"]/option[{selected_option}]')

            # Click on button continue
            self._log_info("Searching invoice")
            self.chrome.click_button('XPATH', '/html/body/form/table/tbody/tr/td[3]/input')

            time.sleep(3)

            checking_if_more_than_one_vins_finish_equal = self.chrome.extract_element_text('XPATH', '//*[@id="main"]/table[1]/tbody/tr[2]/td[2]')
            if "Multiple VINs ending" in checking_if_more_than_one_vins_finish_equal:
                
                self._log_info("Selecting correct vin...")
                options = self.chrome.get_options('XPATH','//*[@id="VIN"]/option')
                selected_option = -1
                for idx, option in enumerate(options):
                    if self.vin.lower() in option.text.lower().replace(" ", ""):
                        selected_option = idx + 1
                        break

                if selected_option == -1:
                    raise Exception(f'Brand {brand} not vin found in retailer list')
                
                self.chrome.click_button('XPATH','//*[@id="VIN"]/option')
                self.chrome.click_button('XPATH',f'//*[@id="VIN"]/option[{selected_option}]')

                self._log_info("Selecting brand...")
                options = self.chrome.get_options('XPATH','//*[@id="retailerLst"]/option')
                selected_option = -1
                for idx, option in enumerate(options):
                    if brand.lower().replace(" ", "") in option.text.lower().replace(" ", ""):
                        selected_option = idx + 1
                        break

                if selected_option == -1:
                    raise Exception(f'Brand {brand} not found in retailer list')

                self.chrome.click_button('XPATH','//*[@id="retailerLst"]')
                self.chrome.click_button('XPATH',f'//*[@id="retailerLst"]/option[{selected_option}]')

                # Click on button continue
                self._log_info("Searching invoice")
                self.chrome.click_button('XPATH', '/html/body/form/table/tbody/tr/td[3]/input')


            # Click on button View Invoice
            element = self.chrome.get_element('XPATH', '//*[@id="main"]/table[1]/tbody/tr[8]/td[2]/a[1]')
            if element is False:
                return False
                
            self._log_info(f'Invoice found')
            self.chrome.click_button('XPATH', '//*[@id="main"]/table[1]/tbody/tr[8]/td[2]/a[1]')

            # Logic to handle the new tab for the first time that ask us for a new login
            time.sleep(2)
            self.chrome.switch_tab(2)
            if count == 1:
                self._log_info('Handling login...')
                self.chrome.type_value('ID','DEALER-WSLXloginUserIdInput', self.user) # last 8 digits
                self.chrome.type_value('ID','DEALER-WSLXloginPasswordInput', self.password) # last 8 digits
                self.chrome.click_button('XPATH', '//*[@id="DEALER-WSLXloginWSLSubmitButton"]/input')

            button_refresh_password_continue = self.chrome.get_element('XPATH', '//*[@id="DEALER-WSLXauthSuccessContinueButtonText"]')

            if change_password:
                self.chrome.click_button('XPATH', '//*[@id="DEALER-WSLXauthSuccess"]/td/div[4]/center/table/tbody/tr/td[2]/center/form/input')
                return    
            else:
                if button_refresh_password_continue != False:
                    self.chrome.click_button('XPATH', '//*[@id="DEALER-WSLXauthSuccessContinueButtonText"]')

            
            if not self.chrome.get_element('XPATH', '//table//a[contains(text(), "Vehicle Invoice")]'):
                self._log_error('Page did not load correctly. Reloading...')
                self.chrome.refresh_page()
                self.utilities.sleep(2)
                if not self.chrome.get_element('XPATH', '//table//a[contains(text(), "Vehicle Invoice")]'):
                    raise Exception('Page did not load correctly')
                
            
            self.chrome.click_button('XPATH', '(//table//a)[last()]')
            self.chrome.switch_frame("fraBody")
            self.chrome.click_button('ID', 'open-button')
            self._log_info('Success')
            return True

        except Exception as e:
            self._handle_error(e)
            raise e

    def save_invoice(self):
        """Download invoice"""
        self.stage = 'SAVE_INVOICE'

        try:
            self.utilities.sleep(4) # Allow navigation delay
            
            self.get_file_from_download()
        except Exception as e:
            self._handle_error(e)

    def reset_password(self, user, actual_password, new_password):

        # Update button
        try:
 
            mongo_uri = Boto3Utils.get_secret(f"{os.getenv('ENV', '')}-mongodb_uri")
            client = MongoClient(mongo_uri)
            db = client[os.getenv("DB_NAME", "")]
            vin_collection = db.get_collection("vin")
            recent_lr_vin_car_processed = vin_collection.find({"$and": [{"flows.post-inventory.report-data.store": "JLRB"}, {"status": 11}]}).sort("updated_at", DESCENDING)
            vin = recent_lr_vin_car_processed[0]["vin"]

           
            self.search_by_vin(vin, 1, True)
            
            current_password_input = self.chrome.get_element('XPATH','/html/body/form/div/table[2]/tbody/tr[1]/td[2]/input')
            if current_password_input != False:
                self.chrome.type_value('XPATH','/html/body/form/div/table[2]/tbody/tr[1]/td[2]/input', actual_password) # last 8 digits
                time.sleep(0.2)
                self.chrome.type_value('XPATH','/html/body/form/div/table[2]/tbody/tr[2]/td[2]/input', new_password) # last 8 digits
                time.sleep(0.2)
                self.chrome.type_value('XPATH','/html/body/form/div/table[2]/tbody/tr[3]/td[2]/input', new_password) # last 8 digits
                time.sleep(0.2)


                submit_change_password = self.chrome.get_element('XPATH','/html/body/form/div/center/input')
                if submit_change_password != False:
                    self.chrome.click_button('XPATH','/html/body/form/div/center/input')
                    return True
                else:
                    raise Exception("Submit button to change password not exists!")

            else:
                raise Exception("Page to change password not correct!")
               
        except Exception as e:
            return False
 