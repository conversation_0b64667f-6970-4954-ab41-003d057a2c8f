import os
import re
import sys
import time
from datetime import datetime, timedelta
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))
from Invoices.downloader import Downloader

# Make sure the Outlook class (from the refactored module above) is available.
# For example, if saved in outlook.py:
# from outlook import Outlook

class CADILLAC(Downloader):
    def __init__(self, store, current_flow, action):
        """
        Initialize the CADILLAC automation class.

        Parameters:
            email_client_id (str): Client ID for the Outlook API.
            email_client_secret (str): Client secret for the Outlook API.
            email_tenant_id (str): Tenant ID for the Outlook API.
            email_user_id (str): User ID (or email address) for mailbox operations.
        """
        # Enable logging and launch the browser automation.
        super().__init__(store, current_flow, action)
        self.vin = ''
        self.chrome.navigate_to_url('https://bars-reprint.autopartners.net/main')

        self.label = 'CADILLAC'

    def login(self, user, password, trys=3):
        """
        Performs the Toyota login process including MFA.

        Parameters:
            user (str): The username/email for the Toyota site.
            password (str): The password for the Toyota site.

        Raises:
            Exception: If any step in the login process fails.
        """
        self.stage = 'LOGIN'
        try:
            # Enter user credentials.
            self._log_info('Signing in...')
            self.chrome.type_value('ID', 'IDToken1', user)
            self.chrome.type_value('ID', 'IDToken2', password, password=True)
            self.chrome.click_button('NAME', 'Login.Submit')
            self._log_info('Credentials entered')
            # Was redirected?
            if self.chrome.get_element('ID', "mainDocNumInput"):
                self._log_info('Succeded!')
                print('Succeded!')
                return
            else:
                raise "Could not log in"

        except Exception as e:
            self._handle_error(e)

    def download_vins(self, vins):
        self.navigate_to_vin()
        return super().download_vins(vins)

    def navigate_to_vin(self):
        """Navigate to the invoice download section."""
        self.stage = 'NAVIGATE_TO_VIN'
        self.chrome.navigate_to_url('https://bars-reprint.autopartners.net/main')
        self.chrome.get_element('ID', "mainDocNumInput")

    def search_by_vin(self, vin, count=None):
        """Searches for an invoice by VIN."""
        
        try:
            self.navigate_to_vin()
            self._log_info(f"Searching for VIN...")
            self.stage = 'SEARCH_INVOICE'
            self.vin = vin
            self.chrome.select_custom_dropdown_option(
                '//span[@role="combobox" and @aria-label="A/R Statement"]',
                'Vehicle Invoice'
            )
            self.utilities.sleep(2)  # Allow for loading
            self.chrome.type_value('ID', 'mainVINInput', vin)
            self._log_info('Entered information, searching...')
            self.chrome.click_button('XPATH', '//span[contains(@class, "p-button-label") and text()="Search"]')

            # Assert 1 element is found
            self.utilities.sleep(1)  # Allow for loading


            if self.chrome.get_element('XPATH', '//*[@id="pn_id_16-table"]/tbody/tr/td[6]/span[2]/p-button[1]/button') == False:
                return False
            
            self._log_info('VIN found!')
            self.chrome.click_button('XPATH', '//*[@id="pn_id_16-table"]/tbody/tr/td[6]/span[2]/p-button[1]/button')
            time.sleep(2)
            self.chrome.switch_to_next_tab()
            self.chrome.get_element('XPATH', '/html/body/app-root')

            self.chrome.save_html_as_pdf(f"/tmp/CAD/{vin}.pdf")
            
            self._log_info('Downloading invoice from website...')
            return True
        except Exception as e:
            self._handle_error(e)
            return False

    def save_invoice(self):
        """Downloads and renames the invoice based on the VIN."""
        self.stage = 'SAVE_INVOICE'

        try:
            self.utilities.sleep(4)  # Allow navigation delay
            self.get_file_from_download()
        except Exception as e:
            self._handle_error(e)