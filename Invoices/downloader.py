import os
import sys
import time
import boto3
from datetime               import datetime
from botocore.exceptions    import ClientError
from invokeRPA.Utilities.boto3_utils import Boto3Utils

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
import invokeRPA
from invokeRPA.Utilities.outlook_utils  import Outlook
from Invoices.config                    import DefaultConfig
import json
import traceback
from error_exceptions import StoreLoginException, StoreProcessingException

# Import enhanced error notification system
try:
    from error_notification_system import (
        IntelligentErrorNotificationSystem, ErrorContext, ErrorSeverity, ErrorCategory
    )
    ERROR_SYSTEM_AVAILABLE = True
except ImportError:
    # Fallback to original system
    try:
        from error_notification_system import (
            ErrorNotificationTemplates, ErrorContext, ErrorSeverity, ErrorCategory
        )
        ERROR_SYSTEM_AVAILABLE = True
        ENHANCED_SYSTEM = False
    except ImportError:
        ERROR_SYSTEM_AVAILABLE = False
        print("Warning: Error notification system not available")

class Downloader():
    def __init__(self, store, current_flow, action, send_email_function=None):
        self.store = store
        self.current_flow = current_flow
        self.action = action
        self.__create_s3_path()

        # Initialize enhanced error notification system if available
        if ERROR_SYSTEM_AVAILABLE and send_email_function:
            if 'IntelligentErrorNotificationSystem' in globals():
                # Use enhanced system
                self.error_manager = IntelligentErrorNotificationSystem(send_email_function)
                self.enhanced_error_system = True
            else:
                # Use original system
                self.error_manager = ErrorNotificationTemplates(send_email_function)
                self.enhanced_error_system = False
            
            self.error_notifications_enabled = True
            # Session tracking
            self.session_start_time = None
            self.processed_count = 0
            self.successful_count = 0
            self.failed_count = 0
        else:
            self.error_notifications_enabled = False
            self.enhanced_error_system = False

        # Enable log
        self.logger = invokeRPA.Logging(self.bucket, self.store)
        # Automation application
        self.chrome = invokeRPA.ChromeBrowserSelenium(self.logger, self.store)
        # Launch browser
        if not self.chrome.launch_browser(headless=False, store=store):
            # Raise exception if browser fails to launch with enhanced error handling
            error = Exception('Failed to launch browser')
            if self.error_notifications_enabled:
                self._handle_error(error, ErrorSeverity.CRITICAL, ErrorCategory.SYSTEM_ERROR)
            else:
                raise error
        # Utilities objects
        self.utilities = invokeRPA.Utilities()
        # Boto3 objects
        self.boto3_utils = Boto3Utils()
        # Downloaded Invoices path
        self.destination_path = os.getcwd() + "/downloaded_invoices"
        # Assert folder exists
        if not os.path.exists(self.destination_path):
            os.makedirs(self.destination_path)

        self.s3 = boto3.client('s3')
        self.stored = False
        self.tracker = {}

    def __create_s3_path(self):
        self.bucket = DefaultConfig.get_s3_bucket()
        if self.current_flow == "post-inventory":
            folder = 'invoices'
        elif self.current_flow == "pre-inventory":
            folder = 'invoices_new_cars'
        else:
            folder = ""
        date_today = datetime.now().strftime('%Y/%m/%d')
        self.s3_path = f'{folder}/{date_today}/'

    def __get_error_name(self):
        return f'{os.getcwd()}/logs/{self.label}_{self.vin}_{self.stage.lower()}_error.png'
    
    def _init_outlook(self, stage = None):
        email_config = DefaultConfig.get_email_config(stage)
        self.outlook_user_id = email_config['USERID']
        self.outlook = Outlook(
            client_id=email_config['CLIENT_ID'],
            client_secret=email_config['CLIENT_SECRET'],
            tenant_id=email_config['TENANT_ID'],
            user_id=email_config['USERID']
        )

    def _log_info(self, message):
        self.logger.log(f'{self.label.upper()} - {self.stage}.{self.vin} - {message}')

    def _log_error(self, message):
        self.logger.error(f'{self.label.upper()} - {self.stage}.{self.vin} - {message}')
    
    def _handle_error(self, e, severity=None, category=None, raise_handled_exception=True):
        """Enhanced error handling with intelligent notifications"""
        self._log_error(f'Failed: {str(e)}')
        screenshot_name = self.__get_error_name()
        screenshot_taken = False
        
        if self.chrome.take_screenshot(screenshot_name):
            self.logger.error(f"Storing screenshot in '{screenshot_name}'")
            s3_path = f"logs/screenshots/{self.label}_{self.vin}_{self.stage.lower()}_error.png"
            self.s3.upload_file(screenshot_name, self.bucket, s3_path)
            screenshot_taken = True
        else:
            self.logger.error(f"Failed to store screenshot in '{screenshot_name}'")

        # Capture enhanced context based on action type
        additional_context = self._build_action_context()

        # Enhanced error notification handling
        error_id = None
        if self.error_notifications_enabled:
            if self.enhanced_error_system:
                error_id = self._send_enhanced_error_notification(e, screenshot_name if screenshot_taken else None, severity, category, additional_vars={'additional_context': additional_context})
            else:
                error_id = self._send_error_notification(e, screenshot_name if screenshot_taken else None, severity, category)
        
        # Raise a handled exception to prevent duplicate notifications
        if raise_handled_exception:
            if hasattr(self, 'stage') and self.stage in ['LOGIN', 'CHANGE_PASSWORD', 'PRICING_SHEET']:
                raise StoreLoginException(e, error_id)
            else:
                raise StoreProcessingException(e, error_id)
        else:
            raise e

    def _build_action_context(self):
        """Build action-specific context information"""
        context = {
            'action_type': getattr(self, 'current_flow', 'unknown'),
            'error_context': 'store_level'
        }
        
        # Add VIN-specific context for invoice downloads
        if hasattr(self, 'tracker') and self.tracker:
            failed_vins = [vin for vin, data in self.tracker.items() if data.get('error')]
            successful_vins = [vin for vin, data in self.tracker.items() if data.get('stored')]
            
            context.update({
                'total_vins_in_batch': len(self.tracker),
                'failed_vins': failed_vins,
                'successful_vins': successful_vins,
                'batch_progress': f"{len(successful_vins)}/{len(self.tracker)}"
            })
        
        return context


    def _send_enhanced_error_notification(self, error, screenshot_path=None, severity=None, category=None, additional_vars=None):
        """Send error notification using the enhanced intelligent system"""
        try:
            # Get additional context from additional_vars
            additional_context = {}
            if additional_vars and 'additional_context' in additional_vars:
                additional_context = additional_vars['additional_context']

            # Capture browser state if available
            browser_logs = None
            url = None
            
            if hasattr(self, 'chrome') and self.chrome:
                try:
                    url = self.chrome.driver.current_url
                    browser_logs = self._capture_browser_state()
                    if not screenshot_path:
                        screenshot_path = self._take_error_screenshot()
                except:
                    pass  # Don't let screenshot/logging errors crash error handling
            
            # Create error context
            context = ErrorContext(
                store=self.store,
                vin=getattr(self, 'vin', None),
                stage=getattr(self, 'stage', None),
                action=getattr(self, 'action', None),
                stage_action=getattr(self, 'current_flow', None),
                url=url,
                browser_logs=browser_logs,
                screenshot_path=screenshot_path,
                additional_data={
                    'processed_count': getattr(self, 'processed_count', 0),
                    'has_screenshot': screenshot_path is not None,
                    'error_level': 'store',
                    **additional_context
                }
            )
            
            # Determine severity and category if not provided
            if not severity:
                severity = self._determine_error_severity(error)
            if not category:
                category = self._determine_error_category(error)
            
            # Use enhanced error system - it handles intelligent throttling automatically
            error_id = self.error_manager.capture_error(
                error, context, severity, category, app_id=f"{self.store}_downloader"
            )
            return error_id
            
        except Exception as notification_error:
            self.logger.error(f"Failed to send enhanced error notification: {notification_error}")
            return None

    def _send_error_notification(self, error, screenshot_path=None, severity=None, category=None):
        """Send error notification using the original system (fallback)"""
        try:
            # Capture browser state if available
            browser_state = {}
            if hasattr(self, 'chrome') and self.chrome:
                browser_state = self.error_manager.capture_browser_state(self.chrome)
            
            # Use provided screenshot or take a new one
            if not screenshot_path and hasattr(self, 'chrome') and self.chrome:
                error_id = f"ERR_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                screenshot_path = self.error_manager.take_error_screenshot(self.chrome, error_id)
            
            # Create error context with proper attachment handling
            context = ErrorContext(
                store=self.store,
                vin=getattr(self, 'vin', None),
                stage=getattr(self, 'stage', None),
                action=getattr(self, 'action', None),
                stage_action=getattr(self, 'current_flow', None),
                url=browser_state.get('current_url'),
                browser_logs=browser_state.get('browser_logs'),
                screenshot_path=screenshot_path,
                additional_data={
                    **browser_state,
                    'has_screenshot': screenshot_path is not None,
                    'error_level': 'store'
                }
            )
            
            # Determine severity and category if not provided
            if not severity:
                severity = self._determine_error_severity(error)
            if not category:
                category = self._determine_error_category(error)
            
            # Capture and process error - get the error ID back
            error_id = self.error_manager.capture_error(error, context, severity, category, app_id=f"{self.store}_downloader")
            return error_id
            
        except Exception as notification_error:
            self.logger.error(f"Failed to send error notification: {notification_error}")
            return None

    def _capture_browser_state(self):
        """Capture browser state for debugging"""
        try:
            if hasattr(self, 'chrome') and self.chrome:
                logs = self.chrome.driver.get_log('browser')
                return [
                    {'level': log['level'], 'message': log['message'], 'timestamp': log['timestamp']}
                    for log in logs[-5:]  # Last 5 logs
                ]
        except:
            pass
        return None
    
    def _take_error_screenshot(self):
        """Take screenshot for error documentation"""
        try:
            if hasattr(self, 'chrome') and self.chrome:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                screenshot_path = f"/tmp/error_screenshots/{self.store}_{timestamp}.png"
                os.makedirs(os.path.dirname(screenshot_path), exist_ok=True)
                
                if self.chrome.take_screenshot(screenshot_path):
                    return screenshot_path
        except:
            pass
        return None

    def _determine_error_severity(self, error):
        """Determine error severity based on error type"""
        error_str = str(error).lower()
        if 'login' in error_str or 'authentication' in error_str:
            return ErrorSeverity.CRITICAL
        elif 'not found' in error_str:
            return ErrorSeverity.LOW  # Enhanced: VIN not found is low severity
        elif 'timeout' in error_str or 'network' in error_str:
            return ErrorSeverity.MEDIUM  # Enhanced: Reduced from HIGH
        elif 'element' in error_str or 'selenium' in error_str:
            return ErrorSeverity.MEDIUM  # Enhanced: Reduced from HIGH
        else:
            return ErrorSeverity.HIGH

    def _determine_error_category(self, error):
        """Determine error category based on error type"""
        error_str = str(error).lower()
        if 'login' in error_str or 'authentication' in error_str:
            return ErrorCategory.LOGIN_FAILURE
        elif 'mfa' in error_str or 'code' in error_str:
            return ErrorCategory.MFA_FAILURE
        elif 'not found' in error_str:
            return ErrorCategory.DATA_NOT_FOUND
        elif 'timeout' in error_str:
            return ErrorCategory.TIMEOUT_ERROR
        elif 'network' in error_str or 'connection' in error_str:
            return ErrorCategory.NETWORK_ERROR
        elif 'element' in error_str or 'selenium' in error_str:
            return ErrorCategory.SELENIUM_ERROR
        elif 'file' in error_str or 'download' in error_str:
            return ErrorCategory.FILE_PROCESSING_ERROR
        elif 's3' in error_str or 'upload' in error_str:
            return ErrorCategory.S3_UPLOAD_ERROR
        else:
            return ErrorCategory.SYSTEM_ERROR

    def start_session_tracking(self):
        """Start session tracking for performance monitoring"""
        if self.error_notifications_enabled:
            self.session_start_time = datetime.now()
            self.processed_count = 0
            self.successful_count = 0
            self.failed_count = 0
            self.logger.log(f"Session tracking started for {self.store}")

    def increment_processed_count(self):
        """Increment processed VIN count"""
        if self.error_notifications_enabled:
            self.processed_count += 1

    def _calculate_session_duration(self):
        """Calculate session duration"""
        if self.error_notifications_enabled and hasattr(self, 'session_start_time') and self.session_start_time:
            duration_seconds = (datetime.now() - self.session_start_time).total_seconds()
            hours = int(duration_seconds // 3600)
            minutes = int((duration_seconds % 3600) // 60)
            seconds = int(duration_seconds % 60)
            return f"{hours}h {minutes}m {seconds}s"
        return "Unknown"

    def _safe_selenium_operation(self, operation_name, selenium_func, *args, **kwargs):
        """
        Enhanced wrapper for Selenium operations with intelligent error handling
        """
        try:
            result = selenium_func(*args, **kwargs)
            if result == False:
                error_msg = f"Selenium operation '{operation_name}' failed - returned False"
                raise Exception(error_msg)
            return result
        except Exception as e:
            # Enhanced error handling with automatic categorization
            if getattr(self, 'batch_mode', False):
                # Just log and return False - no notifications
                self._log_error(f"Batch operation '{operation_name}' failed: {str(e)}")
                return False
            else:
                self._log_error(f"Selenium operation '{operation_name}' failed: {str(e)}")
                raise e
    
    def __get_latest_file(self, directory, extension=".pdf"):
        """Returns the most recently modified file in the given directory"""
        try:
            files = [os.path.join(directory, f) for f in os.listdir(directory) if f.lower().endswith(extension)]
            self.logger.debug(f'Files in directory: {files}')
            files = [f for f in files if os.path.isfile(f)]
            return max(files, key=os.path.getctime) if files else None
        except Exception as e:
            self.logger.error(f'Error in get_latest_file: {e}')
            return None

    def __is_download_complete(self, file):
        """Check if the file is completely downloaded by checking its size"""
        if not os.path.exists(file):
            return False  # File disappeared
        initial_size = os.path.getsize(file)
        time.sleep(2)  # Wait a bit longer
        if not os.path.exists(file):
            return False  # File disappeared
        final_size = os.path.getsize(file)
        return initial_size == final_size

    def __is_download_in_progress(self, directory):
        """Check for partial downloads (Chrome: .crdownload, Firefox: .part, Edge: .download)"""
        partial_exts = [".crdownload", ".part", ".download", ".tmp"]
        try:
            files = os.listdir(directory)
            self.logger.debug(f'Checking for partial downloads in {directory}: {files}')
            return any(any(f.endswith(ext) for ext in partial_exts) for f in files)
        except Exception as e:
            self.logger.error(f'Error checking download progress: {e}')
            return False

    def get_file_from_download(self, extension=".pdf", name=None, s3_path=None, stage='SAVE_INVOICE', post_to_s3 = True):
        """Get the file from the download folder and relocate it"""
        self.stage = stage
        download_path = os.getcwd() + "/" + self.store

        latest_file = None
        try:
            # Wait for the download to complete
            timeout = 60
            start_time = time.time()

            while time.time() - start_time < timeout:
                if not self.__is_download_in_progress(download_path):
                    latest_file = self.__get_latest_file(download_path, extension=extension)
                    if latest_file and self.__is_download_complete(latest_file):
                        break
                time.sleep(2)

            if not latest_file:
                self._log_error(f'Failed: File not downloaded or missing')
                return False

            # Rename the file to match the VIN number
            self._log_info(f'Moving file {latest_file} to the correct location')
            if name:
                new_file_path = os.path.join(self.destination_path, f"{name}{extension}")
            else:
                new_file_path = os.path.join(self.destination_path, f"{self.vin}{extension}")

            # Ensure no filename collision
            try:
                if os.path.exists(new_file_path):
                    self._log_error(f'File {new_file_path} already exists. Overwriting.')
                    os.remove(new_file_path)
                os.rename(latest_file, new_file_path)
            except Exception as e:
                self._log_error(f'Error renaming file: {e}')
                return False

            # Post to S3
            if post_to_s3:
                try:
                    self.upload_to_s3(new_file_path, s3_path=s3_path, filename=name, extension=extension)
                except Exception as e:
                    self._log_error(f'Error uploading to S3: {e}')
                    # Send specific S3 upload error notification with enhanced handling
                    if self.error_notifications_enabled:
                        if self.enhanced_error_system:
                            self._send_enhanced_error_notification(e, category=ErrorCategory.S3_UPLOAD_ERROR, severity=ErrorSeverity.HIGH)
                        else:
                            self._send_error_notification(e, category=ErrorCategory.S3_UPLOAD_ERROR, severity=ErrorSeverity.HIGH)
                    return False
                
                self.stored = True

                if self.stored:
                    self._log_info(f'Successfully uploaded {new_file_path} to S3')
                    return True

        except Exception as e:
            # Clean up the file if it exists
            if latest_file and os.path.exists(latest_file):
                try:
                    os.remove(latest_file)
                except Exception as cleanup_e:
                    self._log_error(f'Error deleting file: {cleanup_e}')
            # Log the error with file processing category
            self._handle_error(e, severity=ErrorSeverity.MEDIUM, category=ErrorCategory.FILE_PROCESSING_ERROR, raise_handled_exception=False)

        return False

    def upload_to_s3(self, file_path, s3_path=None, filename=None, extension=".pdf"):
        """Upload file to S3"""
        self.stage = 'UPLOAD_TO_S3'
        try:
            self._log_info('Start')
            key = (self.s3_path if not s3_path else s3_path) + \
                (filename if filename else self.vin) + extension
            self.s3.upload_file(file_path, self.bucket, key)
            destination = f's3://{self.bucket}/{key}'
            self._log_info(f'Success: {destination}')
            return destination
        except ClientError as e:
            self._handle_error(e, severity=ErrorSeverity.HIGH, category=ErrorCategory.S3_UPLOAD_ERROR)

    def save_invoice(self):
        """Download invoice"""
        pass

    def search_by_vin(self, vin, count):
        """Search invoice"""
        if self.error_notifications_enabled:
            self.increment_processed_count()
        pass
    
    def reset_password(self, user, actual_password, new_password):
        pass

    def reset_password_and_refresh_secret(self, secret_name, store, user, actual_password, new_password, dealer_code='', otp=''):
        print("MIRA LA NEW PASSWORD", new_password)
        
        try:
            if "hon" in store.lower():
                successfully_changed = self.reset_password(user, actual_password, new_password, dealer_code, otp)
            else:
                successfully_changed = self.reset_password(user, actual_password, new_password)
            
            if successfully_changed:
                if "hon" in store.lower():
                    secret_data = {
                        'user': user, 
                        'old_password': actual_password, 
                        'actual_password': new_password, 
                        'dealer_code': dealer_code, 
                        'otp': otp
                    }
                else:
                    secret_data = {
                        'user': user, 
                        'old_password': actual_password, 
                        'actual_password': new_password
                    }
                
                self.boto3_utils.update_secret(secret_name, json.dumps(secret_data))

                
        except Exception as e:
            # Send password reset error notification with enhanced handling
            if self.error_notifications_enabled:
                if self.enhanced_error_system:
                    self._send_enhanced_error_notification(e, severity=ErrorSeverity.HIGH, category=ErrorCategory.LOGIN_FAILURE)
                else:
                    self._send_error_notification(e, severity=ErrorSeverity.HIGH, category=ErrorCategory.LOGIN_FAILURE)
            raise
        
        return successfully_changed 
            
    def download_vins(self, vins):
        # Set batch mode to suppress individual notifications
        self.batch_mode = True
        
        # Start session tracking
        if self.error_notifications_enabled:
            self.start_session_tracking()
    
        count = 1
        self.logger.log('Downloading invoices...')
        
        # Initialize batch error tracking
        self.batch_errors = []

        for v in vins:
            self.tracker[v] = {'error': None, 'stored': False, 'stage': None}
            try:
                self.logger.log(f'Downloading invoice for VIN: {v}')
                found = self.search_by_vin(v, count)
                # Only increment count if the search was successful
                count += 1

                if not found:
                    self.tracker[v]['error'] = 'VIN not found'
                    self.tracker[v]['stage'] = self.stage
                    if self.error_notifications_enabled:
                        self.failed_count += 1
                    continue

                self.save_invoice()
                if self.stored:
                    self.tracker[v]['stored'] = self.stored
                    if self.error_notifications_enabled:
                        self.successful_count += 1
                else:
                    self.tracker[v]['error'] = 'Failed to store invoice'
                    self.tracker[v]['stage'] = self.stage
                    if self.error_notifications_enabled:
                        self.failed_count += 1
                        
            except Exception as e:
                self.logger.error(f'Download invoices failed for VIN {v}: {str(e)}')
                self.tracker[v]['error'] = str(e)
                self.tracker[v]['stage'] = self.stage
                if self.error_notifications_enabled:
                    self.failed_count += 1

        self.batch_mode = False

        # Send SINGLE batch notification only if there were failures
        if self.error_notifications_enabled:
            failed_vins = [vin for vin, data in self.tracker.items() if data['error']]
            if failed_vins:
                self.logger.log(f"Sending single batch notification for {len(failed_vins)} failed VINs")
                self._send_single_batch_notification(vins, failed_vins)

        # Session summary only for significant issues
        if self.error_notifications_enabled and self.enhanced_error_system:
            if self.failed_count > len(vins) * 0.5:  # More than 50% failed
                session_stats = {
                    'total_vins': len(vins),
                    'successful_downloads': self.successful_count,
                    'failed_downloads': self.failed_count,
                    'success_rate': (self.successful_count / len(vins)) * 100 if vins else 0,
                    'store': self.store,
                    'current_flow': self.current_flow,
                    'session_duration': self._calculate_session_duration(),
                    'failed_vins': failed_vins
                }
                self.error_manager.send_session_summary(session_stats, app_id=f"{self.store}_downloader")


        try:
            del self.chrome
        except Exception as e:
            self.logger.error(f'Failed to close browser: {str(e)}')

        return self.tracker

    def _send_single_batch_notification(self, all_vins, failed_vins):
        """Send a single consolidated batch notification only for significant errors"""
        try:
            # Analyze failure types
            critical_failures = []
            vin_not_found_count = 0
            
            for vin in failed_vins:
                error = self.tracker[vin].get('error', '')
                stage = self.tracker[vin].get('stage', '')
                
                if 'not found' in error.lower() or stage == 'SEARCH_INVOICE':
                    vin_not_found_count += 1
                else:
                    critical_failures.append(vin)
            
            # Only send notification if there are critical failures
            # VIN not found is normal and shouldn't trigger notifications
            if not critical_failures and vin_not_found_count == len(failed_vins):
                self.logger.log(f"All {len(failed_vins)} failures are 'VIN not found' - no notification needed")
                return
            
            successful_vins = [vin for vin, data in self.tracker.items() if data.get('stored')]
            
            # Determine severity based on critical failures only
            if critical_failures:
                failure_rate = len(critical_failures) / len(all_vins)
                if failure_rate >= 0.5:
                    severity = ErrorSeverity.HIGH
                else:
                    severity = ErrorSeverity.MEDIUM
            else:
                # No critical failures, just VIN not found
                return
            
            # Create batch error context
            context = ErrorContext(
                store=self.store,
                vin=None,
                stage='BATCH_PROCESSING',
                action=self.action,
                url=getattr(self.chrome.driver, 'current_url', None) if hasattr(self, 'chrome') and self.chrome else None,
                stage_action=getattr(self, 'current_flow', None),
                additional_data={
                    'batch_size': len(all_vins),
                    'failed_vins': critical_failures,  # Only include critical failures
                    'vin_not_found_count': vin_not_found_count,
                    'successful_vins': successful_vins,
                    'failure_rate': len(critical_failures) / len(all_vins) * 100,
                    'error_level': 'batch'
                }
            )
            
            # Create appropriate error message
            if critical_failures:
                error_message = f"Batch processing issues: {len(critical_failures)} critical failures, {vin_not_found_count} VINs not found (normal)"
            else:
                error_message = f"Batch completed: {vin_not_found_count} VINs not found (normal), no critical issues"
            
            # Send notification only for critical failures
            error_id = self.error_manager.capture_error(
                Exception(error_message),
                context,
                severity,
                ErrorCategory.SYSTEM_ERROR,  # Use existing enum
                app_id=f"{self.store}_downloader"
            )
            self.logger.log(f"Batch notification sent for critical failures only: {error_id}")
            
        except Exception as e:
            self.logger.error(f"Failed to send batch notification: {e}")

    def download_new_vehicle_report(self, store):
        pass

    def download_new_vehicles_report(self, store):
        self.logger.log('Downloading new vehicle report...')
        self.tracker = {'error': None, 'stored': False, 'stage': None}
        try:
            self.logger.log(f'Downloading new vehicles report')

            found = self.download_new_vehicle_report(store)
            if not found:
                self.tracker['error'] = 'Report not found'
                self.tracker['stage'] = self.stage
                    
            if self.stored:
                self.tracker['stored'] = self.stored
            else:
                self.tracker['error'] = 'Failed to store report'
                self.tracker['stage'] = self.stage
        except Exception as e:
            self.logger.error(f'Download report failed: {str(e)}')
            self.tracker['error'] = str(e)
            self.tracker['stage'] = self.stage
            # Enhanced report download error notification
            if self.error_notifications_enabled:
                if self.enhanced_error_system:
                    self._send_enhanced_error_notification(e, severity=ErrorSeverity.MEDIUM, category=ErrorCategory.FILE_PROCESSING_ERROR)
                else:
                    self._send_error_notification(e, severity=ErrorSeverity.MEDIUM, category=ErrorCategory.FILE_PROCESSING_ERROR)

        try:
            del self.chrome
        except Exception as e:
            self.logger.error(f'Failed to close browser: {str(e)}')

        return self.tracker

    def close(self):
        """Enhanced cleanup method"""
        if self.error_notifications_enabled and hasattr(self, 'error_manager'):
            try:
                self.error_manager.close()
            except Exception as e:
                self.logger.error(f"Error closing error manager: {e}")
        
        # Close browser if it exists
        if hasattr(self, 'chrome'):
            try:
                del self.chrome
            except Exception as e:
                self.logger.error(f"Error closing browser: {e}")

    def __del__(self):
        """Cleanup when object is destroyed"""
        self.close()