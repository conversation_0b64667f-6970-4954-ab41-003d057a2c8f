from invokeRPA.Utilities.boto3_utils    import Boto3Utils

import json
import os

DEV_CONFIG = {
    "store_config": {
        "LOA": {
            "user": "fernanm81",
            "password": "Uniform456"
        },
        "LOG": {
            "user": "fernanm80",
            "password": "Invoice789"
        },
        "FOR": {
            "user": "M-FER680",
            "password": "HennessyInvoke2025!"
        },
        "JLRN": {
            "user": "m-fer172",
            "password": "Rover#01"
        },
        "POR": {
            "user": "porm<PERSON>nandez",
            "password": "HennessyInvoke2025!"
        },
        "PNW": {
            "user": "pnwmfernandez",
            "password": "HennessyInvoke2025!"
        },
        "CAD": {
            "user": "ncars10",
            "password": "oyT-_Y2Q!"
        },
        "HON": {
            "brand": "Honda",
            "user": "agribbins",
            "password": "Hennessy2025!",
            "dealer_num": "208054",
            "otp": "HXPAVTPDIKBCP6QAINNTSFW6TX2FGKHL",
        },
    },
    "outlook_config": {
        "client_id": "974823b8-952a-43aa-912b-33bdf25e88f5",
        "client_secret": "****************************************",
        "tenant_id": "8a18fab7-7edc-47b4-85e1-016b3a6445d4",
        "user_id": "<EMAIL>"
    },
    "s3_bucket": "snd-hen-bucket"
}

class DefaultConfig:
    # Retrieve stage from env variable
    env = os.getenv('ENV', 'snd-hen')

    # Retrieve secret
    config = json.loads(Boto3Utils().get_secret(env + "-selenium_config"))

    @classmethod
    def get_parameters(cls):
        """Return all configuration parameters as a dictionary"""
        return {key: value for key, value in cls.__dict__.items() if not key.startswith("__") and not callable(value)}
    
    @classmethod
    def get_user_password(cls, store, brand = None):
        """Return username and password for a given store"""
        if "mbg" in store.lower():
            credentials = json.loads(Boto3Utils().get_secret(cls.env + f"-user_login_{store.lower()}_{brand.lower()}").replace("\'", "\""))
        else:
            credentials = json.loads(Boto3Utils().get_secret(cls.env + f"-user_login_{store.lower()}").replace("\'", "\""))
        if "hon" in store.lower():
            return credentials['user'], credentials['actual_password'], credentials['dealer_code'], credentials['otp']
        

        return credentials['user'], credentials['actual_password']
    
    @classmethod
    def get_email_config(cls, stage = None):
        """Return email configuration"""
        emails_config = None
        if stage == None:
            emails_config = json.loads(Boto3Utils().get_secret(cls.env + f"-email_credentials"))['mfa_outlook_config']
        elif stage == "pre-inventory":
            pass
        elif stage == "used-cars":
            emails_config = json.loads(Boto3Utils().get_secret(cls.env + f"-email_credentials"))[f"{cls.env}_hennessy_used_cars"]
            print(emails_config)

        return emails_config
    
    @classmethod
    def get_s3_bucket(cls):
        """Return S3 bucket name"""
        return f"{cls.env}-bucket"


