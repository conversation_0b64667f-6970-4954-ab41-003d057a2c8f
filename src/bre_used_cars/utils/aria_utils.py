import requests
import time

class ARIA():

	def __init__(self, base_url, request_token):
		self.request_token = request_token
		self.base_url = base_url
		
	def request_to_aria(self, type, url, body, return_data=False):
		if type == 'post':
			x = requests.post(url, json = body, headers = {'Authorization': self.request_token, 'Content-Type': 'application/json', 'Accept': '*/*', 'Accept-Encoding': 'gzip, deflate, br'})		
		elif type == 'get':
			x = requests.get(url, headers = {'Authorization': self.request_token, 'Content-Type': 'application/json', 'Accept': '*/*', 'Accept-Encoding': 'gzip, deflate, br'})		
		elif type == 'delete':
			x = requests.delete(url, headers = {'Authorization': self.request_token, 'Content-Type': 'application/json', 'Accept': '*/*', 'Accept-Encoding': 'gzip, deflate, br'})		
		else:
			print('Type not supported!')

		print("Resposne --> ", x.text)

		if x.status_code != 202:
			print('!!!!!!!!!!!!!!!!!!!!')
			print(x.status_code)
			print('!!!!!!!!!!!!!!!!!!!!')

		if return_data:
			return x.json()
			
			
	def bre_reply(self, app_id, item_id, bre_response):
		url = f'{self.base_url}/public/v1/apps/{app_id}/case_management_middleware/work_items/{item_id}/bre'
		body = {
			"data":{
				"type":"workItem",
				"id": item_id,
				"attributes":{
					"response": bre_response
				}
			}
			}
		self.request_to_aria('post', url, body)			
	
	def create_item(self, group_name, pdf_base64, app_id):
		url = f'{self.base_url}/public/v1/apps/{app_id}/document_processing'
		body = {
			"data": {
				"attributes": {
					"groups": [
						{
							"name": group_name,
							"content": f"data:application/pdf;base64,{pdf_base64}",

							"metadata": []
						}
					
					]
				},
				"type": "CaseManagement"
			}
		}
		self.request_to_aria('post', url, body)	

	def create_event(self, app_id, item_id, title, body=None, status=None):
		url = f'{self.base_url}/public/v1/apps/{app_id}/case_management_middleware/work_items/{item_id}/events'
		request_body = {
			"data": {
				"type": "event",
				"attributes": {
					"title": title
				}
			}
		}

		if body:
			request_body['data']['attributes']['body'] = body

		if status is not None:
			status_code = 'Completed' if status == 0 else ('Failed' if status == 1 else 'Warning')
			request_body['data']['attributes']['status'] = status_code

		self.request_to_aria('post', url, request_body)

	def delete_pending_invoice_item(self, app_id, item_id):
		url = f'{self.base_url}/public/v1/apps/{app_id}/case_management_middleware/work_items/{item_id}'
		self.request_to_aria('delete', url, {})

	def find_item(self, app_id, item_id):
		url = f'{self.base_url}/public/v1/apps/{app_id}/case_management_middleware/work_items/{item_id}'
		response = self.request_to_aria('get', url, {}, True)
		return response