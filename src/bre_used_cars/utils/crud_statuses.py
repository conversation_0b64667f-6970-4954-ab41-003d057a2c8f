# This module contains a class for interacting with the <PERSON><PERSON> collection in the MongoDB database.

import os
import datetime

from utils.mongo_utils import Mongo
from utils.boto3_utils import get_secret

"""
Handler Schema:
{
    "app_id": "string",
    "status": {
        "status_id": {
                "id": "status_id1",
                "label": "Status 1",
                "default": false,
                "read_only": true
        }
    }
    "updated_at": "datetime"
}
"""

class CrudStatuses:
    def __init__(self, mongo):
        self.mongo = mongo
        self.collection_name='aria_status'

    def insert_or_update(self, app_id, statuses):

        self.mongo.select_db_and_collection(os.environ['MONGO_DATABASE'], collection_name=self.collection_name)

        now = datetime.datetime.now()
        
        # Find in DB if exists and was not modified today
        # Create or update if not exist
        status_in_db = self.mongo.find_one({
            "app_id": app_id,
            "updated_at": {"$lt": now.replace(hour=0, minute=0, second=0, microsecond=0)}
        })

        if not status_in_db:
            self.mongo.insert_or_update(
                filter = {"app_id": app_id},
                data = {
                    "app_id": app_id,
                    "status": statuses,
                    "updated_at": now
                }
            )
    
    def get_statuses_by_app_id(self, app_id):
        self.mongo.select_db_and_collection(os.environ['MONGO_DATABASE'], collection_name=self.collection_name)
        return self.mongo.find_one({"app_id": app_id})