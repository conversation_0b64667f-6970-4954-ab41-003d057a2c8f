import json
import os
import boto3
import traceback
from datetime import datetime
from botocore.exceptions import ClientError
from botocore.config import Config
from mongo_utils import Mongo
from aria_utils import ARIA
from group_wrapper import Group_Wrapper
from llm_wrapper import LLM_Wrapper
from crud_report_rows import CrudReynolsReport

def check_if_valid(mongo_client, group, aria_wi_id, unique_id):
    """
    This method tries to find the case being processed in Mongo. If:
        - 1 entry -> OK
        - 0 entries -> It tries to find using a different unique combination. If:
            + 1 entry -> OK
            + Otherwise -> NOK
        - >1 entries -> NOK
    """
    error_message = ''

    print("CHECKING IF VALID", group, aria_wi_id, unique_id)

    # Unique combination for invoice
    filter = {"aria_wi_id": '', "vin": unique_id, "current_flow": "post-inventory"}
    mongo_client.select_db_and_collection(db_name=os.environ.get('MONGO_DATABASE'), collection_name="invoice")
 
    # Unique combination for bol
    if "bol" in group:
        filter = {"aria_wi_id": '', "attachment_id": unique_id}
        mongo_client.select_db_and_collection(db_name=os.environ.get('MONGO_DATABASE'), collection_name="bol")

    # Unique combination for title
    if "title" in group:
        filter = {"aria_wi_id": '', "title_id": unique_id}
        mongo_client.select_db_and_collection(db_name=os.environ.get('MONGO_DATABASE'), collection_name="title")

    entries_found = mongo_client.count_documents(data={"aria_wi_id": aria_wi_id})
    print("First entries found", entries_found)
    if entries_found == 0:
        
        entries_found = mongo_client.count_documents(data=filter)
        # If 1 
        if entries_found == 1:
            mongo_client.update_one(filter=filter, data={"$set": {"aria_wi_id": aria_wi_id, "updated_at": datetime.now()}})

        else:
            error_message = f'Number of entries found in the collection for this {filter} combination:' + str(entries_found)

    # wi repeated collection (this should not happen)
    elif entries_found > 1:
        error_message = 'Number of entries found in the collection for this aria_wi_id:' + str(entries_found)

    return error_message    
    

def get_secret(secret_name):
    region_name = "us-east-1"

    # Create a Secrets Manager client
    session = boto3.session.Session()
    client = session.client(
        service_name='secretsmanager',
        region_name=region_name
    )

    try:
        get_secret_value_response = client.get_secret_value(
            SecretId=secret_name
        )
    except ClientError as e:
        raise e

    secret = get_secret_value_response['SecretString']

    return secret  

def llm_extractor(event):
    try:
        execution_id = ''
        # Init mongo
        mongo_uri = get_secret(f'{os.environ["ENV"]}-mongodb_uri')
        mongo_client = Mongo(mongo_uri)
        mongo_client.select_db_and_collection(db_name=os.environ.get('MONGO_DATABASE'), collection_name=os.environ["LLM_EXTRACTOR_COLLECTION_NAME"])
        
        # Extracting data from input message
        now_in_ms = datetime.now()
        body = event
        print(body)
        document = body.get('document', None)
        action = body.get('action', None)
        status = body.get('status', None)
        ocr_groups = document.get('ocr_groups', None)
        #group_name = ocr_groups[0]
        ocr_file = body.get('words_coordinates', None)
        execution_id = body.get('execution_id', None)
        retrys = body.get('retrys')

        if "invoice" in ocr_groups[0]:
            crud_reynols_report = CrudReynolsReport()
            rows_report = document['groups'][ocr_groups[0]]['fields']['reynols_report']['rows'] or document['groups'][ocr_groups[0]]['fields']['reynols_report']['value']
            vin = ''
            for k, v in rows_report.items():
                if v['cells']['field']['value'] == 'VIN':
                    unique_id = vin = v['cells']['value']['value']
                    break

        if "bol" in ocr_groups[0]:
            unique_id = document['groups'][ocr_groups[0]]['fields']['unique_id']['value']
        
        if "title" in ocr_groups[0]:
            unique_id = document['groups'][ocr_groups[0]]['fields']['unique_id']['value']

    except Exception as e:
        print('Bad request ({}): {}'.format(e, event))
        mongo_client.insert_one(data={"execution_id": execution_id if execution_id else '', "time": now_in_ms.strftime("%m/%d/%Y-%H:%M:%S.%f"), "input": body, "success": False, "error_message": str(e)})
        mongo_client.close_connection()        
        return {
            'statusCode': 400,
            'body': json.dumps('Bad request!')
        }        
    
    try:
        
        # Checking if the current wi exists in data collection
        error_message = check_if_valid(mongo_client, ocr_groups[0], document['id'], unique_id)
        if error_message:
            print(error_message)
            mongo_client.close_connection()        
            return {
                'statusCode': 500,
                'body': json.dumps(error_message)
            }
        
        # Storing input in Mongo
        mongo_client.select_db_and_collection(db_name=os.environ.get('MONGO_DATABASE'), collection_name=os.environ["LLM_EXTRACTOR_COLLECTION_NAME"])
        mongo_client.insert_one(data={"execution_id": execution_id, "time": now_in_ms.strftime("%m/%d/%Y-%H:%M:%S.%f"), "input": body, "success": "", "error_message": ""})
        
        if "invoice" in ocr_groups[0]:
            crud_reynols_report.update_row_by_vin(vin=vin, data_to_set={"flows.post-inventory.status": 5, "flows.post-inventory.updated_at": datetime.now()}, data_to_push={"flows.post-inventory.status_history": 5})

        # Extracting llm params
        secret = json.loads(get_secret(secret_name=f'{os.environ["ENV"]}-llm_params'))
        llm_wrapper_gpt_mini = LLM_Wrapper(secret['openai']['public']['gpt4o3-mini']['llm_params'])
        llm_wrapper_main_llm = LLM_Wrapper(secret['openai']['public']['gpt4o']['llm_params'])
        llm_wrapper_aux1_llm = LLM_Wrapper(secret['bedrock']['claude']['claude3.7-sonnet']['llm_params'])
        llm_wrapper_aux2_llm = LLM_Wrapper(secret['bedrock']['claude']['claude3.5-sonnet']['llm_params'])

        # Generating the json with expected format for BRE
        bre_input_json = {}
        bre_input_json['execution_id'] = execution_id
        bre_input_json['action'] = action
        bre_input_json['status'] = status
        bre_input_json['document'] = document

        # Operations at group level
        wi_fields_json_clean = {}
        for group_name in ocr_groups:
            print(f'Processing group:{group_name}')

            group_name_wrapper = group_name

            if "invoice" in group_name:
                brand = document['groups'][group_name]['fields']['make']['value'].lower()
                group_name_wrapper = f'{brand}_{group_name}'

            group_wrapper = Group_Wrapper(group_name_wrapper)
            
            # Downloading ocr data
            link_to_download = ''
            if ocr_file.get(group_name, None):
                link_to_download = ocr_file.get(group_name, None)
            group_wrapper.download_ocr_data(link_to_download)

            # Classifying pages
            classify_needed = False
            group_wrapper.classify_pages(mongo_client, llm_wrapper_main_llm, execution_id, classify_needed = classify_needed)
            
            # Processing the document in blocks of pages (as per classification)
            bre_fields_json, extracted_data_clean = group_wrapper.process_group(llm_wrapper_main_llm, llm_wrapper_gpt_mini, llm_wrapper_aux1_llm, llm_wrapper_aux2_llm, mongo_client, execution_id)
            wi_fields_json_clean[group_name] = extracted_data_clean
            bre_input_json['document']['groups'][group_name]['fields'].update(bre_fields_json)


        if "bol" in group_name:
            wi_fields_json_clean['bol']['bol_vins'] = [v['vin'] for k,v in wi_fields_json_clean['bol']['bol_vins'].items()]
  

        mongo_client.select_db_and_collection(db_name=os.environ.get('MONGO_DATABASE'), collection_name=group_name)
        mongo_client.update_one(filter={"aria_wi_id": document['id']}, data={"$set": {"extracted_data": wi_fields_json_clean, "updated_at": datetime.now()}})

        
        # Invoking BRE lambda
        config = Config(
            read_timeout=100,
            retries = {
                'max_attempts': 10,
                'mode': 'standard'
                }
        )
        print('===============================')
        print(bre_input_json)
        print('===============================')
        client = boto3.client('lambda', region_name="us-east-1", config=config)
        response = None
        
        response = client.invoke(
            FunctionName=f'{os.environ["BRE_LAMBDA"]}',
            InvocationType='RequestResponse',
            Payload=json.dumps(bre_input_json)
        )
        

        print(response)

        # Extracting BRE's reply
        payload = json.loads(response["Payload"].read()) 
        if payload['statusCode'] == 200:
            bre_fields_json = json.loads(payload['body'])
        else:
            print('Issue calling BRE:' + str(payload.get('statusCode')))    

        mongo_client.close_connection()        
    
        return {
            'statusCode': 200,
            'body': json.dumps('Ok!')
        }
    
    except Exception as e:
        print('Error: {}'.format(traceback.format_exc()))
        aria_exception = ""
        bre_response = False
        if "Please reduce the length of the messages" in str(e):
            aria_exception = 'File too long to process'
            bre_response = True 
        else:
            aria_exception = 'Exception extracting data'
            if retrys <= 0:
                bre_response = True
            else:
                return {
                    'statusCode': 404,
                    'body': json.dumps('LLM Error!')
                }
    

        if bre_response:
            try:
                # Extracting target status id
                for k,v in status.items():
                    if 'needs' in v['label'].lower():
                        target_status_label = v['label']
                        target_status_id = k
                        break
                try:
                    # Updating folios and llm_extractor collections with error
                    mongo_client.select_db_and_collection(db_name=os.environ.get('MONGO_DATABASE'), collection_name=os.environ["LLM_EXTRACTOR_COLLECTION_NAME"])            
                    mongo_client.update_one(filter={'execution_id': execution_id}, data={"$set": {"success": False, "error_message": str(traceback.format_exc())}})
                except:
                    pass
                mongo_client.select_db_and_collection(db_name=os.environ.get('MONGO_DATABASE'), collection_name=ocr_groups[0])
                note = datetime.now().strftime("%m/%d/%Y-%H:%M:%S") + '\n' + aria_exception
                mongo_client.update_one(
                    filter={"aria_wi_id": document['id']},
                    data={
                        "$set": {
                            "status": target_status_label,
                            "updated_at": datetime.now(),
                            "exception": aria_exception
                        },
                        "$push": {
                            "status_history": target_status_label
                        }
                    }
                )

                # Send reply back to ARIA
                
                secret = get_secret(secret_name=f'{os.environ["ENV"]}-aria_cm_tokens')
                secret = json.loads(secret)
                aria = ARIA(base_url=secret[os.environ.get('aria_env')]['url'], request_token=secret[os.environ.get('aria_env')]['token'])
                aria.bre_reply(
                    app_id=document['app_id'],
                    item_id=document['id'],
                    bre_response={
                        "aria_status":{"value": target_status_id},
                        "aria_exception":{"value": aria_exception}
                    })
                # Insert event in ARIA
                aria.create_event(
                    app_id=document['app_id'],
                    item_id=document['id'],
                    title=aria_exception,
                    status=1
                )
            
                mongo_client.close_connection()

                

            except Exception as e:
                print('Error: {}'.format(traceback.format_exc()))
            
            return {
                'statusCode': 500,
                'body': json.dumps('Nok!')
            }

def lambda_handler(event, context):
    retrys = int(os.getenv("RETRYS", 3))
    finished_by_no_problem_with_llm = False

    while(finished_by_no_problem_with_llm == False and retrys > 0):
        event['retrys'] = retrys
        response = llm_extractor(event)
        if response['statusCode'] != 404:
            finished_by_no_problem_with_llm = True
        
        retrys -= 1

    return response