fields = {
    "lexus_invoice":{
        "Total Dealer Invoice": "<Total dealer invoice amount. It's the last number that can be found next to 'TOTAL INVOICE'.>",
        "Wholesale Finance Reserve": "<Wholesale Finance Reserve amount.>",
        "VIN": "<17-character Vehicle Identification Number (VIN). Bear in mind that VIN numbers don’t have 'O', 'I', and 'Q' letters to avoid confusing them with '0', '1', and '9'. Shown as 'SERIAL NO' in the document.>",
        "Model code": "<Alphanumeric car model code. Shown as 'MODEL CODE' in the document.>",
        "Model description": "<Car model. Shown as 'DESCRIPTION' in the document.>",
        "Sold to": "<Dealer shop address. Shown below 'SOLD TO' in the document.>",
        "Delivery Charge": "<Delivery processing and handling fee amount. It's the last number that can be found next to 'DPH FEE'.>",
        "Invoice Date": "<The date when the invoice was issued. Should be at the start of the document>",
        "LCCS Car": "<Its a boolean, If in the OCR exists the word '(LCCS)' your response will be true otherwise your response will be false.>",
        },
    "jaguar_invoice":{
        "Total Dealer Invoice": "<Dealer invoice total amount. It's the last number that can be found in the 'TOTAL' line of the prices table.>",
        "VIN": "<17-character Vehicle Identification Number (VIN). Bear in mind that VIN numbers don’t have 'O', 'I', and 'Q' letters to avoid confusing them with '0', '1', and '9'. Shown as 'Vehicle number' in the document.>",
        "Model code": "<Alphanumeric car model code. Shown as 'Model Code' in the document.>",
        "Model description": "<Car model. Shown as 'Description' in the document.>",
        "Sold to": "<Dealer shop address. Shown below 'SOLD TO' in the document. Extract only the address (Street Address, City, State and ZIP code)>",
        "Delivery Charge": "<Delivery processing and handling fee amount. It's the last number that can be found in the 'Dest. & Delivery' line of the prices table.>",
        "Invoice Date": "<The date when the invoice was issued. Should be at the start of the document.  I need it in american format that is MM/DD/YYYY (Ex: January 14, 2025 --> 01/14/2025))>"
        },
    "cadillac_invoice":{
        "Model code": "<Alphanumeric car model code. Its the first value after the line that has all the columns of the table, starting with 'MODEL & FACTORY OPTIONS' its also before the model description on the next line>",
        "Model description": "<Car model. It can be found in the first line of the document, before to 'GENERAL MOTORS LLC'.>",
        "VIN": "<17-character Vehicle Identification Number (VIN). Bear in mind that VIN numbers don’t have 'O', 'I', and 'Q' letters to avoid confusing them with '0', '1', and '9'. Shown as 'VIN' in the document. Its located between the words 'VIN' and 'VEHICLE INVOICE', so take all the text between that two words.>",
        "Delivery Charge": "<Delivery processing and handling fee amount. It's the second number that can be found in the line starting by 'DESTINATION CHARGE'. Should be two values so take the second value>",
        "Total Dealer Invoice": "<Total dealer invoice amount. It's the last number that can be found in the line starting by 'TOTAL' (ignore the values after the words 'MEMO: TOTAL'). After the 'TOTAL' word you will find 5 values with the structure ( VALUE_1 VALUE_2 PAY/PY_VALUE_3 VALUE_4 VALUE_5 an example of the entire line: TOTAL 1237.21 2390.12 PAY 123 11237.00 fot that example we want 11237.00 the VALUE_5). Take the fift'th value which is the total dealer invoice amount>",        "Invoice Date": "<The date when the invoice was issued. Should be near the word 'INVOICE'>",
        "Electric Vehicle": "<Determine if the vehicle is fully electric, if the vehicle is 100% electric say 'true', if it is a hybrid, gasoline, diesel, or any other non-electric vehicle say 'false'. You can get if a vehicle is non electric cause in the invoice usually are the specifications of the engine like 'V8' and the liters that it consumes '5.0L'>",
        "MSRP Amount": "<Extract the MSRP (Manufacturer’s Suggested Retail Price). MSRP is a price listed under the column heading “MSRP”. Its placed in the same line after the model code and model description. The first value.>",
        },
    "lincoln_invoice":{
        "Model code": "<Alphanumeric car model code. It can be found in the first line of the table of the document. Could not exists, but if exists is a combination of chars numbers and maybe symbols. Its not on the line of that starts with 'DEALER'>",
        "Model description": "<Car model. It can be found in the first line of the table of the document. At the right of the model code.>",
        "VIN": "<17-character Vehicle Identification Number (VIN). Bear in mind that VIN numbers don’t have 'O', 'I', and 'Q' letters to avoid confusing them with '0', '1', and '9'. Shown as 'Invoice & Unit Identification NO.' in the document.>",
        "Total Dealer Invoice": "<Total dealer invoice amount. Shown as 'Invoice Total' in the document. Its at the end of the document.>",
        "Total Vehicle & Options":"<Total Vehicle & Options/Other amount. Focus on the line starting by 'TOTAL VEHICLE & OPTIONS/OTHER' and extract the first number. This number will be found just before '00', so use this as delimiter.>",
        "Sold to": "<Dealer shop address. Shown below 'SOLD TO' in the document. Extract only the address (Street Address, City, State and ZIP code)>",
        "Delivery Charge": "<Delivery processing and handling fee amount. The value is the inmmediate first value of that follows to the words 'DESTINATION & DELIVERY' (EXTRACT THE FIRST AND ONLY THE FIRST VALUE if there is two get the FIRST). If the result its divided into 2 parts, merge them and return it as a float in python like 'NNNN.DD'>",
        "Invoice Date": "<The date when the invoice was issued. Shown as 'Date Inv. Prepared'. It will be in three blocks as follows 'MM DD YY'. I need it in american format that is MM/DD/YYYY (Ex: 03 23 25 --> 03/23/2025))>"
        },
    "landrover_invoice":{
        "Model code": "<Alphanumeric car model code. Shown as 'Model Code' in the document.>",
        "Model description": "<Car model. Shown as 'Description' in the document.>",
        "VIN": "<17-character Vehicle Identification Number (VIN). Bear in mind that VIN numbers don’t have 'O', 'I', and 'Q' letters to avoid confusing them with '0', '1', and '9'. Shown as 'Vehicle Number' in the document.>",
        "Total Dealer Invoice": "<Total dealer invoice amount. It's the last number that can be found in the 'TOTAL' line of the prices table.>",
        "Sold to": "<Dealer shop address. Shown below 'SOLD TO' in the document. Extract only the address (Street Address, City, State and ZIP code)>",
        "Delivery Charge": "<Delivery processing and handling fee amount. It's the last number that can be found in the 'Dest. & Delivery' line of the prices table.>",
        "Invoice Date": "<The date when the invoice was issued. Should be at the start of the document.  I need it in american format that is MM/DD/YYYY (Ex: January 14, 2025 --> 01/14/2025))>"
        },
    "porsche_invoice":{
        "Model code": "<Alphanumeric car model code. Shown as 'MTYP' in the document.>",
        "Model description": "<Car model. It can be found in the first line of the prices table.>",
        "VIN": "<17-character Vehicle Identification Number (VIN). Bear in mind that VIN numbers don’t have 'O', 'I', and 'Q' letters to avoid confusing them with '0', '1', and '9'. Shown as 'VIN' in the document.>",
        "Total Dealer Invoice": "<Total dealer invoice amount. Shown as 'Total Dealer Gross Price' in the document.>",
        "Domestic Wholesale": "<Domestic wholesale amount of the car model. It can be found in the first line of the prices table on the page that has in 'Wholesale Price'>",
        "Retail Amount": "<Retail amount of the car model. It can be found in the first line of the prices table on the page that has in 'Retail Price'>",
        "Sold to": "<Dealer shop address. Shown below 'SOLD TO' in the document. Extract only the address (Street Address, City, State and ZIP code)>",
        "Delivery Charge": "<Delivery processing and handling fee amount. Shown as 'Delivery, Processing and Handling Fee' in the document.>",
        "Cocar": "<Its a boolean, If in the OCR exists the words 'Cocar Discount' your response will be true, if not false>",
        "Invoice Date": "<The date when the invoice was issued. The document is structured on Bill sheet - Retail and at the end the Invoice pages. Get the invoice date that should be at the top of the document of the pages that are from the invoice NOT take the date from the pages of the Bill sheet.>",
        "porsche_experience_delivery": "<Its a boolean, If in the OCR exists the phrase 'Porsche Experience Center Delivery' your response will be true, if not false>",
        },
    "honda_invoice":{
        "Model code": "<Alphanumeric car model code. It can be found in the line starting by 'CONTROL#' just before the 'SHIP#'.>",
        "Model description": "<Car model. It can be found in the first line of the prices table.>",
        "VIN": "<17-character Vehicle Identification Number (VIN). Bear in mind that VIN numbers don’t have 'O', 'I', and 'Q' letters to avoid confusing them with '0', '1', and '9'. Shown as 'VIN#' in the document.>",
        "Total Dealer Invoice": "<Total dealer invoice amount. Shown as 'Invoice Total' in the document.>",
        "Hold Back": "Identify the line that starts with 'MSRP'. The expected format is: 'MSRP $amount code/5000 hold_back_amount'. Both 'code/5000' and 'hold_back_amount' must be present in this exact structure. If the line follows 'MSRP $amount 5000', no hold_back_amount exists. Extract the hold_back_amount only if all elements are present. Return NONE if the line doesn't exist or any part is missing.",
        "Sold to": "<Dealer shop address. Shown below 'SOLD TO' in the document. Extract only the address (Street Address, City, State and ZIP code)>",
        "Delivery Charge": "<Delivery processing and handling fee amount. Shown as 'DESTINATION AND HANDLING CHARGE' in the document.>",
        "Invoice Date": "<The date when the invoice was issued. Should be at the start of the document>",
        "Color upcharge fee": "<Its a boolean, If in the OCR exists the words 'COLOR UPCHRG FEE' the vehicle is premium and your response will be true, if not false>"
        },
    "gm_invoice":{
        "Model code": "<Alphanumeric car model code.>",
        "Model description": "<Car model. It can be found in the first line of the document.>",
        "VIN": "<17-character Vehicle Identification Number (VIN). Bear in mind that VIN numbers don’t have 'O', 'I', and 'Q' letters to avoid confusing them with '0', '1', and '9'. Shown as 'VIN' in the document.>",
        "Total Dealer Invoice": "<Total dealer invoice amount. It's the last number that can be found in the line starting by 'TOTAL' (just before 'MEMO').>",
        "Hold Back": "<Holdback amount. Shown as 'H/B' in the document.>",
        "Sold to": "<Dealer shop address. Shown below 'SOLD TO' in the document. Extract only the address (Street Address, City, State and ZIP code)>",
        "Delivery Charge": "<Delivery processing and handling fee amount. It's the last number that can be found in the line starting by 'DESTINATION CHARGE'.>",
        "Invoice Date": "<The date when the invoice was issued. Should be at the start of the document>"
        },
    "ford_invoice":{
        "Model code": "<Alphanumeric car model code. It can be found in the first line of the table of the document. Could not exists, but if exists is a combination of chars numbers and maybe symbols. Its not on the line of that starts with 'DEALER'>",
        "Model description": "<Car model. It can be found in the first line of the table of the document. At the right of the model code.>",
        "VIN": "<17-character Vehicle Identification Number (VIN). Bear in mind that VIN numbers don’t have 'O', 'I', and 'Q' letters to avoid confusing them with '0', '1', and '9'. Shown as 'Invoice & Unit Identification NO.' in the document.>",
        "Total Dealer Invoice": "<Total dealer invoice amount. Shown as 'Invoice Total' in the document.>",
        "Hold Back": "<Holdback amount. Shown as 'HB' in the document, just before the 'Invoice Total'. Its the first immediate number found after the labels of the table where its containing 'HB'. It will be formatted as an integer amount, with NO decimals (for instance: '546')>",
        "Total Vehicle & Options":"<Total Vehicle & Options/Other amount. Focus on the line starting by 'TOTAL VEHICLE & OPTIONS/OTHER' and extract the first number. This number will be found just between 'TOTAL VEHICLE & OPTIONS/OTHER' and '00', so use this as delimiter.>",
        "Sold to": "<Dealer shop address. Shown below 'SOLD TO' in the document. Extract only the address (Street Address, City, State and ZIP code)>",
        "Delivery Charge": "<Delivery processing and handling fee amount. The value is the inmmediate first value that follows to the words 'DESTINATION & DELIVERY' (EXTRACT THE FIRST AND ONLY THE FIRST VALUE if there is two get the FIRST). If the result its divided into 2 parts, merge them and return it as a float in python like 'NNNN.DD'>",
        "Invoice Date": "<The date when the invoice was issued. Shown as 'Date Inv. Prepared'. It will be in three blocks as follows 'MM DD YY'. I need it in american format that is MM/DD/YYYY (Ex: 03 23 25 --> 03/23/2025))>"
        },
    "mazda_invoice":{
        "Model code": "<Car model. Shown as 'MODEL' in the document.>",
        "VIN": "<17-character Vehicle Identification Number (VIN). Bear in mind that VIN numbers don’t have 'O', 'I', and 'Q' letters to avoid confusing them with '0', '1', and '9'. Shown as 'VIN' in the document.>",
        "Total Dealer Invoice": "<Total dealer invoice amount. The line starting by 'Total Invoice Amount' contains 2 numbers, extract the second one (which is the highest one also).>",
        "Hold Back": "<Holdback amount. It can be found starting by DH as a single word. For instance: 'DH000313'.>",
        "Retail Amount": "<Total Retail amount. Shown as 'RETAIL AMT' in the document. It can be found just before the 'EXTERIOR' details.>",
        "Sold to": "<Dealer shop address. Extract only the address (Street Address, City, State and ZIP code)>",
        "Delivery Charge": "<Delivery processing and handling fee amount. The line starting by 'TRANSPORTATION' contains 2 numbers, extract the second one.>",
        "Invoice Date": "<The date when the invoice was issued. Should be at the start of the document>"
        },
    "title": {
        "VIN": "<17-character Vehicle Identification Number (VIN). Bear in mind that VIN numbers don’t have 'O', 'I', and 'Q' letters to avoid confusing them with '0', '1', and '9'. Shown as 'Invoice & Unit Identification NO.' in the document.>",
        "Make": "Identify the value corresponding to the ‘Make’ field. The ‘Make’ represents the manufacturer of the vehicle, such as Toyota, Lamborghini, Seat, etc."
    },
    "bol":{
        "BOL Date": "Its the date when the bol was signed. I need it in american format that is MM/DD/YYYY (Ex: Jan 13, 2025 --> 01/13/2025)",
        "BOL VINs": """Extract a list of **valid** Vehicle Identification Numbers (VINs) from the document.  

                    **Rules:**  
                    - A **VIN must be exactly 17 characters long** and consist of only **A-Z** (excluding 'I', 'O', and 'Q') and **0-9**.  
                    - **Remove duplicate VINs** while ensuring each extracted VIN appears in its correct order.  
                    - **Handle split VINs**: If the same VIN appears on multiple lines, it may be duplicated in pairs. In such cases, retain only the version that appears later in the document.  
                    - **Handle extra characters**: If a VIN has **more than 17 characters**, check if the extra characters are a **four-digit year (e.g., "2020", "2021")**. If so, remove the year and keep only the valid 17-character VIN. Also if you got a VIN that is in another one ignore it cause it could be incompleted  
                    - **Ensure completeness**: If a VIN is **shorter than 17 characters**, discard it as it is likely an OCR extraction error.  

                    Return only the **final list of valid, unique, and correctly formatted VINs**, separated by commas. Do not include any additional text or explanations.  """,
        "Dates": "Its a list of dates (all the dates that are found in the document and I need it in american format that is MM/DD/YYYY (Ex: Jan 13, 2025 --> 01/13/2025)) separated each one by commas. Take in mind not to extract only years. I want only dates. Also add the one that you use in the BOL Date field.",   
        "Address": "Identify and extract the 'Deliver To' address from the given text. The address typically follows 'Deliver To:', 'Consignee:', or similar labels. Return only the address."

    }
}

fields_json = {
    "lexus_invoice":{
        "Total Dealer Invoice": "95,389.98 [Id: 'unique_id_1']",
        "Wholesale Finance Reserve": "$2,161.00 [Id: 'unique_id_2']",
        "VIN": "JTHGZ1B27S5082852[Id: 'unique_id_3']",
        "Model code": "9246A [Id: 'unique_id_4']",
        "Model description": "RX [Id: 'unique_id_5'] 350h [Id: 'unique_id_6'] AWD [Id: 'unique_id_7']",
        "Sold to": "1234 [Id: 'unique_id_8'] Fake [Id: 'unique_id_9'] Avenue [Id: 'unique_id_10'] Atlanta [Id: 'unique_id_11'] GA [Id: 'unique_id_12'] 30342 [Id: 'unique_id_13']",
        "Delivery Charge": "1,250.00 [Id: 'unique_id_14']",
        "Invoice Date": "01/23/2025 [Id: 'unique_id_15']",
        "LCCS Car": "<true or false> [Id: 'unique_id_16']",
        },
    "jaguar_invoice":{
        "Total Dealer Invoice": "63,259.36 [Id: 'unique_id_1']",
        "VIN": "SADCT2EX3SA753934 [Id: 'unique_id_2']",
        "Model code": "5002259853 [Id: 'unique_id_3']",
        "Model description": "25.5MY [Id: 'unique_id_4'] F-PACE [Id: 'unique_id_5'] R-Dynamic [Id: 'unique_id_6'] S [Id: 'unique_id_7'] 250PS [Id: 'unique_id_8'] AWD [Id: 'unique_id_9'] HB761/352KU [Id: 'unique_id_10']",
        "Sold to": "1234 [Id: 'unique_id_11'] Fake [Id: 'unique_id_12'] Avenue [Id: 'unique_id_13'] Atlanta [Id: 'unique_id_14'] GA [Id: 'unique_id_15'] 30342 [Id: 'unique_id_16']",
        "Delivery Charge": "1,250.00 [Id: 'unique_id_17']",
        "Invoice Date": "01/23/2025 [Id: 'unique_id_18']"
        },
    "cadillac_invoice":{
        "Model code": "6MC58 [Id: 'unique_id_1']",
        "Model description": "2025 [Id: 'unique_id_2'] XT6 [Id: 'unique_id_3'] SPORT [Id: 'unique_id_4'] AWD [Id: 'unique_id_5']",        
        "VIN": "1GY S9RR L3 SR169853 [Id: 'unique_id_6']",
        "Total Dealer Invoice": "66367.98 [Id: 'unique_id_7']",
        "Delivery Charge": "1250.00 [Id: 'unique_id_8']",
        "Invoice Date": "01/23/2025 [Id: 'unique_id_9']",
        "Electric Vehicle": "true",
        "MSRP Amount": "14123.00 [Id: 'unique_id_10']"
        },
    "lincoln_invoice":{
        "Model code": "J7XF [Id: 'unique_id_1']",
        "Model description": "NAUTILUS [Id: 'unique_id_2'] PREMIER [Id: 'unique_id_3'] AWD [Id: 'unique_id_4']",
        "VIN": "5LM6J8XC8SGL58968 [Id: 'unique_id_5']",
        "Total Dealer Invoice": "75951.00 [Id: 'unique_id_6']",
        "Sold to": "1234 [Id: 'unique_id_7'] Fake [Id: 'unique_id_8'] Avenue [Id: 'unique_id_9'] Atlanta [Id: 'unique_id_10'] GA [Id: 'unique_id_11'] 30342 [Id: 'unique_id_12']",
        "Delivery Charge": "1550.00 [Id: 'unique_id_13']",
        "Invoice Date": "01/23/2025 [Id: 'unique_id_14']",
        "Total Vehicle & Options": "8330 [Id: 'unique_id_15']",
        },
    "landrover_invoice":{
        "Model code": "5002211955 [Id: 'unique_id_1']",
        "Model description": "25MY [Id: 'unique_id_2'] Range [Id: 'unique_id_3'] Rover [Id: 'unique_id_4'] Autobiography [Id: 'unique_id_5'] (AB460/460BB/4.4L/530PS) [Id: 'unique_id_6']",
        "VIN": "SAL1L9E99SA3690895 [Id: 'unique_id_7']",
        "Total Dealer Invoice": "198,187.55 [Id: 'unique_id_8']",
        "Sold to": "1234 [Id: 'unique_id_9'] Fake [Id: 'unique_id_10'] Avenue [Id: 'unique_id_11'] Atlanta [Id: 'unique_id_12'] GA [Id: 'unique_id_13'] 30342 [Id: 'unique_id_14']",
        "Delivery Charge": "1550.00 [Id: 'unique_id_15']",
        "Invoice Date": "01/23/2025 [Id: 'unique_id_16']"
        },
    "porsche_invoice":{
        "Model code": "992458 [Id: 'unique_id_1']",
        "Model description": "911 [Id: 'unique_id_2'] GT3 [Id: 'unique_id_3'] RS [Id: 'unique_id_4']",
        "VIN": "WP0AF2A95SS178842 [Id: 'unique_id_5']",
        "Total Dealer Invoice": "295,198.00 [Id: 'unique_id_6']",
        "Domestic Wholesale": "216,300.00 [Id: 'unique_id_7']",
        "Retail Amount": "[Id: 'unique_id_8']",
        "Sold to": "1234 [Id: 'unique_id_9'] Fake [Id: 'unique_id_10'] Avenue [Id: 'unique_id_11'] Atlanta [Id: 'unique_id_12'] GA [Id: 'unique_id_13'] 30342 [Id: 'unique_id_14']",
        "Delivery Charge": "1550.00 [Id: 'unique_id_15']",
        "Cocar": "<true or false> [Id: 'unique_id_16']",
        "Invoice Date": "01/23/2025 [Id: 'unique_id_17']",
        "porsche_experience_delivery": "false"
        },
    "honda_invoice":{
        "Model code": "RZ1H4JXW [Id: 'unique_id_1']",
        "Model description": "HONDA [Id: 'unique_id_2'] PILOT [Id: 'unique_id_3'] AWD [Id: 'unique_id_4'] ELITE [Id: 'unique_id_5']",
        "VIN": "3CZRZ1H35SM711586 [Id: 'unique_id_6']",
        "Total Dealer Invoice": "28,157.40 [Id: 'unique_id_7']",
        "Hold Back": "108850 [Id: 'unique_id_8']",
        "Sold to": "1234 [Id: 'unique_id_9'] Fake [Id: 'unique_id_10'] Avenue [Id: 'unique_id_11'] Atlanta [Id: 'unique_id_12'] GA [Id: 'unique_id_13'] 30342 [Id: 'unique_id_14']",
        "Delivery Charge": "1550.00 [Id: 'unique_id_15']",
        "Invoice Date": "01/23/2025 [Id: 'unique_id_16']",
        "Color upcharge fee": "<true or false> [Id: 'unique_id_17']"
        },
    "gm_invoice":{
        "Model code": "4TS65 [Id: 'unique_id_1']",
        "Model description": "2025 [Id: 'unique_id_2'] ENVISTA [Id: 'unique_id_3'] AVENIR [Id: 'unique_id_4']",
        "VIN": "3GK7LCEPGXB152335 [Id: 'unique_id_5']",
        "Total Dealer Invoice": "28,157.40 [Id: 'unique_id_6']",
        "Hold Back": "923.70 [Id: 'unique_id_7']",
        "Sold to": "1234 [Id: 'unique_id_8'] Fake [Id: 'unique_id_9'] Avenue [Id: 'unique_id_10'] Atlanta [Id: 'unique_id_11'] GA [Id: 'unique_id_12'] 30342 [Id: 'unique_id_13']",
        "Delivery Charge": "1550.00 [Id: 'unique_id_14']",
        "Invoice Date": "01/23/2025 [Id: 'unique_id_15']"
        },
    "ford_invoice":{
        "Model code": "F15F [Id: 'unique_id_1']",
        "Model description": "4X4 [Id: 'unique_id_2'] PLATINUM [Id: 'unique_id_3'] - [Id: 'unique_id_4'] 145 [Id: 'unique_id_5']",
        "VIN": "1FTFW7L8XSFA29024 [Id: 'unique_id_6']",
        "Total Dealer Invoice": "79816.40 [Id: 'unique_id_7']",
        "Hold Back": "1667 [Id: 'unique_id_8']",
        "Total Vehicle & Options": "8330 [Id: 'unique_id_9']",
        "Sold to": "1234 [Id: 'unique_id_9'] Fake [Id: 'unique_id_10'] Avenue [Id: 'unique_id_11'] Atlanta [Id: 'unique_id_12'] GA [Id: 'unique_id_13'] 30342 [Id: 'unique_id_14']",
        "Delivery Charge": "1550.00 [Id: 'unique_id_15']",
        "Invoice Date": "01/23/2025 [Id: 'unique_id_16']"
        },
    "mazda_invoice":{
        "Model code": "C30 [Id: 'unique_id_1'] 25S [Id: 'unique_id_2'] XA [Id: 'unique_id_3']",
        "VIN": "JM3DMBAM2SM798875 [Id: 'unique_id_4']",
        "Total Dealer Invoice": "79816.00 [Id: 'unique_id_5']",
        "Hold Back": "313 [Id: 'unique_id_6']",
        "Retail Amount": "76330.00 [Id: 'unique_id_7']",
        "Sold to": "1234 [Id: 'unique_id_8'] Fake [Id: 'unique_id_9'] Avenue [Id: 'unique_id_10'] Atlanta [Id: 'unique_id_11'] GA [Id: 'unique_id_12'] 30342 [Id: 'unique_id_13']",
        "Delivery Charge": "1550.00 [Id: 'unique_id_14']",
        "Invoice Date": "01/23/2025 [Id: 'unique_id_15']"
        },
    "title":{
        "VIN": "JM3DMBAM2SM798875 [Id: 'unique_id_1']",
        "Make": "CHEVROLET [Id: 'unique_id_2']",
    },
    "bol":{
        "BOL Date": "01/14/2025 [Id: 'unique_id_1']",
        "BOL VINs": "JM3DMBAM2SM798875 [Id: 'unique_id_2'], JM3DMBAM2SX798873 [Id: 'unique_id_3'], JM2DKADM2SX797873 [Id: 'unique_id_4']",
        "Dates": "01/14/2024 [Id: 'unique_id_5'], 01/15/2025 [Id: 'unique_id_6'], 02/05/2025 [Id: 'unique_id_7']",
        "Address": "1234 [Id: 'unique_id_8'] Fake [Id: 'unique_id_9'] Avenue [Id: 'unique_id_10'] Atlanta [Id: 'unique_id_11'] GA [Id: 'unique_id_12'] 30342 [Id: 'unique_id_13']",
    }
}

fields_json_v2 = {
    "lexus_invoice":{
        "Total Dealer Invoice": "95,389.98",
        "Wholesale Finance Reserve": "$2,161.00",
        "VIN": "JTHGZ1B27S5082852",
        "Model code": "9246A",
        "Model description": "RX 350h AWD",
        "Sold to": "1234 Fake Avenue Atlanta GA 30342",
        "Delivery Charge": "1,250.00",
        "Invoice Date": "01/23/2025",
        "LCCS Car": "<true or false>",
        },
    "jaguar_invoice":{
        "Total Dealer Invoice": "63,259.36",
        "VIN": "SADCT2X3SA753934",
        "Model code": "5002259853",
        "Model description": "25.5MY F-PACE R-Dynamic S 250PS AWD HB761/352KU",
        "Sold to": "1234 Fake Avenue Atlanta GA 30342",
        "Delivery Charge": "1,250.00",
        "Invoice Date": "01/23/2025"
        },
    "cadillac_invoice":{
        "Model code": "6MC58",
        "Model description": "2025 XT6 SPORT AWD",        
        "VIN": "1GY S9RR L3 SR169853",
        "Total Dealer Invoice": "66367.98",
        "Delivery Charge": "1250.00",
        "Invoice Date": "01/23/2025",
        "Electric Vehicle": "true",
        "MSRP Amount": "14123.00"
        },
    "lincoln_invoice":{
        "Model code": "J7XF",
        "Model description": "NAUTILUS PREMIER AWD",
        "VIN": "5LM6J8XC8SGL58968",
        "Total Dealer Invoice": "75951.00",
        "Total Vehicle & Options": "8330",
        "Sold to": "1234 Fake Avenue Atlanta GA 30342",
        "Delivery Charge": "1550.00",
        "Invoice Date": "01/23/2025"
        },
    "landrover_invoice":{
        "Model code": "5002211955",
        "Model description": "25MY Range Rover Autobiography (AB460/460BB/4.4L/530PS)",
        "VIN": "SAL1L9E99SA3690895",
        "Total Dealer Invoice": "198,187.55",
        "Sold to": "1234 Fake Avenue Atlanta GA 30342",
        "Delivery Charge": "1550.00",
        "Invoice Date": "01/23/2025"
        },
    "porsche_invoice":{
        "Model code": "992458",
        "Model description": "911 GR3 RS",
        "VIN": "WP0AF2A95SS178842",
        "Total Dealer Invoice": "295,198.00",
        "Domestic Wholesale": "216,300.00",
        "Retail Amount": "76330.00",
        "Sold to": "1234 Fake Avenue Atlanta GA 30342",
        "Delivery Charge": "1550.00",
        "Cocar": "<true or false>",
        "Invoice Date": "01/23/2025",
        "porsche_experience_delivery": "false"
        },
    "honda_invoice":{
        "Model code": "RZ1H4JXW",
        "Model description": "HONDA PILOT AWD ELITE",
        "VIN": "3CZRZ1H35SM711586",
        "Total Dealer Invoice": "28,157.40",
        "Hold Back": "108850",
        "Sold to": "1234 Fake Avenue Atlanta GA 30342",
        "Delivery Charge": "1550.00",
        "Invoice Date": "01/23/2025",
        "Color upcharge fee": "<true or false>"
        },
    "gm_invoice":{
        "Model code": "4TS65",
        "Model description": "2025 ENVISTA AVENIR",
        "VIN": "3GK7LCEPGXB152335",
        "Total Dealer Invoice": "28,157.40",
        "Hold Back": "923.70",
        "Sold to": "1234 Fake Avenue Atlanta GA 30342",
        "Delivery Charge": "1550.00",
        "Invoice Date": "01/23/2025"
        },
    "ford_invoice":{
        "Model code": "F15F",
        "Model description": "4X4 PLATINUM - 145",
        "VIN": "1FTFW7L8XSFA29024",
        "Total Dealer Invoice": "79816.40",
        "Hold Back": "1667",
        "Total Vehicle & Options": "8330",
        "Sold to": "1234 Fake Avenue Atlanta GA 30342",
        "Delivery Charge": "1550.00",
        "Invoice Date": "01/23/2025"
        },
    "mazda_invoice":{
        "Model code": "C30 25S XA",
        "VIN": "JM3DMBAM2SM798875",
        "Total Dealer Invoice": "79816.00",
        "Hold Back": "313",
        "Retail Amount": "76330.00",
        "Sold to": "1234 Fake Avenue Atlanta GA 30342",
        "Delivery Charge": "1550.00",
        "Invoice Date": "01/23/2025"
        },
    "title":{
        "VIN": "JM3DMBAM2SM798875",
        "Make": "CHEVROLET",
    },
    "bol":{
        "BOL Date": "01/14/2025",
        "BOL VINs": "JM3DMBAM2SM798875, JM3DMBAM2SX798873, JM2DKADM2SX797873",
        "Dates": "01/14/2024, 01/15/2025, 02/05/2025",
        "Address": "1234 Fake Avenue Atlanta GA 0342",
    }      
}

fields_type = {
    "lexus_invoice":{
        "Total Dealer Invoice": {"type": "regular"},
        "Wholesale Finance Reserve": {"type": "regular"},
        "VIN": {"type": "regular"},
        "Model code": {"type": "regular"},
        "Model description": {"type": "regular"},
        "Sold to": {"type": "regular"},
        "Delivery Charge": {"type": "regular"},
        "Invoice Date":  {"type": "regular"},
        "LCCS Car": {"type": "regular"}
        },
    "jaguar_invoice":{
        "Total Dealer Invoice": {"type": "regular"},
        "VIN": {"type": "regular"},
        "Model code": {"type": "regular"},
        "Model description": {"type": "regular"},
        "Sold to": {"type": "regular"},
        "Delivery Charge": {"type": "regular"},
        "Invoice Date":  {"type": "regular"}
        },
    "cadillac_invoice":{
        "Model code": {"type": "regular"},
        "Model description": {"type": "regular"},
        "VIN": {"type": "regular"},
        "Total Dealer Invoice": {"type": "regular"},
        "Delivery Charge": {"type": "regular"},
        "Invoice Date":  {"type": "regular"},
        "Electric Vehicle": {"type": "regular"},
        "MSRP Amount": {"type": "regular"}
        },
    "lincoln_invoice":{
        "Model code": {"type": "regular"},
        "Model description": {"type": "regular"},
        "VIN": {"type": "regular"},
        "Total Dealer Invoice": {"type": "regular"},
        "Total Vehicle & Options": {"type": "regular"},
        "Sold to": {"type": "regular"},
        "Delivery Charge": {"type": "regular"},
        "Invoice Date":  {"type": "regular"}
        },
    "landrover_invoice":{
        "Model code": {"type": "regular"},
        "Model description": {"type": "regular"},
        "VIN": {"type": "regular"},
        "Total Dealer Invoice": {"type": "regular"},
        "Sold to": {"type": "regular"},
        "Delivery Charge": {"type": "regular"},
        "Invoice Date":  {"type": "regular"}
        },
    "porsche_invoice":{
        "Model code": {"type": "regular"},
        "Model description": {"type": "regular"},
        "VIN": {"type": "regular"},
        "Total Dealer Invoice": {"type": "regular"},
        "Domestic Wholesale": {"type": "regular"},
        "Retail Amount": {"type": "regular"},  
        "Sold to": {"type": "regular"},
        "Delivery Charge": {"type": "regular"},
        "Cocar": {"type": "regular"},
        "Invoice Date":  {"type": "regular"},
        "porsche_experience_delivery": {"type": "regular"}
        },
    "honda_invoice":{
        "Model code": {"type": "regular"},
        "Model description": {"type": "regular"},
        "VIN": {"type": "regular"},
        "Total Dealer Invoice": {"type": "regular"},
        "Hold Back": {"type": "regular"},
        "Sold to": {"type": "regular"},
        "Delivery Charge": {"type": "regular"},
        "Invoice Date":  {"type": "regular"},
        "MSRP Amount": {"type": "regular"},
        "Color upcharge fee": {"type": "regular"}
        },
    "gm_invoice":{
        "Model code": {"type": "regular"},
        "Model description": {"type": "regular"},
        "VIN": {"type": "regular"},
        "Total Dealer Invoice": {"type": "regular"},
        "Hold Back": {"type": "regular"},
        "Sold to": {"type": "regular"},
        "Delivery Charge": {"type": "regular"},
        "Invoice Date":  {"type": "regular"}
        },
    "ford_invoice":{
        "Model code": {"type": "regular"},
        "Model description": {"type": "regular"},
        "VIN": {"type": "regular"},
        "Total Dealer Invoice": {"type": "regular"},
        "Hold Back": {"type": "regular"},
        "Total Vehicle & Options": {"type": "regular"},
        "Sold to": {"type": "regular"},
        "Delivery Charge": {"type": "regular"},
        "Invoice Date":  {"type": "regular"}
        },
    "mazda_invoice":{
        "Model code": {"type": "regular"},
        "VIN": {"type": "regular"},
        "Total Dealer Invoice": {"type": "regular"},
        "Hold Back": {"type": "regular"},
        "Retail Amount": {"type": "regular"},
        "Sold to": {"type": "regular"},
        "Delivery Charge": {"type": "regular"},
        "Invoice Date":  {"type": "regular"}
        },
    "title":{
        "VIN": {"type": "regular"},
        "Make": {"type": "regular"},
    },
    "bol":{
        "BOL Date": {"type": "regular"},
        "BOL VINs": {"type": "regular"},
        "Dates": {"type": "regular"},
        "Address": {"type": "regular"}
    } 
}