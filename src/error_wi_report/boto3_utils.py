import json
import boto3
import os
from botocore.exceptions import Client<PERSON><PERSON>r

def get_parameter(param_name, return_json=True, with_decryption=True):
    """
    Retrieves a parameter value from AWS SSM Parameter Store.

    Args:
        param_name (str): The name of the parameter.
        return_json (bool): If True, parse the value as JSON.
        with_decryption (bool): If True, decrypt SecureString parameters.

    Returns:
        str | dict: The parameter value, optionally parsed as JSON.
    """
    region_name = "us-east-1"

    session = boto3.session.Session()
    client = session.client(
        service_name='ssm',
        region_name=region_name
    )

    try:
        response = client.get_parameter(
            Name=param_name,
            WithDecryption=with_decryption
        )
    except ClientError as e:
        print(f"An error occurred while retrieving the parameter {param_name}: {e}")
        raise e

    param_value = response['Parameter']['Value']

    if return_json:
        return json.loads(param_value)

    return param_value

def get_secret(secret_name, return_json=True):
    """
    This function retrieves the secret value from AWS Secrets Manager.
    """
    region_name = "us-east-1"

    # Create a Secrets Manager client
    session = boto3.session.Session()
    client = session.client(
        service_name='secretsmanager',
        region_name=region_name
    )

    try:
        get_secret_value_response = client.get_secret_value(
            SecretId=secret_name
        )
    except ClientError as e:
        print(f"An error occurred while retrieving the secret value for {secret_name}: {e}")
        raise e

    secret = get_secret_value_response['SecretString']
    if return_json:
        return json.loads(secret)

    return secret

def get_aria_credentials():
    """
    Retrieves ARIA credentials based on environment variables.
    """
    env = os.environ['ENV']
    credentials = get_secret(f"{env}-aria_cm_tokens")
    aria_env = os.environ['ARIA_ENV']
    return credentials.get(aria_env, {})
