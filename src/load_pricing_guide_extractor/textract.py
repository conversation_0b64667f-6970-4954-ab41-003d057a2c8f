import os
from ocr import Ocr, OCRStatus
import time
import threading


"""
This class contains all the related to by giving the files it 
returs a string with all the text in it given by Textract
"""

class TextractResponseHandler():

    def __init__(self, path):
        
        self.debug_mode = 1
        self.path = path

        self.GLOBAL_LOCK = threading.Lock()
        self.TIMESTEPS_THRESHOLD = 20

        textract_adapter_details = {
            "bucket_name": os.getenv('BUCKET'),
            "bucket_folder": os.getenv('BUCKET_FOLDER')
        }

        self.ocr_class = Ocr(textract_adapter_details)
    
    def send_file_to_textract(self, file_name):
        """
        We get the content of the file and send it to textract
        """
        with open(self.path + file_name, "rb") as file_content:
            job_id, ocr_status = self.ocr_class.async_call_textract(file_name, file_content)
            if self.debug_mode == 1: print(f"[LOG_DEBUG] File {file_name} has been sent to textract.")

        return job_id, ocr_status
    
    def get_rest_of_file_if_needed(self, textract_response, job_id, next_token):
        
        next_token_responses = []
        next_token_responses.append(textract_response)

        while(next_token != ""):
            _, textract_next_token_response, next_token = self.ocr_class.get_response(job_id, next_token)
            next_token_responses.append(textract_next_token_response)
            
        return next_token_responses

    def get_text_from_file(self, documents, file_name):
        """
        This methods handle all the logic to get the response we want. 
        First of all we have to send the document to textract in order
        to get the values.

        Later on we have to check if the job has done. This process could take
        some time that is why we have a while checking for the ocr status until
        the job is completed

        After that we have all the information that textract gave us so we have to
        parse it in order to get the complete "string" of all the word blocks

        Theorically we can avoid using GLOBAL_LOCK if the file name is unique (should be)
        so for avoiding any race condition we use it
        """
        
        job_id, ocr_status = self.send_file_to_textract(file_name) 

        time.sleep(5)

        timesteps = 0
        # Async wait
        while(ocr_status != 1 and timesteps < self.TIMESTEPS_THRESHOLD):
            ocr_status, textract_response, next_token = self.ocr_class.get_response(job_id)
            time.sleep(1 * timesteps)

            full_response = self.get_rest_of_file_if_needed(textract_response, job_id, next_token) 

            timesteps += 1
            
            # If the file should be retry we send it again
            if ocr_status == OCRStatus.retry.value:
                job_id, ocr_status = self.send_file_to_textract(file_name)
                time.sleep(1 * timesteps)

        complete_text = ""
        if ocr_status == 1:
            word_blocks, complete_text = self.ocr_class.get_full_page_string(full_response)

        with self.GLOBAL_LOCK:
            documents[file_name] = complete_text
            if self.debug_mode == 1: print(f"[LOG_DEBUG] Finished getting ocr for {file_name}")


    def get_complete_text_of_files(self, files):
        threads = []
        documents = {}

        if self.debug_mode == 1: print("[LOG_DEBUG] Starting to get ocr from files")
        
        # We launch each document on a thread
        # to avoid large waiting time and to get a 
        # faster reponse
        for file_name in files:
            #self.get_text_from_file(documents,file_name)
            thread = threading.Thread(target=self.get_text_from_file, args=(documents,file_name))
            threads.append(thread)
            thread.start()

        # Wait for all threads launched to get the information about
        # all the files
        for thread in threads:
            thread.join()
        
        if self.debug_mode == 1: print("[LOG_DEBUG] Finished of getting ocr from files")
        
        return documents
        