# This module contains a class for interacting with the files collection in the MongoDB database.

import os
from datetime import datetime, timezone, timedelta
from mongo_utils import Mongo
from boto3_utils import get_secret


class CrudFiles:
    def __init__(self):
        mongo_uri = get_secret(os.environ['ENV'] + '-mongodb_uri', return_json=False)
        self.mongo = Mongo(mongo_uri)
        self.mongo.select_db_and_collection(
            db_name=os.environ['MONGO_DATABASE'],
            collection_name='files'
        )

    def add_files(self, payload):
        if isinstance(payload, list):
            self.mongo.insert_many(payload)
        else:
            self.mongo.insert_one(payload)

    def get_failed_files(self, exclude, max_age=5):
        startTime = datetime.now(timezone.utc) - timedelta(days=max_age)

        items = self.mongo.find(
            {
                'status.pdfUtils': {'$in': [0, 1]},
                'filename': {'$nin': exclude},
                'created_at': {'$gte': startTime}
            },
            {"_id": 0}  # projection
        )

        return items

    def find_file_by_name(self, filename):
        return self.mongo.find_one({'filename': filename})

