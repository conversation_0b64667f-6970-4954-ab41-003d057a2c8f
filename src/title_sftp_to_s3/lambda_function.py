import os
from datetime import datetime, timezone
from sftp_utils import get_titles_files
import traceback
from crud_files import Crud<PERSON><PERSON>

def get_todays_title_s3_path(document_type):
    today = datetime.today()
    year = today.strftime("%Y") 
    month = today.strftime("%m") 
    day = today.strftime("%d") 
    
    s3_path = f"{os.environ['COMPLETE_TITLE_FOLDER']}/{year}/{month}/{day}/{document_type}"
    return s3_path

def lambda_handler(event, context):
        
    try:
    
        print(" ****** DOWNLOADING TITLE FILE ****** ")

        document_type = event.get("document_type")
        crud_files = CrudFiles()
        max_failed_file_age_days = os.environ['MAX_FAILED_FILE_AGE_DAYS']

        if not document_type or document_type not in ["mso", "title"]:
            print("Document type is required and must be either 'mso' or 'title'.")
            raise ValueError("Document type is required and must be either 'mso' or 'title'.")

        file_path_s3 = get_todays_title_s3_path(document_type)
        filenames = get_titles_files(os.environ['BUCKET'], file_path_s3, document_type)

        failed_files = crud_files.get_failed_files(exclude=filenames, max_age=max_failed_file_age_days)
        payload = []
        body = []

        #Adding file entry in database if it doesn't exist and creating a return body object
        for filename in filenames:
            if not crud_files.find_file_by_name(filename):
                payload.append({
                    "filename": filename,
                    "filepath": f"s3://{os.environ['BUCKET']}/{file_path_s3}/{filename}",
                    "status": {
                        'pdfUtils': 0
                    },
                    "document_type": document_type,
                    "created_at": datetime.now(timezone.utc)
                })
            
            body.append({
                "action": "process_document",
                "s3_file": f"s3://{os.environ['BUCKET']}/{file_path_s3}/{filename}",
                "document_type": document_type,
                "invoke_type": "step_function"
            })
        
        if len(payload) > 0:
            crud_files.add_files(payload)

        #appending failed_files metadata to response body
        for file in failed_files:
            body.append({
                "action": "process_document",
                "s3_file": file['filepath'],
                "document_type": file['document_type'],
                "invoke_type": "step_function"
            })

        return {
            "statusCode": 200,
            "body": body
        }

    except Exception as e:
        print(f"Error downloading the title: {traceback.format_exc()}")
        if len(body > 0):
            return {
                "statusCode": 202,
                "body": body,
                "error_message": str(e)
            }
        else: 
            raise Exception(f"Error downloading the title: {traceback.format_exc()}")