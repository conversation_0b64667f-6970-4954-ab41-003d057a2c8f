import os
import paramiko
from stat import S_ISREG
import boto3
from boto3_utils import get_secret, post_to_s3, get_latest_pdf_metadata
import datetime
import time
from PyPDF2 import PdfReader, PdfWriter

def connect_sftp(hostname, username, password, port):
    try:
        client = paramiko.SSHClient()
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())  # Automatically add host key
        client.connect(hostname, port=port, username=username, password=password)
        sftp = client.open_sftp()  # Open SFTP session
        return sftp, client
    except paramiko.AuthenticationException:
        print("Auth failed.")
        raise
    except paramiko.SSHException as e:
        print(f"SSH error: {e}")
        raise
    except Exception as e:
        print(f"Error: {e}")
        raise

def sftp_download_as_chunks(sftp, remote_path, local_path, chunk_size_by_mb=5, logger=False):
    chunk_size = chunk_size_by_mb * 1024 * 1024
    start = time.time()
    total_bytes = 0

    with sftp.open(remote_path, 'rb') as remote_file, open(local_path, 'wb') as local_file:
        while True:
            data = remote_file.read(chunk_size)
            if not data:
                break
            local_file.write(data)
            total_bytes += len(data)

            # Print progress while downloading
            if logger:
                elapsed = time.time() - start
                size_mb = total_bytes / (1024 * 1024)
                speed = size_mb / elapsed if elapsed > 0 else 0
                print(f"{size_mb:.2f} MB downloaded in {elapsed:.2f}s ({speed:.2f} MB/s)")

    if logger:
        total_time = time.time() - start
        total_mb = total_bytes / (1024 * 1024)
        avg_speed = total_mb / total_time if total_time > 0 else 0
        print(f"Download complete. Total: {total_mb:.2f} MB in {total_time:.2f}s (Avg Speed: {avg_speed:.2f} MB/s)")

def split_pdf(input_path, page_limit=100):
    # Extract filename without extension
    base_name = os.path.splitext(os.path.basename(input_path))[0]

    reader = PdfReader(input_path)
    total_pages = len(reader.pages)

    output_filenames = []

    if total_pages > page_limit:
        # Split into chunks
        for i in range(0, total_pages, page_limit):
            writer = PdfWriter()
            for j in range(i, min(i + page_limit, total_pages)):
                writer.add_page(reader.pages[j])

            output_filename = f"/tmp/{base_name}_{(i // page_limit) + 1}.pdf"
            with open(output_filename, "wb") as out_file:
                writer.write(out_file)
            
            output_filenames.append(output_filename)
    else: 
        output_filenames = [input_path]

    return output_filenames

def download_files_from_sftp(sftp, client, remote_folder, local_folder, bucket_name, folder_name):
    file_extension = os.environ['FILE_SFTP_EXTENSION']
    page_limit = os.environ['PDF_PAGE_LIMIT']
    today = datetime.date.today()
    today_files = []
    uploaded_files = []
    error_list = []
    try:
        sftp.chdir(remote_folder)
        # Filtrar archivos con la extensión deseada y con fecha de modificación de hoy

        for filename in sftp.listdir():
            if filename.endswith(file_extension):
                file_stat = sftp.stat(filename)
                modified_date = datetime.date.fromtimestamp(file_stat.st_mtime)

                if today - modified_date <= datetime.timedelta(hours=24):
                    today_files.append(filename)

        if not today_files:
            print(f"No files with extension {file_extension} found for today in {remote_folder}.")
            return []

        os.makedirs(local_folder, exist_ok=True)


        for filename in today_files:
            local_path = f"/tmp/{filename}"

            # Verificar si ya fue subido a S3 (evitar duplicados)
            metadata = get_latest_pdf_metadata(bucket_name, folder_name)
            if metadata and filename in metadata['Key']:
                print(f"File '{filename}' already uploaded to S3. Skipping.")
                continue

            print(f"Downloading file: {filename}")
            sftp_download_as_chunks(sftp=sftp, remote_path=filename, local_path=local_path)

            #check if pdf pages exceeds the specified length and divide if possible
            splitted_files = split_pdf(local_path, page_limit)

            for file in splitted_files:
                splitted_filename = os.path.basename(file)
                print(f"Uploading to S3: {splitted_filename}")
                post_to_s3(bucket_name, folder_name, splitted_filename, file)
                uploaded_files.append(splitted_filename)

    except Exception as e:
        print(f"❌ Error: {e}")
        error_list.append(e)
    finally:
        if sftp:
            sftp.close()  # Close the SFTP session
        if client:
            client.close()  # Close the SSH client
    if not uploaded_files and error_list:
        raise Exception(f"❌ Error: {''.join(error_list)}")
    return uploaded_files

def get_titles_files(bucket_name, folder_name, document_type):
    secret = get_secret(os.environ['SFTP_CREDENTIALS'])  # Get secrets from Secrets Manager
    hostname = secret["hostname"]
    username = secret["username"]
    password = secret["password"]
    port = secret["port"]


    remote_folder_path = os.environ['SFTP_FILES_PATH_MSOS']
    if document_type == "title":
        remote_folder_path = os.environ['SFTP_FILES_PATH_TITLES']
        
    local_download_path = '/tmp/'  
    sftp, client = connect_sftp(hostname, username, password, port)  # Establish SFTP connection
    response = download_files_from_sftp(sftp, client, remote_folder_path, local_download_path, bucket_name, folder_name)

    return response