from bre_files.bre import Bre
from vininfo import Vin
import os
import pymongo

class PreInventoryInvoicesBreEngine(Bre):

    def __init__(self, event, mongo_uri):
        super().__init__(event, mongo_uri)
        
        self.bre_rules.update({
            801: {
                'tag': 'vin',
                'output_tags': ['vin'],
                'pre_req': [],
            },
            802: {
                'tag': 'stock',
                'output_tags': ['stock'],
                'pre_req': [],
            },
            803: {
                'tag': 'make',
                'output_tags': ['make'],
                'pre_req': [],
            },
            804: {
                'tag': 'store',
                'output_tags': ['store'],
                'pre_req': [],
            },
            805: {
                'tag': 'invoice_date',
                'output_tags': ['invoice_date'],
                'pre_req': [],
            },
            806: {
                'tag': 'model_code',
                'output_tags': ['model_code'],
                'pre_req': [],
            },
            807: {
                'tag': 'model_description',
                'output_tags': ['model_description'],
                'pre_req': [],
            },
            808: {
                'tag': 'sold_to',
                'output_tags': ['sold_to'],
                'pre_req': [],
            },
            809: {
                'tag': 'order_code',
                'output_tags': ['order_code'],
                'pre_req': [],
            },
            810: {
                'tag': 'color',
                'output_tags': ['color'],
                'pre_req': [],
            },
            811: { # Check VIN is same as extracted
                'tag': 'vin',
                'output_tags': ['vin'],
                'pre_req': [],
            },
            812: { # Fill VIN in DMS data
                'tag': 'stock_in_values',
                'output_tags': ['stock_in_values'],
                'pre_req': [],
            },
            813: { # Fill Store in DMS data
                'tag': 'stock_in_values',
                'output_tags': ['stock_in_values'],
                'pre_req': [],
            },
            814: { # Fill Brand in DMS data
                'tag': 'stock_in_values',
                'output_tags': ['stock_in_values'],
                'pre_req': [],
            },
            815: { # Fill Prefix in DMS data
                'tag': 'stock_in_values',
                'output_tags': ['stock_in_values'],
                'pre_req': [],
            },
            816: { # Fill Reference in DMS data
                'tag': 'stock_in_values',
                'output_tags': ['stock_in_values'],
                'pre_req': [],
            },
            817: { # Fill Date in DMS data
                'tag': 'stock_in_values',
                'output_tags': ['stock_in_values'],
                'pre_req': [805],
            },
            818: { # Fill Order in DMS data
                'tag': 'stock_in_values',
                'output_tags': ['stock_in_values'],
                'pre_req': [],
            },
            819: { # Fill General color in DMS data
                'tag': 'stock_in_values',
                'output_tags': ['stock_in_values'],
                'pre_req': [],
            },
            820: { # Fill Exterior color in DMS data
                'tag': 'stock_in_values',
                'output_tags': ['stock_in_values'],
                'pre_req': [],
            },
            821: { # Fill Interior color in DMS data
                'tag': 'stock_in_values',
                'output_tags': ['stock_in_values'],
                'pre_req': [],
            },
            822: { # Fill List price in DMS data
                'tag': 'stock_in_values',
                'output_tags': ['stock_in_values'],
                'pre_req': [],
            },
            823: { # Fill Sales cost in DMS data
                'tag': 'stock_in_values',
                'output_tags': ['stock_in_values'],
                'pre_req': [],
            },
            824: { # Fill Base MSRP in DMS data
                'tag': 'stock_in_values',
                'output_tags': ['stock_in_values'],
                'pre_req': [],
            },
            825: { # Fill Model code in DMS data
                'tag': 'stock_in_values',
                'output_tags': ['stock_in_values'],
                'pre_req': [],
            },
            826: { # Fill Prefix location in DMS data
                'tag': 'stock_in_values',
                'output_tags': ['stock_in_values'],
                'pre_req': [],
            },
            827: { # Fill Prefix location in DMS data
                'tag': 'table',
                'output_tags': ['table'],
                'pre_req': [],
            },
            828: { # Fill Reynolds Model in DMS data
                'tag': 'stock_in_values',
                'output_tags': ['stock_in_values', 'reynolds_model'],
                'pre_req': [],
            }
        })

        self.mapping_fields_row_uuid_stock_in_values = {}
        rows_stock_in = self.parsed_fields[self.document_type]['stock_in_values'].get("rows", {})
        for k, v in rows_stock_in.items():
            field_name = v['cells']['field']['value'].replace(' ', '_').lower()
            self.mapping_fields_row_uuid_stock_in_values[field_name.lower()] = k

        self.bre_matrix = self.db.get_collection("bre_matrix")

    def fill_dms_data_row(self, input_data, field):

        field_pretty = field.replace(' ', '_').lower()

        rows = self.parsed_fields[self.document_type]['stock_in_values'].get("rows", {})
        
        row_uuid = self.mapping_fields_row_uuid_stock_in_values.get(field_pretty, "")
        if row_uuid == "":
            row_uuid = str(len(list(self.mapping_fields_row_uuid_stock_in_values.keys())))

        rows[row_uuid] = {}
        rows[row_uuid]['cells'] = {
            'field': {'pass': True,'value': field, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
            'value': {'pass': True,'value': input_data, 'display': True, 'confidence': 'None', 'coordinates': {}}}

        self.result['stock_in_values'] = {'pass': True, 'value': rows, 'display': True, 'message': ''}
        if input_data == "":
            self.result['stock_in_values']['message'] = f'{field_pretty} calculation cant be done'

            rows[row_uuid]['cells'] = {
            'field': {'pass': True,'value': field, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
            'value': {'pass': False, 'message': f'{field} value couldnt be filled','value': input_data, 'display': True, 'confidence': 'None', 'coordinates': {}}}

            self.result['stock_in_values']['value'].update(rows)

    def rule_801(self):
        """
        Rule 801: Validate VIN
        This rule checks the VIN (Vehicle Identification Number) for validity.
        """
        input_data = str(self.parsed_fields[self.document_type]['vin'].get('value')) if self.parsed_fields[self.document_type].get('vin', '').get("value", "") != "" else ''
        vin = input_data.upper().replace(" ", "").replace("O", "0").replace("I", "1").replace("Q", "9")

        self.result['vin'] = {'pass': True, 'value': vin, 'display': True}
        if not input_data or input_data.replace(' ','') == '' or len(vin) != 17 or Vin(vin).verify_checksum() == False:
            self.result['vin']['pass'] = False
            self.result['vin']['message'] = 'VIN value failed verification. Wrong VIN or malformed.'
            self.result['vin']['value'] = input_data

    def rule_802(self):
        """
        Rule 802: Validate Store
        This rule checks the Store of the vehicle for validity.
        """
        input_data = str(self.parsed_fields[self.document_type]['stock'].get('value')) if self.parsed_fields[self.document_type].get('stock', {}).get("value", "") != "" else ''
        self.non_empty_rule(input_data, 'Stock')
    
    def rule_803(self):
        """
        Rule 803: Validate Brand
        This rule checks the Brand of the vehicle for validity.
        """
        input_data = str(self.parsed_fields[self.document_type]['make'].get('value')) if self.parsed_fields[self.document_type].get('make', {}).get("value", "") != "" else ''
        self.non_empty_rule(input_data, 'Make')
    
    def rule_804(self):
        """
        Rule 804: Validate Store
        This rule checks the Store of the vehicle for validity.
        """
        input_data = str(self.parsed_fields[self.document_type]['store'].get('value')) if self.parsed_fields[self.document_type].get('store', {}).get("value", "") != "" else ''
        self.non_empty_rule(input_data, 'Store')

    def rule_805(self):
        """
        Rule 805: Validate Invoice Date
        This rule checks the Invoice Date of the vehicle for validity.
        """
        input_data = str(self.parsed_fields[self.document_type]['invoice_date'].get('value')) if self.parsed_fields[self.document_type].get('invoice_date', {}).get("value", "") != "" else ''
        input_data = input_data.split(" ")[0]
        if "-" in input_data:
            vals = input_data.split("-")
            input_data = f"{vals[1]}/{vals[2]}/{vals[0]}"


        self.result['invoice_date'] = {'pass': True, 'value': input_data.replace(' ',''), 'message': "", 'display': True}

        input_data
        if not input_data or input_data.replace(' ','') == '' and not self.is_valid_date(input_data.replace(' ','')):
            self.result['invoice_date']['pass'] = False
            self.result['invoice_date']['message'] = 'Invoice date value not found'
            self.result['invoice_date']['value'] = input_data
            self.result['invoice_date']['coordinates'] = {}
        
    def rule_806(self):
        """
        Rule 806: Validate Model code
        This rule checks the Model code of the vehicle for validity.
        """
        input_data = str(self.parsed_fields[self.document_type]['model_code'].get('value')) if self.parsed_fields[self.document_type].get('model_code', {}).get("value", "") != "" else ''
        self.non_empty_rule(input_data, 'Model code')

    def rule_807(self):
        """
        Rule 807: Validate Model description
        This rule checks the Model description of the vehicle for validity.
        """
        input_data = str(self.parsed_fields[self.document_type]['model_description'].get('value')) if self.parsed_fields[self.document_type].get('model_description', {}).get("value", "") != "" else ''
        self.non_empty_rule(input_data, 'Model description')

    def rule_808(self):
        """
        Rule 808: Validate Sold to
        This rule checks the Sold to of the vehicle for validity.
        """
        input_data = str(self.parsed_fields[self.document_type]['sold_to'].get('value')) if self.parsed_fields[self.document_type].get('sold_to', {}).get("value", "") != "" else ''
        self.non_empty_rule(input_data, 'Sold to')

    def rule_809(self):
        """
        Rule 809: Validate Order code
        This rule checks the Order code of the vehicle for validity.
        """
        input_data = str(self.parsed_fields[self.document_type]['order_code'].get('value')) if self.parsed_fields[self.document_type].get('order_code', {}).get("value", "") != "" else ''
        self.non_empty_rule(input_data, 'Order code')

    def rule_810(self):
        """
        Rule 810: Validate Color
        This rule checks the Color of the vehicle for validity.
        """
        input_data = str(self.parsed_fields[self.document_type]['color'].get('value')) if self.parsed_fields[self.document_type].get('color', {}).get("value", "") != "" else ''
        self.non_empty_rule(input_data, 'Color')

    def rule_811(self):
        extracted_vin_value = str(self.parsed_fields[self.document_type]['vin'].get('value')).replace(" ", "") if self.parsed_fields[self.document_type].get('vin', {}).get("value", "") != "" else ''

        report_vin_value = None
        rows_report = self.parsed_fields[self.document_type]['input_data'].get("rows", {})
        for k, v in rows_report.items():
            field_name = v['cells']['field']['value']
            if field_name == "VIN":
                report_vin_value = v['cells']['value']['value']

        self.result['vin'] = {'pass': True, 'value': report_vin_value, 'message': "", 'display': True}
        if report_vin_value != extracted_vin_value:
            self.result['vin'] = {'pass': False, 'value': report_vin_value, 'message': "VIN extracted and the VIN of the report are differents", 'display': True}

    def rule_812(self):
        field_name = "VIN"
        value = str(self.parsed_fields[self.document_type]['vin'].get('value')) if self.parsed_fields[self.document_type].get('vin', {}).get("value", "") != "" else ''
        self.fill_dms_data_row(value, field_name)
    
    def rule_813(self):
        field_name = "STORE"
        value = str(self.parsed_fields[self.document_type]['store'].get('value')) if self.parsed_fields[self.document_type].get('store', {}).get("value", "") != "" else ''
        self.fill_dms_data_row(value, field_name)

    def rule_814(self):
        field_name = "BRAND"
        value = str(self.parsed_fields[self.document_type]['make'].get('value')) if self.parsed_fields[self.document_type].get('make', {}).get("value", "") != "" else ''
        self.fill_dms_data_row(value, field_name)

    def rule_815(self):
        field_name = "PREFIX"
        pass
        
    def rule_816(self):
        field_name = "REFERENCE"
        value = str(self.parsed_fields[self.document_type]['stock'].get('value')) if self.parsed_fields[self.document_type].get('stock', {}).get("value", "") != "" else ""
        self.fill_dms_data_row(value, field_name)

    def rule_817(self):
        field_name = "DATE"
        value = str(self.parsed_fields[self.document_type]['invoice_date'].get('value')) if self.parsed_fields[self.document_type].get('invoice_date', {}).get("value", "") != "" else ""
        self.fill_dms_data_row(value, field_name)

    def rule_818(self):
        field_name = "ORDER"
        value = str(self.parsed_fields[self.document_type]['order_code'].get('value')) if self.parsed_fields[self.document_type].get('order_code', {}).get("value", "") != "" else ""
        self.fill_dms_data_row(value, field_name)

    def rule_819(self):
        field_name = "GENERAL COLOR"
        pass

    def rule_820(self):
        field_name = "EXTERIOR COLOR"
        pass

    def rule_821(self):
        field_name = "INTERIOR COLOR"
        pass

    def rule_822(self):
        field_name = "LIST PRICE"
        pass

    def rule_823(self):
        field_name = "SALES COST"
        pass

    def rule_824(self):
        field_name = "BASE MSRP"
        pass

    def rule_825(self):
        field_name = "MODEL CODE"
        value = str(self.parsed_fields[self.document_type]['model_code'].get('value')) if self.parsed_fields[self.document_type].get('model_code', {}).get("value", "") != "" else ""
        self.fill_dms_data_row(value, field_name)

    def rule_826(self):
        field_name = "PREFIX LOCATION"
        pass

    def rule_827(self):
        # Rule to mark those ROWS of the line items table extracted that needs to be stocked in
        pass

    def rule_828(self):
        field_name = "REYNOLDS MODEL"
        value = str(self.parsed_fields[self.document_type]['reynolds_model'].get('value')) if self.parsed_fields[self.document_type].get('reynolds_model', {}).get("value", "") != "" else ""
        self.fill_dms_data_row(value, field_name)
        self.result['reynolds_model'] = {'pass': True, 'value': value, 'display': True}

    def process_based_on_rules_bre(self):

        if self.valid_rules == self.passed_rules:
            aria_exception = ''
            next_status_id = self.action['target_status']
            note = ""
            
            next_status_label = self.status_info[next_status_id]['label']
            next_status = self.status_info[next_status_id]['key']

        else:
            
            next_status_id = self.action['source_status']

            print(self.status_info)
            actual_status = self.get_status_label_by_status_id(next_status_id, self.status_info).lower()
            next_status = "ready"
            aria_exception = ""
            note = ""

            print("Actual status" , actual_status)

            if "needs" in actual_status:
                if len(self.not_passed_rules) > 0:
                    
                    next_status = "needs"
                    aria_exception = "One or more fields require human intervention"
                    
                    # if len(self.not_passed_rules) == 1 and 802 in self.not_passed_rules:
                    #     next_status = "hold"
                    #     aria_exception = "Transport amount for this vehicle not found yet"
                           

            # if "hold" in actual_status:
            #     if len(self.not_passed_rules) > 0:

            #         next_status = "needs"
            #         aria_exception = "One or more fields require human intervention"
                    
            #         if len(self.not_passed_rules) == 1 and 802 in self.not_passed_rules:
            #             next_status = "hold"
            #             aria_exception = "Transport amount for this vehicle not found yet"

                            
            next_status_id, next_status_label = self.get_status_label_info(next_status, self.status_info)

        return next_status_id, next_status_label, aria_exception, note


    def process_default_bre(self):

        aria_exception = ""
        note = ""
        next_status = "ready"

        if len(self.not_passed_rules) > 0:

            next_status = "needs"
            aria_exception = "One or more fields require human intervention"
            
            # if len(self.not_passed_rules) == 1 and 802 in self.not_passed_rules:
            #     next_status = "hold"
            #     aria_exception = "Transport amount for this vehicle not found yet"
       
        next_status_id, next_status_label = self.get_status_label_info(next_status, self.status_info)


        return next_status_id, next_status_label, aria_exception, note