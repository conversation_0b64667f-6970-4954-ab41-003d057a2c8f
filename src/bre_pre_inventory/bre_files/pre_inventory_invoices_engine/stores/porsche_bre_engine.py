from bre_files.pre_inventory_invoices_engine.invoice_bre_engine import PreInventoryInvoicesBreEngine
import os
from utils.boto3_utils import trigger_lambda_response
import json

class PorscheInvoicesBreEngine(PreInventoryInvoicesBreEngine):

    def __init__(self, event, mongo_uri):
        super().__init__(event, mongo_uri)
        
        self.rules = {
            "required_fields_and_calculations": [801, 803, 804, 805, 806, 807, 808, 809, 810, 811, 812, 813, 814, 815, 817, 818, 819, 820, 821, 822, 823, 824, 825, 826, 827, 1, 2],
            "submit": [801, 803, 804, 805, 806, 807, 808, 809, 810, 811, 812, 813, 814, 815, 817, 818, 819, 820, 821, 822, 823, 824, 825, 826, 827, 828, 1, 2],
            "save": [],
            "review": [],
            "inspection": [],
            "manually": []
        }

        self.bre_rules.update({
            819: { # Fill General color in DMS data
                'tag': 'stock_in_values',
                'output_tags': ['stock_in_values'],
                'pre_req': [810],
            },
            820: { # Fill Exterior color in DMS data
                'tag': 'stock_in_values',
                'output_tags': ['stock_in_values'],
                'pre_req': [810],
            },
            821: { # Fill Interior color in DMS data
                'tag': 'stock_in_values',
                'output_tags': ['stock_in_values'],
                'pre_req': [810],
            },
            822: { # Fill List price in DMS data
                'tag': 'stock_in_values',
                'output_tags': ['stock_in_values'],
                'pre_req': [1],
            },
            824: { # Fill Base MSRP in DMS data
                'tag': 'stock_in_values',
                'output_tags': ['stock_in_values'],
                'pre_req': [1],
            },
            1: { # Retail total price non empty
                'tag': 'retail_total_price',
                'output_tags': ['retail_total_price'],
                'pre_req': [],
            },
            2: { # Color code length validation
                'tag': 'color',
                'output_tags': ['color'],
                'pre_req': [810],
            }
        })

        self.valid_rules = self.rules[self.bre_type[self.document_type]]
        print("RULES TO EXECUTE", self.valid_rules)
        self.store = self.parsed_fields[self.document_type]['store'].get('value')

    def rule_1(self):
        input_data = str(self.parsed_fields[self.document_type]['retail_total_price'].get('value')) if self.parsed_fields[self.document_type].get('retail_total_price', {}).get("value", "") != "" else ''
        self.non_empty_rule(input_data, 'Retail total price')

    def rule_2(self):
        """
        Rule 2: Validate Color length for PORSCHE
        This rule checks the Color code length of the vehicle for validity. It should be made of 6 alphanumeric characters (Ex: 1H63V2).
        """
        input_data = str(self.parsed_fields[self.document_type]['color'].get('value')) if self.parsed_fields[self.document_type].get('color', {}).get("value", "") != "" else ''
        passed_validation = True if len(input_data.replace(" ", "")) == 6 else False
        self.result['color'] = {
            'pass': passed_validation, 
            'value': input_data.replace(" ", ""), 
            'message': "" if passed_validation else "Color code validation failed, color code must be 6 alphanumeric characters", 
            'display': True
        }

    def rule_815(self):
        field_name = "PREFIX"
        value = "PO"
        self.fill_dms_data_row(value, field_name)

    def construct_color_extractor_message(self, color_extracted):
        return {
            "provider": "openai",
            "host": "public",
            "llm_model":"gpt-4.1-mini",
            "prompt": """

                You are an expert choosing what color is the best match for a color that I extracted from a document.
                We must match that color with one and ONLY one of the next colors:
                
                [BLACK, BLUE, BROWN, COPPER, GOLD, GRAY, GREEN, MAROON, ORANGE, PINK, PRIMER, PURPLE, RED, SILVER, SPECIAL, TAN, TEAL, WHITE, YELLOW, NON-COLOR]

                The ouput must be: 
                { "color": "selected_color_from_the_list" }

                An example could be:
                { "color": "PURPLE" }


                Output Requirements:
                - Provide only the JSON object without any additional text or explanations.
                - Do not include 'json' tags or any special symbols; output should only contain the `{}` characters.
                - If the color is not in the list, output "NONE".

            """,
            "message": f"""
            
                * The color extracted is {color_extracted}
                
            """
        }
    

    def rule_819(self):
        field_name = "GENERAL COLOR"
        
        color_code = str(self.parsed_fields[self.document_type]['color'].get('value')).replace(" ", "") if self.parsed_fields[self.document_type].get('color', {}).get("value", "") != "" else ''
        general_color = color_code[:2]

        general_color_value = ""
        rows_table = self.parsed_fields[self.document_type]['table'].get("rows", {}) or self.parsed_fields[self.document_type]['table'].get("value", {})
        for k, v in rows_table.items():
            field_name_code = v['cells']['code']['value']
            if field_name_code.replace(" ", "").lower() == general_color.replace(" ", "").lower():
                general_color_value = v['cells']['description']['value']
                break
        
        msg_to_extract_color =  self.construct_color_extractor_message(general_color_value)
        llm_lambda_response = trigger_lambda_response(os.environ['LLM_MESSENGER_LAMBDA'], msg_to_extract_color)
        if llm_lambda_response['statusCode'] != 200:
            return {
                "statusCode": 500,
                "body": json.dumps({"message": "Error response from llm."})
            }
    
        llm_lambda_response = json.loads(llm_lambda_response['body'])
        llm_lambda_response = llm_lambda_response['message']

        general_color_value = json.loads(llm_lambda_response)["color"]

        self.fill_dms_data_row(general_color_value, field_name)   

    def rule_820(self):
        field_name = "EXTERIOR COLOR"
        
        color_code = str(self.parsed_fields[self.document_type]['color'].get('value')).replace(" ", "") if self.parsed_fields[self.document_type].get('color', {}).get("value", "") != "" else ''
        general_color = color_code[2:4]

        exterior_color_value = ""
        rows_table = self.parsed_fields[self.document_type]['table'].get("rows", {}) or self.parsed_fields[self.document_type]['table'].get("value", {})
        for k, v in rows_table.items():
            field_name_code = v['cells']['code']['value']
            if field_name_code.replace(" ", "").lower() == general_color.replace(" ", "").lower():
                exterior_color_value = field_name_code
                break

        self.fill_dms_data_row(exterior_color_value, field_name)  

    def rule_821(self):
        field_name = "INTERIOR COLOR"
        
        color_code = str(self.parsed_fields[self.document_type]['color'].get('value')).replace(" ", "") if self.parsed_fields[self.document_type].get('color', {}).get("value", "") != "" else ''
        general_color = color_code[-2:]

        interior_color_value = ""
        rows_table = self.parsed_fields[self.document_type]['table'].get("rows", {}) or self.parsed_fields[self.document_type]['table'].get("value", {})
        for k, v in rows_table.items():
            field_name_code = v['cells']['code']['value']
            if field_name_code.replace(" ", "").lower() == general_color.replace(" ", "").lower():
                interior_color_value = field_name_code
                break

        self.fill_dms_data_row(interior_color_value, field_name)  

    def rule_822(self):
        field_name = "LIST PRICE"
        value = str(self.parsed_fields[self.document_type]['retail_total_price'].get('value')) if self.parsed_fields[self.document_type].get('retail_total_price', {}).get("value", "") != "" else ""
        self.fill_dms_data_row(value, field_name)
    
    def rule_823(self):
        field_name = "SALES COST"
        value = str(self.parsed_fields[self.document_type]['retail_total_price'].get('value')) if self.parsed_fields[self.document_type].get('retail_total_price', {}).get("value", "") != "" else ""
        self.fill_dms_data_row(value, field_name)

    def rule_824(self):
        field_name = "BASE MSRP"
        value = str(self.parsed_fields[self.document_type]['retail_total_price'].get('value')) if self.parsed_fields[self.document_type].get('retail_total_price', {}).get("value", "") != "" else ""
        self.fill_dms_data_row(value, field_name)

    def rule_826(self):
        field_name = "PREFIX LOCATION"
        value = "NW" if "pnw" in self.store.lower() else "NA"
        self.fill_dms_data_row(value, field_name)

    def rule_827(self):
        color_code = str(self.parsed_fields[self.document_type]['color'].get('value')).replace(" ", "") if self.parsed_fields[self.document_type].get('color', {}).get("value", "") != "" else ''
        interior_color_code = color_code[-2:]

        rows_table = self.parsed_fields[self.document_type]['table'].get("rows", {}) or self.parsed_fields[self.document_type]['table'].get("value", {})
        for k, v in reversed(list(rows_table.items())):
            if v['cells']['code']['value'].replace(" ", "").lower() == interior_color_code.replace(" ", "").replace(" ", "").lower():
                break
            rows_table[k]['to_stock_in'] = True

        self.result['table'] = {'pass': True, 'value': rows_table, 'message': "", 'display': True}