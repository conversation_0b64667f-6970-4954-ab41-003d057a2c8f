
import os
from datetime import datetime
from utils.mongo_utils import Mongo
from utils.boto3_utils import get_secret


class CrudBreMatrix:
    def __init__(self, mongo):
        self.mongo = mongo
        self.collection_name = 'bre_matrix'


    def get_bre_matrix_by_store_and_brand(self, store, brand):
        """
        This function finds a bol by its attachment ID.
        """
        query = {"STORE": store.upper(), "BRAND": brand.upper()}
        self.mongo.select_db_and_collection(os.environ['MONGO_DATABASE'], collection_name=self.collection_name)
        return self.mongo.find_one(query)
    
    def get_bre_matrix_vals(self):
        self.mongo.select_db_and_collection(os.environ['MONGO_DATABASE'], collection_name=self.collection_name)
        return self.mongo.find({})
    
    def __del__(self):
        self.mongo.close_connection()