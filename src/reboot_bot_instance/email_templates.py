"""
Lightweight email templates with full MongoDB integration for EC2 operations
"""

from datetime import datetime
from typing import Dict, Any, Optional
import logging
from mongo_utils import MongoClientUtils

logger = logging.getLogger()

class EmailTemplates:
    """Lightweight email template class that fully relies on MongoDB for structure and content"""
    
    def __init__(self):
        self.mongo_client = MongoClientUtils()
    
    @staticmethod
    def _format_duration(seconds: float) -> str:
        """Format duration in seconds to human-readable format"""
        if seconds < 60:
            return f"{seconds:.1f} seconds"
        elif seconds < 3600:
            minutes = seconds / 60
            return f"{minutes:.1f} minutes"
        else:
            hours = seconds / 3600
            return f"{hours:.1f} hours"
    
    def _get_email_template(self, template_type: str, app_id: str = None) -> Optional[Dict]:
        """
        Get complete email template from MongoDB
        
        Args:
            template_type: Type of template ('reboot', 'reboot_status', 'reboot_error')
            app_id: Optional app ID for app-specific templates
        
        Returns:
            Complete template configuration or None
        """
        try:
            query = {"template_type": template_type, "active": True}
            
            if app_id:
                # Try app-specific template first
                app_query = {**query, "app_id": app_id}
                template = self.mongo_client.db.EmailTemplates.find_one(app_query)
                if template:
                    logger.info(f"Using app-specific template for {app_id}")
                    return template
            
            # Fall back to default template
            default_query = {**query, "app_id": app_id}
            print("default_query", default_query)
            template = self.mongo_client.db.EmailTemplates.find_one(default_query)
            
            if template:
                logger.info(f"Using default template for {template_type}")
                return template
            else:
                logger.error(f"No email template found for {template_type}")
                return None
                
        except Exception as e:
            logger.error(f"Error fetching email template for {template_type}: {str(e)}")
            return None
    
    def _process_template_variables(self, template_str: str, variables: Dict[str, Any]) -> str:
        """
        Replace template variables with actual values
        
        Args:
            template_str: Template string with {variable} placeholders
            variables: Dictionary of variable values
        
        Returns:
            Processed string with variables replaced
        """
        try:
            # Process special formatting
            processed_vars = {}
            for key, value in variables.items():
                if 'duration' in key.lower() and isinstance(value, (int, float)):
                    processed_vars[key] = self._format_duration(value)
                elif key.lower() == 'timestamp':
                    processed_vars[key] = f"🕐 {value}"
                else:
                    processed_vars[key] = value
            
            return template_str.format(**processed_vars)
        except Exception as e:
            logger.error(f"Error processing template variables: {str(e)}")
            return template_str
    
    def _build_email_from_template(self, template: Dict, data: Dict[str, Any]) -> Dict[str, str]:
        """
        Build complete email from MongoDB template
        
        Args:
            template: Template configuration from MongoDB
            data: Data to populate in template
        
        Returns:
            Dict with 'subject' and 'content' keys
        """
        try:
            # Process subject
            subject_template = template.get('subject', 'Email Notification')
            subject = self._process_template_variables(subject_template, data)
            
            # Get HTML content directly from MongoDB
            html_content = template.get('html_content', '<html><body><p>Email content not available</p></body></html>')
            
            # Process HTML content with variables
            content = self._process_template_variables(html_content, data)
            
            return {
                'subject': subject,
                'content': content
            }
            
        except Exception as e:
            logger.error(f"Error building email from template: {str(e)}")
            return {
                'subject': 'Email Notification Error',
                'content': '<html><body><p>Error generating email content</p></body></html>'
            }
    
    def _build_additional_rows(self, additional_info: Optional[Dict[str, Any]]) -> str:
        """Build additional table rows from additional_info dictionary"""
        additional_rows = ""
        if additional_info:
            for key, value in additional_info.items():
                # Special formatting for duration
                if 'duration' in key.lower() and isinstance(value, (int, float)):
                    value = self._format_duration(value)
                elif key.lower() == 'operation duration':
                    if isinstance(value, str) and 'seconds' in value:
                        try:
                            seconds = float(value.split()[0])
                            value = self._format_duration(seconds)
                        except:
                            pass
                
                additional_rows += f"""
                <tr>
                    <td style="padding: 10px; background-color: #f9f9f9; font-weight: 600; border: 1px solid #e0e0e0;">{key.replace('_', ' ').title()}:</td>
                    <td style="padding: 10px; border: 1px solid #e0e0e0;">{value}</td>
                </tr>"""
        
        return additional_rows

    def get_reboot_notification_email(
        self,
        instance_id: str,
        instance_name: str,
        success: bool,
        message: str,
        additional_info: Optional[Dict[str, Any]] = None,
        app_id: str = None
    ) -> Dict[str, str]:
        """
        Construct reboot notification email using MongoDB template
        """
        try:
            template = self._get_email_template('reboot', app_id)
            if not template:
                raise Exception("No reboot template found")
            
            # Prepare template data
            data = {
                'instance_id': instance_id,
                'instance_name': instance_name,
                'operation_type': 'Instance Reboot',
                'success': success,
                'status': 'SUCCESS' if success else 'FAILURE',
                'status_icon': '✅' if success else '❌',
                'status_color': '#4CAF50' if success else '#f44336',
                'status_bg': '#e8f5e8' if success else '#ffeaea',
                'message': message,
                'timestamp': datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC'),
                'additional_rows': self._build_additional_rows(additional_info)
                # **(additional_info or {})
            }
            
            print("data", data)

            return self._build_email_from_template(template, data)
            
        except Exception as e:
            logger.error(f"Error creating reboot notification email: {str(e)}")
            return self._get_fallback_email('reboot', instance_id, instance_name, success, message)
    
    def get_instance_status_email(
        self,
        instance_id: str,
        instance_name: str,
        current_state: str,
        previous_state: Optional[str] = None,
        additional_details: Optional[Dict[str, Any]] = None,
        app_id: str = None
    ) -> Dict[str, str]:
        """
        Construct status notification email using MongoDB template
        """
        try:
            template = self._get_email_template('reboot_status', app_id)
            if not template:
                raise Exception("No status template found")
            
            # State configuration
            state_icons = {
                'running': '🟢', 'stopped': '🔴', 'stopping': '🟡', 
                'starting': '🔵', 'terminated': '⚫', 'rebooting': '🔄'
            }
            
            state_colors = {
                'running': '#4CAF50', 'stopped': '#FF9800', 'stopping': '#FF5722',
                'starting': '#2196F3', 'terminated': '#9C27B0', 'rebooting': '#00BCD4'
            }
            
            data = {
                'instance_id': instance_id,
                'instance_name': instance_name,
                'current_state': current_state.upper(),
                'previous_state': previous_state.upper() if previous_state else 'N/A',
                'state_icon': state_icons.get(current_state.lower(), '❓'),
                'state_color': state_colors.get(current_state.lower(), '#757575'),
                'timestamp': datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC'),
                **(additional_details or {})
            }
            
            return self._build_email_from_template(template, data)
            
        except Exception as e:
            logger.error(f"Error creating status notification email: {str(e)}")
            return self._get_fallback_email('reboot_status', instance_id, instance_name, current_state)
    
    def get_error_notification_email(
        self,
        operation: str,
        instance_id: str,
        instance_name: str,
        error_message: str,
        error_code: Optional[str] = None,
        additional_info: Optional[Dict[str, Any]] = None,
        app_id: str = None
    ) -> Dict[str, str]:
        """
        Construct error notification email using MongoDB template
        """
        try:
            template = self._get_email_template('reboot_error', app_id)
            if not template:
                raise Exception("No error template found")
            
            data = {
                'operation': operation,
                'instance_id': instance_id,
                'instance_name': instance_name,
                'error_message': error_message,
                'error_code': error_code or 'N/A',
                'status': 'FAILED',
                'status_icon': '❌',
                'status_color': '#f44336',
                'timestamp': datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC'),
                'additional_rows': self._build_additional_rows(additional_info)
                # **(additional_info or {})
            }

            print("data", data)
            
            return self._build_email_from_template(template, data)
            
        except Exception as e:
            logger.error(f"Error creating error notification email: {str(e)}")
            return self._get_fallback_email('reboot_error', instance_id, instance_name, operation, error_message)
    
    def _get_fallback_email(self, email_type: str, *args) -> Dict[str, str]:
        """
        Generate basic fallback email when MongoDB template is unavailable
        """
        timestamp = datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')
        print("email_type", email_type)
        if email_type == 'reboot':
            instance_id, instance_name, success, message = args
            status = 'SUCCESS' if success else 'FAILURE'
            return {
                'subject': f'[EC2] {status}: Reboot - {instance_name} ({instance_id})',
                'content': f'''
                <html><body style="font-family: Arial, sans-serif; padding: 20px;">
                    <h2>EC2 Reboot Notification</h2>
                    <p><strong>Status:</strong> {status}</p>
                    <p><strong>Instance:</strong> {instance_name} ({instance_id})</p>
                    <p><strong>Message:</strong> {message}</p>
                    <p><strong>Timestamp:</strong> {timestamp}</p>
                </body></html>
                '''
            }
        
        elif email_type == 'reboot_status':
            instance_id, instance_name, current_state = args
            return {
                'subject': f'[EC2] Status: {instance_name} ({instance_id}) - {current_state}',
                'content': f'''
                <html><body style="font-family: Arial, sans-serif; padding: 20px;">
                    <h2>EC2 Status Update</h2>
                    <p><strong>Instance:</strong> {instance_name} ({instance_id})</p>
                    <p><strong>State:</strong> {current_state}</p>
                    <p><strong>Timestamp:</strong> {timestamp}</p>
                </body></html>
                '''
            }
        
        elif email_type == 'reboot_error':
            instance_id, instance_name, operation, error_message = args
            return {
                'subject': f'[EC2] ERROR: {operation} - {instance_name} ({instance_id})',
                'content': f'''
                <html><body style="font-family: Arial, sans-serif; padding: 20px;">
                    <h2>EC2 Operation Error</h2>
                    <p><strong>Operation:</strong> {operation}</p>
                    <p><strong>Instance:</strong> {instance_name} ({instance_id})</p>
                    <p><strong>Error:</strong> {error_message}</p>
                    <p><strong>Timestamp:</strong> {timestamp}</p>
                </body></html>
                '''
            }
        
        return {
            'subject': 'EC2 Notification',
            'content': '<html><body><p>Notification content unavailable</p></body></html>'
        }
    
    def __del__(self):
        """Clean up MongoDB connection"""
        try:
            if hasattr(self, 'mongo_client'):
                self.mongo_client.close_connection()
        except:
            pass