import json
import os
import boto3
import logging
from botocore.exceptions import C<PERSON><PERSON><PERSON><PERSON>, WaiterError
from datetime import datetime
from email_templates import EmailTemplates
import traceback

# Set up logging
logger = logging.getLogger()
logger.setLevel(logging.INFO)

email_templates_helper = EmailTemplates()

def get_instance_name(instance_info):
    """Extract instance name from tags"""
    try:
        tags = instance_info.get('Tags', [])
        for tag in tags:
            if tag['Key'] == 'Name':
                return tag['Value']
    except Exception as e:
        logger.warning(f"Error extracting instance name from tags: {str(e)}")
    return 'Unnamed Instance'

def get_instance_details(ec2_client, instance_id):
    """Get comprehensive instance details"""
    try:
        describe_response = ec2_client.describe_instances(InstanceIds=[instance_id])
        instance_info = describe_response['Reservations'][0]['Instances'][0]
        
        # Extract key details
        details = {
            'instance_name': get_instance_name(instance_info),
            'instance_state': instance_info['State']['Name'],
            'instance_type': instance_info.get('InstanceType', 'Unknown'),
            'availability_zone': instance_info.get('Placement', {}).get('AvailabilityZone', 'Unknown'),
            'private_ip': instance_info.get('PrivateIpAddress', 'N/A'),
            'public_ip': instance_info.get('PublicIpAddress', 'N/A'),
            'launch_time': instance_info.get('LaunchTime', 'Unknown'),
            'vpc_id': instance_info.get('VpcId', 'Unknown'),
            'subnet_id': instance_info.get('SubnetId', 'Unknown')
        }
        
        # Format launch time if available
        if isinstance(details['launch_time'], datetime):
            details['launch_time'] = details['launch_time'].strftime('%Y-%m-%d %H:%M:%S UTC')
        
        return details, instance_info
        
    except ClientError as e:
        error_code = e.response['Error']['Code']
        error_message = e.response['Error']['Message']
        logger.error(f"Failed to describe instance {instance_id}: {error_code} - {error_message}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error describing instance {instance_id}: {str(e)}")
        raise

def send_notification(lambda_function_name, instance_id, instance_name, success, message, 
                     error_code=None, operation_details=None, duration=None):
    """Send notification using the provided Lambda function with enhanced error handling"""
    
    notification_attempts = 0
    max_attempts = 3
    
    while notification_attempts < max_attempts:
        try:
            notification_attempts += 1
            
            # Prepare additional info for the email template
            additional_info = {}
            if error_code:
                additional_info['Error Code'] = error_code
            if duration:
                additional_info['Operation Duration'] = f"{duration:.2f} seconds"
            if operation_details:
                additional_info.update(operation_details)
            
            # Get email content from template
            if success:
                email_data = email_templates_helper.get_reboot_notification_email(
                    instance_id=instance_id,
                    instance_name=instance_name,
                    success=success,
                    message=message,
                    additional_info=additional_info if additional_info else None
                )
            else:
                # Use error template for failures
                email_data = email_templates_helper.get_error_notification_email(
                    operation="Instance Reboot",
                    instance_id=instance_id,
                    instance_name=instance_name,
                    error_message=message,
                    error_code=error_code,
                    additional_info=additional_info if additional_info else None
                )
            
            # Prepare the event payload for the notification Lambda
            event_payload = {
                'subject': email_data['subject'],
                'content': email_data['content'],
                'attachments': None,
                'priority': 'high' if not success else 'normal'
            }
            
            logger.info(f"Sending notification via {lambda_function_name} (Attempt {notification_attempts})")
            
            # Initialize Lambda client and invoke notification function
            lambda_client = boto3.client('lambda')
            
            response = lambda_client.invoke(
                FunctionName=lambda_function_name,
                InvocationType='RequestResponse',  # Synchronous
                Payload=json.dumps(event_payload)
            )
            
            # Check if notification was sent successfully
            response_payload = json.loads(response['Payload'].read())
            
            if response.get('StatusCode') == 200:
                if isinstance(response_payload, dict) and response_payload.get('statusCode') == 200:
                    logger.info("Notification sent successfully")
                    return True
                else:
                    logger.warning(f"Notification Lambda returned non-200 status: {response_payload}")
                    if notification_attempts >= max_attempts:
                        break
            else:
                logger.warning(f"Lambda invocation failed with status {response.get('StatusCode')}: {response_payload}")
                if notification_attempts >= max_attempts:
                    break
                    
        except ClientError as e:
            error_code = e.response['Error']['Code']
            logger.error(f"Lambda invocation failed (attempt {notification_attempts}): {error_code} - {str(e)}")
            if notification_attempts >= max_attempts or error_code in ['ResourceNotFoundException', 'InvalidParameterValueException']:
                break
                
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse notification response (attempt {notification_attempts}): {str(e)}")
            if notification_attempts >= max_attempts:
                break
                
        except Exception as e:
            logger.error(f"Unexpected error sending notification (attempt {notification_attempts}): {str(e)}")
            if notification_attempts >= max_attempts:
                break
    
    logger.error(f"Failed to send notification after {max_attempts} attempts")
    return False

def lambda_handler(event, context):
    """
    Lambda function to reboot EC2 instance and send notification with comprehensive error handling
    
    Expected event structure:
    {
        "instance_id": "i-1234567890abcdef0",
        "wait_for_status_check": true  // Optional, default true
    }
    """
    
    start_time = datetime.utcnow()
    instance_id = None
    instance_name = "Unknown"
    notification_lambda_name = None
    operation_details = {}
    
    try:
        # Extract and validate parameters from event
        if not isinstance(event, dict):
            raise ValueError("Event must be a valid JSON object")
            
        instance_id = event.get('instance_id')
        notification_lambda_name = os.environ.get('NOTIFICATION_LAMBDA_NAME', "test-dmv-monitor")
        wait_for_status_check = event.get('wait_for_status_check', True)
        
        # Validate required parameters
        if not instance_id:
            raise ValueError("instance_id is required and cannot be empty")
        if not notification_lambda_name:
            raise ValueError("notification_lambda_name is required and cannot be empty")
            
        # Validate instance_id format (basic validation)
        if not instance_id.startswith('i-') or len(instance_id) < 10:
            raise ValueError(f"Invalid instance_id format: {instance_id}")
        
        logger.info(f"Starting reboot process for instance: {instance_id}")
        operation_details['Operation Start'] = start_time.strftime('%Y-%m-%d %H:%M:%S UTC')
        
        # Initialize EC2 client with error handling
        try:
            ec2_client = boto3.client('ec2')
        except Exception as e:
            raise RuntimeError(f"Failed to initialize EC2 client: {str(e)}")
        
        # Get instance details before reboot
        try:
            instance_details, instance_info = get_instance_details(ec2_client, instance_id)
            instance_name = instance_details['instance_name']
            instance_state = instance_details['instance_state']
            
            # Add instance details to operation details
            operation_details.update({
                'Instance Type': instance_details['instance_type'],
                'Availability Zone': instance_details['availability_zone'],
                'Private IP': instance_details['private_ip'],
                'Public IP': instance_details['public_ip']
            })
            
            logger.info(f"Instance {instance_id} ({instance_name}) current state: {instance_state}")
            
        except ClientError as e:
            error_code = e.response['Error']['Code']
            error_message = e.response['Error']['Message']
            
            if error_code == 'InvalidInstanceID.NotFound':
                error_msg = f"Instance {instance_id} does not exist"
            elif error_code == 'UnauthorizedOperation':
                error_msg = f"Insufficient permissions to describe instance {instance_id}"
            else:
                error_msg = f"Failed to describe instance {instance_id}: {error_code} - {error_message}"
            
            logger.error(error_msg)
            send_notification(notification_lambda_name, instance_id, instance_name, False, 
                            error_msg, error_code, operation_details)
            
            return {
                'statusCode': 400 if error_code in ['InvalidInstanceID.NotFound'] else 403,
                'body': json.dumps({
                    'success': False,
                    'message': error_msg,
                    'instance_id': instance_id,
                    'error_code': error_code
                })
            }
        
        # Check if instance is in a state that can be rebooted
        valid_states = ['running']
        if instance_state not in valid_states:
            error_msg = f"Instance {instance_id} ({instance_name}) is in '{instance_state}' state and cannot be rebooted. Instance must be in 'running' state."
            logger.warning(error_msg)
            operation_details['Current State'] = instance_state
            send_notification(notification_lambda_name, instance_id, instance_name, False, 
                            error_msg, None, operation_details)
            
            return {
                'statusCode': 400,
                'body': json.dumps({
                    'success': False,
                    'message': error_msg,
                    'instance_id': instance_id,
                    'instance_name': instance_name,
                    'current_state': instance_state
                })
            }
        
        # Perform the reboot operation
        try:
            reboot_start_time = datetime.utcnow()
            reboot_response = ec2_client.reboot_instances(InstanceIds=[instance_id])
            logger.info(f"Reboot initiated successfully for instance {instance_id}")
            
            operation_details['Reboot Initiated'] = reboot_start_time.strftime('%Y-%m-%d %H:%M:%S UTC')
            
            # Wait for the instance to pass status checks if requested
            if wait_for_status_check:
                try:
                    logger.info(f"Waiting for instance {instance_id} to pass status checks after reboot...")
                    waiter = ec2_client.get_waiter('instance_status_ok')
                    waiter.wait(
                        InstanceIds=[instance_id],
                        WaiterConfig={
                            'Delay': 15,  # Check every 15 seconds
                            'MaxAttempts': 40  # Wait up to 10 minutes
                        }
                    )
                    status_check_time = datetime.utcnow()
                    logger.info(f"Instance {instance_id} passed status checks successfully after reboot")
                    operation_details['Status Check Passed'] = status_check_time.strftime('%Y-%m-%d %H:%M:%S UTC')
                    
                except WaiterError as e:
                    logger.warning(f"Status check wait failed for instance {instance_id}: {str(e)}")
                    # Don't fail the entire operation for status check timeout
                    operation_details['Status Check'] = 'Timeout (operation may still be successful)'
                    
            # Calculate operation duration
            end_time = datetime.utcnow()
            duration = (end_time - start_time).total_seconds()
            operation_details['Operation End'] = end_time.strftime('%Y-%m-%d %H:%M:%S UTC')
            
            # Send success notification
            success_msg = f"EC2 instance {instance_id} ({instance_name}) reboot completed successfully"
            if wait_for_status_check and 'Status Check Passed' in operation_details:
                success_msg += " and passed all status checks"
            
            send_notification(notification_lambda_name, instance_id, instance_name, True, 
                            success_msg, None, operation_details, duration)
            
            return {
                'statusCode': 200,
                'body': json.dumps({
                    'success': True,
                    'message': success_msg,
                    'instance_id': instance_id,
                    'instance_name': instance_name,
                    'operation_duration_seconds': duration,
                    'timestamp': end_time.isoformat(),
                    'details': operation_details
                })
            }
            
        except ClientError as e:
            error_code = e.response['Error']['Code']
            error_message = e.response['Error']['Message']
            
            # Provide specific error messages for common scenarios
            if error_code == 'IncorrectInstanceState':
                error_msg = f"Cannot reboot instance {instance_id}: Instance state changed during operation"
            elif error_code == 'InsufficientInstanceCapacity':
                error_msg = f"Cannot reboot instance {instance_id}: Insufficient capacity in availability zone"
            elif error_code == 'UnauthorizedOperation':
                error_msg = f"Insufficient permissions to reboot instance {instance_id}"
            else:
                error_msg = f"Failed to reboot instance {instance_id}: {error_code} - {error_message}"
            
            logger.error(error_msg)
            
            # Calculate partial duration
            duration = (datetime.utcnow() - start_time).total_seconds()
            
            # Send failure notification
            send_notification(notification_lambda_name, instance_id, instance_name, False, 
                            error_msg, error_code, operation_details, duration)
            
            return {
                'statusCode': 500,
                'body': json.dumps({
                    'success': False,
                    'message': error_msg,
                    'instance_id': instance_id,
                    'instance_name': instance_name,
                    'error_code': error_code,
                    'operation_duration_seconds': duration
                })
            }
            
    except ValueError as e:
        error_msg = f"Invalid input parameters: {str(e)}"
        logger.error(error_msg)
        
        # Try to send notification if we have enough info
        if instance_id and notification_lambda_name:
            send_notification(notification_lambda_name, instance_id, instance_name, False, error_msg)
        
        return {
            'statusCode': 400,
            'body': json.dumps({
                'success': False,
                'message': error_msg,
                'error_type': 'ValidationError'
            })
        }
        
    except RuntimeError as e:
        error_msg = f"System error: {str(e)}"
        logger.error(error_msg)
        
        # Try to send notification if we have enough info
        if instance_id and notification_lambda_name:
            send_notification(notification_lambda_name, instance_id, instance_name, False, error_msg)
        
        return {
            'statusCode': 503,
            'body': json.dumps({
                'success': False,
                'message': error_msg,
                'error_type': 'SystemError'
            })
        }
        
    except Exception as e:
        # Capture full stack trace for debugging
        error_trace = traceback.format_exc()
        error_msg = f"Unexpected error during reboot operation: {str(e)}"
        logger.error(f"{error_msg}\nStack trace:\n{error_trace}")
        
        # Calculate partial duration if possible
        duration = None
        try:
            duration = (datetime.utcnow() - start_time).total_seconds()
        except:
            pass
        
        # Try to send failure notification if we have the required info
        if instance_id and notification_lambda_name:
            send_notification(notification_lambda_name, instance_id, instance_name, False, 
                            error_msg, None, operation_details, duration)
        
        return {
            'statusCode': 500,
            'body': json.dumps({
                'success': False,
                'message': error_msg,
                'error_type': 'UnexpectedError',
                'instance_id': instance_id,
                'operation_duration_seconds': duration
            })
        }