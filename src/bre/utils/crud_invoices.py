# This module contains a class for interacting with the Emails collection in the MongoDB database.

import os
import datetime

from utils.mongo_utils import Mongo
from utils.boto3_utils import get_secret


class CrudInvoices:
    def __init__(self, mongo):
        self.mongo = mongo
        self.collection_name = 'invoice'
    
    def update_invoice_vin(self, vin, data):
        query = {"vin": vin}
        data['updated_at'] = datetime.datetime.now()
        data["status_history"] = ["Pending"]
        data = {"$set": data} 
        self.mongo.select_db_and_collection(os.environ['MONGO_DATABASE'], collection_name=self.collection_name)
        self.mongo.update_one(query, data)

    def update_invoice_by_wi_id(self, aria_wi_id, data_to_set, data_to_push):
        query = {"aria_wi_id": aria_wi_id}
        data_to_set['updated_at'] = datetime.datetime.now()
        data = {"$set": data_to_set, "$push": data_to_push} 
        self.mongo.select_db_and_collection(os.environ['MONGO_DATABASE'], collection_name=self.collection_name)
        self.mongo.update_one(query, data)


    def find_invoice_vin(self, vin):
        """
        This function finds an email by its email ID.
        """
        query = {"vin": vin}
        self.mongo.select_db_and_collection(os.environ['MONGO_DATABASE'], collection_name=self.collection_name)
        return self.mongo.find_one(query)


    def __del__(self):
        self.mongo.close_connection()
