# This module contains a class for interacting with the Folios collection in the MongoDB database.

import os
import time
from datetime import datetime
from utils.mongo_utils import Mongo
from utils.boto3_utils import get_secret


class CrudVins:
    def __init__(self, mongo):
        self.mongo = mongo
        self.collection_name='vin'

    def update_row_by_vin(self, vin, data_to_set, data_to_push):
        query = {"vin": vin}
        data = {"$set": data_to_set, "$push": data_to_push} 
        self.mongo.select_db_and_collection(os.environ['MONGO_DATABASE'], collection_name=self.collection_name)
        self.mongo.update_one(query, data)

    def find_report_row_by_vin(self, vin):
        """
        This function finds an email by its email ID.
        """
        query = {"vin": vin}
        self.mongo.select_db_and_collection(os.environ['MONGO_DATABASE'], collection_name=self.collection_name)
        response = self.mongo.find_one(query)
        
        return response 

    def __del__(self):
        self.mongo.close_connection()