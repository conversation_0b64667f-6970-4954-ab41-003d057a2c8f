# This module contains utility functions and a class for interacting with the Outlook API.

import os
import msal
import base64
import requests
import urllib.parse
import datetime
from microsoft_oauth2_client import MicrosoftOAuth2<PERSON>lient

from boto3_utils import get_secret

MAIL_FOLDERS_ENDPOINT = 'https://graph.microsoft.com/v1.0/users/{user_id}/mailFolders?$top=20'
MAIL_SUBFOLDERS_ENDPOINT = 'https://graph.microsoft.com/v1.0/users/{user_id}/mailFolders/Inbox/childFolders'
IDENTITY_ENDPOINT = 'https://login.microsoftonline.com/'
GRAPH_ENDPOINT = 'https://graph.microsoft.com/v1.0/users/'
GET_EMAIL_FROM_INTERNET_MESSAGE_ID = "https://graph.microsoft.com/v1.0/users/{user_id}/messages?$filter=internetMessageId eq \'{email_internet_message_id}\'"

def print_error_details(message, response):
    """
    This function prints the details of the error response.
    """
    print(message)
    print('Status code:', response.status_code)
    print('Response content:', response.json())

class Outlook:
    """
    This class provides methods to interact with the Outlook API.
    """
    def __init__(self, client):
        """
        This method initializes the Outlook class with the client credentials.
        """
        credentials = get_secret(os.environ['ENV'] + '-email_credentials')
        self.config = credentials[client]
        self.tenan_id = self.config['TENANT_ID']
        self.user_id = self.config['USERID']
        self.client_id = self.config['CLIENT_ID']
        self.client_secret = self.config['CLIENT_SECRET']
        self.oauth_client = MicrosoftOAuth2Client()
        self.access_token = self.get_access_token()

    def get_access_token(self):
        """Gets the access token from MicrosoftOAuth2Client"""
        return self.oauth_client.get_access_token()

    def get_access_token(self):
        """
        This function retrieves an access token from Azure AD using the client credentials flow.
        """
        authority_url = f"{IDENTITY_ENDPOINT}{self.tenan_id}"
        scopes = ['https://graph.microsoft.com/.default']

        # Initialize the MSAL app with the client credentials
        app = msal.ConfidentialClientApplication(
            self.client_id,
            authority=authority_url,
            client_credential=self.client_secret
        )
        # Acquire a token for the client
        result = app.acquire_token_for_client(scopes)

        # Check if the access token was successfully acquired
        if 'access_token' in result:
            return result['access_token']
        else:
            # If the access token was not acquired, print the error details and raise an exception
            print('Failed to acquire token:', result.get('error'), result.get('error_description'))
            raise Exception('Failed to acquire access token')
            
    def get_id_from_email(self, email_internet_message_id):
        """Retrieves the email ID based on its internet message ID"""
        
        email_id = urllib.parse.quote(email_internet_message_id)
        endpoint = GET_EMAIL_FROM_INTERNET_MESSAGE_ID.format(user_id=self.user_id, email_internet_message_id=email_id)

        access_token = self.get_access_token()
    
        if access_token:
            headers = {'Authorization': f'Bearer {access_token}'}
            response = requests.get(endpoint, headers=headers)
    
            if response.status_code == 200:
                emails = response.json().get('value', [])
                for email in emails:
                    return email['id']
            else:
                print('Error fetching emails:', response.json())
                
        return None
    
    def get_folder_id(self, folder_name, endpoint=MAIL_FOLDERS_ENDPOINT):
        """Retrieves the folder ID based on its name"""
        endpoint = endpoint.format(user_id=self.user_id)

        if self.access_token:
            headers = {'Authorization': f'Bearer {self.access_token}'}
            response = requests.get(endpoint, headers=headers)

            if response.status_code == 200:
                folders = response.json().get('value', [])
                for folder in folders:
                    if folder['displayName'] == folder_name:
                        return folder['id']
            else:
                print('Error fetching folders:', response.json())
                
        
        if endpoint == MAIL_FOLDERS_ENDPOINT.format(user_id=self.user_id):
            return self.get_folder_id(folder_name, MAIL_SUBFOLDERS_ENDPOINT)
        
        return None


    def get_emails_from_folder(self, folder_name, top_fetch=None, filter_value=None):
        """
        This function fetches emails from the specified folder.
        """

        # Get the folder ID
        if folder_name.lower() == "inbox":
            folder_id = "inbox"
        else:
            folder_id = self.get_folder_id(folder_name)
            if folder_id is None:
                print(f"Folder '{folder_name}' not found.")
                return None

        print(f"Folder_id {folder_id}")
        
        # Construct the endpoint URL
        query_params = []
        if filter_value:
            query_params.append(f'$filter={filter_value}')
        else:
            query_params.append('$orderby=receivedDateTime desc')

        if top_fetch:
            query_params.append(f'$top={top_fetch}')
        

        endpoint = f'{GRAPH_ENDPOINT}{self.user_id}/mailFolders/{folder_id}/messages' + \
                     ('?' if query_params else '') + '&'.join(query_params)
        
        print(endpoint)
        
        # Fetch the emails
        access_token = self.get_access_token()
        headers = {'Authorization': 'Bearer ' + access_token}
        r = requests.get(endpoint, headers=headers)
        if r.ok:
            # If the request was successful, return the emails
            data = r.json()
            return data['value']
        
        # If the request failed, print the error details and return None
        print_error_details('Failed to fetch emails', r)
        return None
    
    def get_email_attachments(self, email_internet_message_id):
        """
        Fetches the attachments of an email identified by internetMessageId.
        """
        email_id = self.get_id_from_email(email_internet_message_id)
        if not email_id:
            raise Exception(f'Email with internetMessageId {email_internet_message_id} not found.')
    
        endpoint = f'{GRAPH_ENDPOINT}{self.user_id}/messages/{email_id}/attachments'
        access_token = self.get_access_token()
        headers = {'Authorization': 'Bearer ' + access_token}
        r = requests.get(endpoint, headers=headers)
        if r.ok:
            data = r.json()
            return data['value']
        
        print_error_details('Failed to fetch attachments', r)
        raise Exception(f'Failed to fetch attachments for email with internetMessageId: {email_internet_message_id}')
    
    def download_attachment(self, attachment_id, email_internet_message_id):
        """
        Downloads the specified attachment by internetMessageId.
        """
        email_id = self.get_id_from_email(email_internet_message_id)
        if not email_id:
            raise Exception(f'Email with internetMessageId {email_internet_message_id} not found.')
    
        endpoint = f'{GRAPH_ENDPOINT}{self.user_id}/messages/{email_id}/attachments/{attachment_id}/$value'
        access_token = self.get_access_token()
        headers = {'Authorization': 'Bearer ' + access_token}
        r = requests.get(endpoint, headers=headers)
        if r.ok:
            return r.content
        
        print_error_details('Failed to download attachment', r)
        return None
    
    def send_csv_report(self, csv_content, csv_name, subject, body, emails):
        """
        This function sends an email with the CSV report attached.
        """

        endpoint = GRAPH_ENDPOINT + self.user_id + '/sendMail'
        access_token = self.get_access_token()

        email_msg = {
            "Message": {
                "Subject": subject,
                "Body": {
                    "ContentType": "Text",
                    "Content": body
                },
                "ToRecipients": [
                    {
                        "EmailAddress": {
                            "Address": user
                        }
                    } for user in emails
                ],
                "Attachments": [
                    {
                        "@odata.type": "#microsoft.graph.fileAttachment",
                        "Name": csv_name,
                        "ContentBytes": base64.b64encode(csv_content.encode()).decode()
                    }
                ]
            },
            "SaveToSentItems": "true"
        }

        r = requests.post(endpoint, headers={'Authorization': 'Bearer ' + access_token}, json=email_msg)
        if r.ok:
            print('Sent email successfully')
        else:
            raise Exception(f'Failed to send email: {r.status_code} - {r.json()}')

