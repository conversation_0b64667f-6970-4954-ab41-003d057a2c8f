import json
import boto3
import os
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict
from mongo_utils import MongoClientUtils

logger = logging.getLogger()
logger.setLevel(logging.INFO)

class ParallelMonitor:
    def __init__(self):
        self.mongo_utils = MongoClientUtils()
        self.lambda_client = boto3.client('lambda')
        self.lambda_function_name = f'{os.environ["ENV"]}-{os.environ.get("MONITOR_LAMBDA_FUNCTION_NAME", "error_wi_bot_report")}'
        
    def get_eligible_app_ids(self) -> List[str]:
        """Get app IDs that have 'ready_for_dms' status in their status config"""
        eligible_apps = []
        
        try:
            # Get all app IDs from the App collection
            all_apps = self.mongo_utils.db[self.mongo_utils.APP_COLLECTION].find({}, {"app_id": 1})
            
            for app in all_apps:
                app_id = app.get('app_id')
                if not app_id:
                    continue
                    
                # Check if this app has a status config with ready_for_dms
                status_config = self.mongo_utils.get_status_config(app_id)
                if self.has_ready_for_dms_status(status_config):
                    eligible_apps.append(app_id)
                    logger.info(f"App {app_id} is eligible for monitoring (has ready_for_dms status)")
                else:
                    logger.info(f"App {app_id} skipped (no ready_for_dms status)")
            
            logger.info(f"Found {len(eligible_apps)} eligible applications for monitoring")
            return eligible_apps
            
        except Exception as e:
            logger.error(f"Error getting eligible app IDs: {str(e)}")
            return []
    
    def has_ready_for_dms_status(self, status_config: Dict) -> bool:
        """Check if status config contains ready_for_dms status"""
        if not status_config or 'status' not in status_config:
            return False
        
        for status_id, status_info in status_config['status'].items():
            if status_info.get('key') == 'ready_for_dms':
                return True
        return False
    
    def invoke_lambda_for_app(self, app_id: str, base_event: Dict) -> Dict:
        """Invoke the main lambda function for a specific app"""
        try:
            # Create event for this specific app
            app_event = base_event.copy()
            app_event['app_id'] = app_id
            app_name = self.mongo_utils.get_app_name(app_id)
            logger.info(f"Invoking lambda for app {app_id}: {app_name}")

            response = self.lambda_client.invoke(
                FunctionName=self.lambda_function_name,
                InvocationType='RequestResponse',  # Synchronous invocation
                Payload=json.dumps(app_event)
            )
            
            # Parse response
            payload = json.loads(response['Payload'].read())
            
            # Add app_id to response for tracking
            payload['app_id'] = app_id
            payload['invocation_success'] = True
            payload['app_name'] = app_name
            logger.info(f"Successfully invoked lambda for app {app_id}: {app_name}")
            return payload

            
        except Exception as e:
            logger.error(f"Error invoking lambda for app {app_id}: {str(e)}")
            return {
                'app_id': app_id,
                'invocation_success': False,
                'error': str(e),
                'statusCode': 500
            }
    
    def monitor_apps_parallel(self, max_workers: int = 5) -> Dict:
        """Monitor all eligible apps in parallel"""
        try:            
            # Get eligible app IDs
            eligible_app_ids = self.get_eligible_app_ids()
            
            if not eligible_app_ids:
                logger.warning("No eligible applications found for monitoring")
                return {
                    'statusCode': 200,
                    'body': json.dumps({
                        'message': 'No eligible applications found for monitoring',
                        'eligible_apps': 0,
                        'results': []
                    })
                }
            
            # Prepare standard event for each app invocation
            base_event = {
                'print_all': False,  # Only print if there are alerts
                'send_email': True   # Send emails for alerts
            }
            
            # Execute monitoring in parallel
            results = []
            failed_apps = []
            successful_apps = []
            
            logger.info(f"Starting parallel monitoring for {len(eligible_app_ids)} applications with {max_workers} workers")
            
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # Submit all tasks
                future_to_app = {
                    executor.submit(self.invoke_lambda_for_app, app_id, base_event): app_id 
                    for app_id in eligible_app_ids
                }
                
                # Collect results as they complete
                for future in as_completed(future_to_app):
                    app_id = future_to_app[future]
                    try:
                        result = future.result()
                        results.append(result)
                        
                        if result.get('invocation_success', False):
                            successful_apps.append(app_id)
                        else:
                            failed_apps.append(app_id)
                            
                    except Exception as e:
                        logger.error(f"Exception occurred for app {app_id}: {str(e)}")
                        failed_apps.append(app_id)
                        results.append({
                            'app_id': app_id,
                            'invocation_success': False,
                            'error': str(e),
                            'statusCode': 500
                        })
            
            # Aggregate summary statistics
            total_alerts = sum(
                json.loads(r.get('body', '{}')).get('alerts_count', 0) 
                for r in results if r.get('invocation_success', False)
            )
            
            total_status_changes = sum(
                json.loads(r.get('body', '{}')).get('status_changes_count', 0) 
                for r in results if r.get('invocation_success', False)
            )
            
            # Prepare response
            response_data = {
                'message': 'Parallel monitoring completed',
                'execution_config': {
                    'max_workers': max_workers,
                    'eligible_apps': len(eligible_app_ids),
                    'successful_apps': len(successful_apps),
                    'failed_apps': len(failed_apps)
                },
                'summary': {
                    'total_alerts_across_apps': total_alerts,
                    'total_status_changes_across_apps': total_status_changes,
                    'successful_app_ids': successful_apps,
                    'failed_app_ids': failed_apps
                },
                'individual_results': results
            }
            
            logger.info(f"Parallel monitoring completed. {len(successful_apps)} successful, {len(failed_apps)} failed")
            
            return {
                'statusCode': 200,
                'body': json.dumps(response_data, default=str)
            }
            
        except Exception as e:
            logger.error(f"Error in parallel monitoring: {str(e)}")
            return {
                'statusCode': 500,
                'body': json.dumps({
                    'error': str(e),
                    'message': 'Failed to complete parallel monitoring'
                })
            }
        finally:
            self.mongo_utils.close_connection()

def lambda_handler(event, context):
    """Lambda entry point for parallel monitoring - designed for scheduler"""
    
    # Fixed configuration for scheduled runs
    max_workers = 5  # Process 5 apps in parallel
    
    logger.info(f"Starting scheduled parallel monitoring with {max_workers} workers")
    
    parallel_monitor = ParallelMonitor()
    return parallel_monitor.monitor_apps_parallel(max_workers)

# # For local testing
# if __name__ == "__main__":
#     # Test parallel monitoring with default scheduler configuration
#     result = parallel_lambda_handler({}, None)
#     print(json.dumps(result, indent=2, default=str))