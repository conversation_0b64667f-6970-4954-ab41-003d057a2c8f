from boto3_utils import get_secret
from pymongo import MongoClient
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import logging

logger = logging.getLogger()

class MongoClientUtils:
    def __init__(self):
        mongo_uri = get_secret(f"{os.environ['ENV']}-mongodb_uri", return_json=False)
        self.client = MongoClient(mongo_uri)
        self.db = self.client[os.environ['MONGO_DATABASE']]
        
        # Collection names
        self.DOCUMENT_COLLECTION = 'Document'
        self.STATUS_CONFIG_COLLECTION = 'StatusConfig'
        self.TRACKING_COLLECTION = 'WorkItemTracking'
        self.EVENT_COLLECTION = 'Event'
        self.VIN_COLLECTION = 'vin'
        self.APP_COLLECTION = 'App'

    def get_status_config(self, app_id: str) -> Optional[Dict]:
        """Get status configuration for an app"""
        try:
            status_config = self.db[self.STATUS_CONFIG_COLLECTION].find_one({"app_id": app_id})
            return status_config
        except Exception as e:
            logger.error(f"Error fetching status config for app {app_id}: {str(e)}")
            return None
    
    def get_app_name(self, app_id: str) -> str:
        """Get application name from App collection"""
        try:
            app_doc = self.db[self.APP_COLLECTION].find_one({"app_id": app_id})
            if app_doc:
                return app_doc.get('name', app_id)
            else:
                return app_id
        except Exception as e:
            logger.error(f"Error fetching app name for {app_id}: {str(e)}")
            return app_id


    def close_connection(self):
        """Close MongoDB connection"""
        if self.client:
            self.client.close()