import json
import os
import sys
from bson import ObjectId
from datetime import datetime, timezone
from pymongo import MongoClient
# from common.aria_helper.boto3_utils import get_secret
# from common.aria_helper.mongo_utils import Mongo

# Environment setup
# environment = os.environ.get('ENV', 'dev')
# database_name = os.environ.get('DATABASE_NAME', 'hennessy_db')

# MongoDB setup
# mongo_client = Mongo(get_secret(environment + '-mongodb_uri', return_json=False))
# mongo_client.select_db_and_collection(database_name, 'py1')  # single document per app_id

# Local development mongo setup

# For local development - replace with your local MongoDB connection
local_mongo_uri = "mongodb://localhost:27017"  # Default local MongoDB
local_database_name = "hennessy_local"  # Local database name

client = MongoClient(local_mongo_uri)
db = client[local_database_name]

files_to_seed = [
    ("new_collection_data.json", "validation_config"),  # <PERSON><PERSON> expects "validation_config" collection
    ("hennessy.prompts.json", "prompts"),               # <PERSON><PERSON> expects "prompts" collection
    ("hennessy.Workitem.json", "Workitem"),             # Lambda expects "Workitem" collection
    ("hennessy.Wordblock.json", "Wordblock"),           # Lambda expects "Wordblock" collection
    ("action_config.json", "action_config"),            # Lambda expects "action_config" collection (optional)
    ("llm_config.json", "llm_config")                   # Lambda expects "llm_config" collection (optional)
]

def convert_extended_json(obj):
    if isinstance(obj, dict):
        if "$oid" in obj:
            return ObjectId(obj["$oid"])
        if "$date" in obj:
            return datetime.fromisoformat(obj["$date"].replace("Z", "+00:00"))
        return {k: convert_extended_json(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_extended_json(item) for item in obj]
    else:
        return obj

def seed(app_id: str):
    inserted_count = 0

    for file_name, collection_name in files_to_seed:
        if not os.path.exists(file_name):
            print(f"❌ Missing: {file_name}")
            continue

        # Get the specific collection for this data type
        collection = db[collection_name]

        # Delete existing records for this app_id first (if applicable)
        if collection_name in ["validation_config", "action_config", "llm_config"]:
            collection.delete_many({"app_id": app_id})
        elif collection_name == "Workitem":
            collection.delete_many({"app_id": app_id})
        elif collection_name == "Wordblock":
            # For Wordblock, we might want to delete by source_file pattern
            collection.delete_many({"source_file": {"$regex": f".*\\.json$"}})
        elif collection_name == "prompts":
            # For prompts, we can delete all and re-insert (or keep existing)
            # collection.delete_many({})  # Uncomment if you want to clear all prompts
            pass

        with open(file_name, 'r', encoding='utf-8') as f:
            raw = json.load(f)
            parsed = convert_extended_json(raw)

            if collection_name in ["validation_config", "action_config", "llm_config"]:
                # For config collections, add app_id to the document
                document = parsed.copy()
                document["app_id"] = app_id
                document["created_at"] = datetime.now(timezone.utc)
                collection.insert_one(document)
                inserted_count += 1
                print(f"✅ Inserted {collection_name} for app_id: {app_id}")

            elif collection_name in ["prompts", "Wordblock"]:
                # For prompts and Wordblock, insert the array items directly
                if isinstance(parsed, list):
                    collection.insert_many(parsed)
                    inserted_count += len(parsed)
                    print(f"✅ Inserted {len(parsed)} {collection_name} documents")
                else:
                    collection.insert_one(parsed)
                    inserted_count += 1
                    print(f"✅ Inserted {collection_name} document")

            elif collection_name == "Workitem":
                # For Workitem, insert the array items directly
                if isinstance(parsed, list):
                    collection.insert_many(parsed)
                    inserted_count += len(parsed)
                    print(f"✅ Inserted {len(parsed)} Workitem documents")
                else:
                    collection.insert_one(parsed)
                    inserted_count += 1
                    print(f"✅ Inserted Workitem document")

    print(f"✅ Total {inserted_count} documents inserted across collections for app_id: {app_id}")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("❌ Usage: python seeder.py <app_id>")
        sys.exit(1)

    app_id_arg = sys.argv[1]
    seed(app_id_arg)
