import json
from microsoft_graph_email_client import MicrosoftGraphEmailClient 
import os

def lambda_handler(event, context):
    if os.getenv('MOVE_EMAILS', "True") == "False":
        return {
                'statusCode': 200,
                'body': json.dumps('Lambda execution send emails disabled')
            }
        
    """Lambda function that handles the event"""
    print(f"Received event: {event}")
    action = event.get('action')
    email_id = event.get('email_id')
    destination_folder = event.get('destination_folder')
    user_id = event.get('user_id')
    folder_name = event.get('folder_name')

    email_client = MicrosoftGraphEmailClient(user_id)

    if action == 'move_email':
        if not email_id or not destination_folder or not user_id:
            return {
                'statusCode': 400,
                'body': json.dumps('Missing parameters: email_id, destination_folder, user_id')
            }
            
        # Call the function to move the email
        print(f"moving email {email_id} to {destination_folder}")
        result = email_client.move_email(email_id, destination_folder)
    
    elif action == 'move_all_emails':
        if not destination_folder or not user_id:
            return {
                'statusCode': 400,
                'body': json.dumps('Missing parameters: destination_folder, user_id')
            }
        # Call the function to move all emails from the specified folder
        source_folder = folder_name  # Assuming you want to move from the specified folder
        result = email_client.move_all_emails_from_folder(source_folder, destination_folder)

    elif action == 'list_folders':
        if not user_id:
            return {
                'statusCode': 400,
                'body': json.dumps('Missing parameter: user_id')
            }
        # Call the function to list the folders
        result = email_client.list_folders()

    elif action == 'list_emails_from_folder':
        if not folder_name or not user_id:
            return {
                'statusCode': 400,
                'body': json.dumps('Missing parameters: folder_name, user_id')
            }
        # Call the function to list the emails in a folder
        result = email_client.list_emails_from_folder(folder_name)
    
    else:
        result = {
            'statusCode': 400,
            'body': json.dumps(f'Unknown action: {action}')
        }
    
    return result
