import os
import json
import base64
import boto3
from outlook_utils import Outlook

def lambda_handler(event, context):
    """
    Universal email sending Lambda function using existing Outlook utils
    
    Expected event structure:
    {
        "subject": "Email Subject",
        "content": "Email HTML content",
        "attachments": [ // optional
            {
                "filename": "document.pdf",
                "content": "base64_encoded_content", // OR
                "path": "/tmp/file.pdf", // OR  
                "s3_bucket": "my-bucket", "s3_key": "path/to/file.pdf"
            }
        ]
    }
    """
    
    try:
        # Validate required fields
        if 'subject' not in event or 'content' not in event:
            return {
                'statusCode': 400,
                'body': json.dumps({'error': 'Missing required fields: subject and content are required'})
            }
        
        # Initialize Outlook service
        outlook = Outlook()
        
        # Extract email parameters
        subject = event['subject']
        content = event['content']
        
        # Get recipients from environment variables
        recipients = [email.strip() for email in os.environ.get('REPORTS_EMAIL', '').split(',')]
        recipients = [r for r in recipients if r]  # Remove empty strings
        
        if not recipients:
            return {
                'statusCode': 400,
                'body': json.dumps({'error': 'No recipients configured in REPORTS_EMAIL environment variable'})
            }
        
        # Get sender from environment variable
        sender = os.environ.get('REPORTER_EMAIL')
        if not sender:
            return {
                'statusCode': 400,
                'body': json.dumps({'error': 'No sender configured in REPORTER_EMAIL environment variable'})
            }
        
        # Get BCC from environment variable
        bcc = []
        bcc_emails = os.environ.get('BCC_EMAIL', '')
        if bcc_emails:
            bcc = [email.strip() for email in bcc_emails.split(',')]
            bcc = [b for b in bcc if b]  # Remove empty strings
        
        # Prepare email configuration for Outlook utils
        emails = {
            "sender": sender,
            "recipients": recipients,
            "bcc": bcc
        }
        
        # Handle attachments - process them for Outlook utils compatibility
        processed_attachments = None
        if event.get('attachments'):
            processed_attachments = []
            for attachment in event['attachments']:
                try:
                    processed_attachment = {
                        'filename': attachment.get('filename', 'attachment')
                    }
                    
                    # Handle different content formats
                    if 'content' in attachment:
                        # Base64 encoded content
                        processed_attachment['content'] = attachment['content']
                    elif 'path' in attachment:
                        # File path - read and encode to base64
                        with open(attachment['path'], 'rb') as file:
                            file_content = file.read()
                            processed_attachment['content'] = base64.b64encode(file_content).decode('utf-8')
                    elif 's3_key' in attachment and 's3_bucket' in attachment:
                        # S3 object - download and encode to base64
                        s3_client = boto3.client('s3')
                        obj = s3_client.get_object(Bucket=attachment['s3_bucket'], Key=attachment['s3_key'])
                        file_content = obj['Body'].read()
                        processed_attachment['content'] = base64.b64encode(file_content).decode('utf-8')
                    else:
                        print(f"Invalid attachment format: {attachment}")
                        continue
                        
                    processed_attachments.append(processed_attachment)
                    print(f"Processed attachment: {attachment.get('filename', 'unknown')}")
                    
                except Exception as e:
                    print(f"Failed to process attachment {attachment.get('filename', 'unknown')}: {e}")
                    continue
        
        # Send email using existing Outlook utils
        try:
            outlook.send_email_notification(
                subject=subject,
                body=content,
                emails=emails,
                attachments=processed_attachments
            )
            
            return {
                'statusCode': 200,
                'body': json.dumps({
                    'message': 'Email sent successfully',
                    'recipients': len(recipients),
                    'attachments': len(processed_attachments) if processed_attachments else 0
                })
            }
            
        except Exception as e:
            print(f"Failed to send email via Outlook utils: {e}")
            return {
                'statusCode': 500,
                'body': json.dumps({'error': f'Failed to send email: {str(e)}'})
            }
            
    except Exception as e:
        print(f"Lambda execution error: {e}")
        return {
            'statusCode': 500,
            'body': json.dumps({'error': f'Lambda execution failed: {str(e)}'})
        }