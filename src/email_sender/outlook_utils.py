import os
import msal
import smtplib
import requests
import base64
from email.mime.multipart import MIME<PERSON><PERSON>ipart
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email import encoders
from datetime import datetime
from boto3_utils import get_secret
import json

IDENTITY_ENDPOINT = 'https://login.microsoftonline.com/'
GRAPH_ENDPOINT = 'https://graph.microsoft.com/v1.0/users/'

class Outlook:
    def __init__(self):
        """
        This method initializes the Outlook class with the client credentials.
        """
        credentials = get_secret(os.environ['ENV'] + '-email_credentials')
        self.config = credentials['aria_cloud']
        
        self.username = self.config['USERNAME_REPORTER']
        self.password = self.config['PASS_REPORTER_EMAIL']
        self.smtp = self.config['SMTP']
        self.port = self.config['PORT']


    # def send_email_notification(self, subject, body, emails):
    #     message = MIMEMultipart()
    #     message['From'] = emails['sender']
    #     message['To'] = ", ".join(emails['recipients'])
    #     message['Subject'] = subject
    #     message.attach(MIMEText(body, 'html'))

    #     try:
    #         server = smtplib.SMTP(self.smtp, self.port)
    #         server.starttls() 

    #         server.login(self.username, self.password)

    #         text = message.as_string()
    #         server.sendmail(emails['sender'], emails['recipients'] + emails['bcc'], text)
    #         print('Email sent successfully')
    #     except Exception as e:
    #         print(f"Error: {e}")
            
    #     finally:
    #         server.quit()


    def send_email_notification(self, subject, body, emails, attachments=None):
        """
        Send email notification with optional attachments
        
        Args:
            subject (str): Email subject
            body (str): Email body content (HTML)
            emails (dict): Dictionary with 'sender', 'recipients', 'bcc' keys
            attachments (list, optional): List of attachment dictionaries with 'filename' and 'content' (base64)
        """
        message = MIMEMultipart()
        message['From'] = emails['sender']
        message['To'] = ", ".join(emails['recipients'])
        message['Subject'] = subject
        message.attach(MIMEText(body, 'html'))

        # Add attachments if provided
        if attachments:
            for attachment in attachments:
                try:
                    # Create attachment part
                    part = MIMEBase('application', 'octet-stream')
                    
                    # Decode base64 content
                    file_content = base64.b64decode(attachment['content'])
                    part.set_payload(file_content)
                    
                    # Encode in base64 for email
                    encoders.encode_base64(part)
                    
                    # Add header with filename
                    part.add_header(
                        'Content-Disposition',
                        f'attachment; filename= {attachment.get("filename", "attachment")}'
                    )
                    
                    message.attach(part)
                    print(f"Added attachment: {attachment.get('filename', 'unknown')}")
                    
                except Exception as e:
                    print(f"Failed to add attachment {attachment.get('filename', 'unknown')}: {e}")
                    continue

        try:
            server = smtplib.SMTP(self.smtp, self.port)
            server.starttls() 
            server.login(self.username, self.password)

            # Include BCC recipients in the actual sending
            all_recipients = emails['recipients'][:]
            if emails.get('bcc'):
                all_recipients.extend(emails['bcc'])

            text = message.as_string()
            server.sendmail(emails['sender'], all_recipients, text)
            print('Email sent successfully')
            return True
            
        except Exception as e:
            print(f"Error: {e}")
            return False
            
        finally:
            server.quit()

    def get_access_token(self):
        """
        This function retrieves an access token from Azure AD using the client credentials flow.
        """
        authority_url = f"{IDENTITY_ENDPOINT}{self.tenan_id}"
        scopes = ['https://graph.microsoft.com/.default']

        # Initialize the MSAL app with the client credentials
        app = msal.ConfidentialClientApplication(
            self.client_id,
            authority=authority_url,
            client_credential=self.client_secret
        )
        # Acquire a token for the client
        result = app.acquire_token_for_client(scopes)

        # Check if the access token was successfully acquired
        if 'access_token' in result:
            return result['access_token']
        else:
            # If the access token was not acquired, print the error details and raise an exception
            print('Failed to acquire token:', result.get('error'), result.get('error_description'))
            raise Exception('Failed to acquire access token')

    def generate_email_body_html(self, state_machine_name, execution_id, error_info):
        """
        Generates an HTML email body for a Step Function error notification.
        Only includes: state machine name, execution ID, and error info.
        """
        timestamp = datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')

        error_json = json.dumps(error_info, indent=2)

        body = f"""
        <html>
        <body>
            <div style="font-family: Arial, sans-serif; background-color: #f8f8f8; padding: 20px;">
                <div style="background-color: #ffffff; border-radius: 8px; padding: 20px; max-width: 600px; margin: auto; box-shadow: 0 0 10px rgba(0,0,0,0.1);">
                    <h2 style="color: #cc0000;">🚨 Step Function error execution</h2>

                    <p><strong>🧱 State Machine:</strong> {state_machine_name}</p>
                    <p><strong>⚙️ Execution ID:</strong> {execution_id}</p>
                    <p><strong>🕓 Timestamp:</strong> {timestamp}</p>

                    <p><strong>📝 Error information:</strong></p>
                    <pre style="background-color: #f2f2f2; padding: 10px; border-radius: 5px; font-size: 13px;">{error_json}</pre>

                    <p style="margin-top: 20px; font-size: 0.9em; color: #777;">This is an automated message.</p>
                </div>
            </div>
        </body>
        </html>
        """
        return body
