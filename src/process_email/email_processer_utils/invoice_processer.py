from email_processer_utils.email_processer import EmailProcesser
from boto3_utils    import download_file_from_s3, post_to_s3, trigger_lambda_response
import json
import os
import base64
from aria_utils     import TemporalUnavailableException
import traceback
import uuid
import re
from outlook_utils  import Outlook

class InvoiceProcesser(EmailProcesser):

    def __init__(self, logger_class, email_id, stage):
        self.app_id = os.environ['ARIA_APP_ID_USED_CARS']
        super().__init__(logger_class, email_id, stage)
        self.client_outlook = Outlook(self.env + '_hennessy_used_cars')

    def save_invoice_in_s3(self, file_content, bucket, path, file_name, file_id):
        try:
            post_to_s3(file_content, bucket, path, file_name)
        except Exception as e:
            attachment_error = f"Error posting PDF to S3: {str(e)}"
            self.logger.error(attachment_error)
            self.crud_emails.failed_attachment(self.email['email_id'], file_id, attachment_error)
        
    def save_invoice_in_db(self, file_id, file_name, s3_file_path, wi_id):
        try:
            self.crud_invoices.insert_invoice(
                self.stage, self.email['email_id'], file_id, file_name, s3_file_path, wi_id
            )
        except Exception as e:
            attachment_error = f"WorkItem was created but could not be inserted in DB: {str(e)}"
            self.logger.error(attachment_error)
            self.crud_emails.failed_attachment(self.email['email_id'], file_id, attachment_error)

    
    def split_attachment_into_individual_invoices(self, s3_file_path, file_name):
        pdf_processer_lambda_response = trigger_lambda_response(os.environ['PDF_PROCESSER_LAMBDA'], {"action": "process_document", "s3_file": f"s3://{self.bucket}/{s3_file_path}/{file_name}"})
        if isinstance(pdf_processer_lambda_response, str):
            pdf_processer_lambda_response = json.loads(pdf_processer_lambda_response)
       
        print(f" ****** RESPONSE FROM PDF_PROCESSOR ****** ")
        print(pdf_processer_lambda_response)
        print("********************************************")

        if pdf_processer_lambda_response['statusCode'] != 200:
            print("Error response from pdf_utils:", pdf_processer_lambda_response)
            raise Exception(f"Error response from pdf_utils: {pdf_processer_lambda_response['body']}")
        
        pdf_processer_lambda_response = json.loads(pdf_processer_lambda_response['body'])

        # We return all the pages except the final page cause its a resume page that isnt an image if the
        # document has more than one page, else we return the single page
        if len(pdf_processer_lambda_response['splitted_pages']) == 1:
            return pdf_processer_lambda_response['splitted_pages']
        return pdf_processer_lambda_response['splitted_pages'][:-1]

    
    def process_invoice(self, file_id, file_name, file_content):
        s3_file_path = self.generate_s3_path(os.environ['ATTACHMENTS_FOLDER'], file_id)
        self.save_invoice_in_s3(file_content, self.bucket, s3_file_path, file_name, file_id)

        invoices_s3_urls = self.split_attachment_into_individual_invoices(s3_file_path, file_name)

        if invoices_s3_urls:

            for invoice_s3_path in invoices_s3_urls:
                file_name = "invoice.pdf"
                file_path = f"/tmp/{file_name}"
                downloaded = download_file_from_s3(invoice_s3_path, file_path)
                if downloaded:
                    
                    invoice_file_content = None
                    with open(file_path, "rb") as f:
                        invoice_file_content = f.read()

                    wi_id = ""
                    try:
                        self.aria_utils.construct_create_request_used_cars(base64.b64encode(invoice_file_content).decode('utf-8'), file_id, invoice_s3_path)
                        work_item_data = self.aria_utils.send_post_request()
                        wi_id = work_item_data['id']
                    except TemporalUnavailableException as e:
                        wi_id = ""
                    except Exception as e:
                        print(f"Error processing bol {file_id}: {traceback.format_exc()}")
                        raise Exception(f"Error processing invoice {file_id}: {str(e)}")
                
                    self.save_invoice_in_db(file_id, file_name, invoice_s3_path, wi_id)
                    self.crud_emails.processed_attachment(self.email['email_id'], file_id)
                
                else:
                    raise Exception(f"Couldnt download an invoice {invoice_s3_path}")


    def process_attachments(self):
        for attachment in self.attachments:
            self.logger.info(f"Processing attachment {attachment['attachment_id']}")

            # Check if attachment is a pdf
            if not self.is_pdf(attachment['attachment_name']):
                self.logger.info(f"Attachment {attachment['attachment_id']} is not a PDF. Skipping...")
                self.crud_emails.not_applicable_attachment(self.email['email_id'], attachment['attachment_id'])
                continue
            
        
            # Check if the invoice already exists
            invoice_exists = self.crud_invoices.find_invoice_by_id(attachment['attachment_id'])

            if invoice_exists:
                # For now, the BRE will handle duplicated invoices. Log the error and continue
                self.logger.info(f"Invoice already exists for attachment {attachment['attachment_id']}")

            # Download the attachment
            try:
                file_content = self.client_outlook.download_attachment(attachment['attachment_id'], self.email['email_id'])
                attachment_path = f"/tmp/{attachment['attachment_name']}"
                with open(attachment_path, 'wb') as file:
                    file.write(file_content)

            except Exception as e:
                attachment_error = f"Error downloading attachment {attachment['attachment_id']}: {str(e)}"
                self.logger.error(attachment_error)
                self.crud_emails.failed_attachment(self.email['email_id'], attachment['attachment_id'], attachment_error)
                continue
            
            self.process_invoice(attachment['attachment_id'], attachment['attachment_name'], file_content)
        
    def run(self):

        self.process_attachments()


        # Send logs to S3
        #self.logger_class.send_log_to_s3(os.environ['PROCESS_EMAIL_LAMBDA'])

        return {
            'statusCode': 200,
            'body': json.dumps({"message": "Email succesfully processed"})
        }