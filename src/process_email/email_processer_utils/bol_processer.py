from email_processer_utils.email_processer import EmailProcesser
from boto3_utils    import json_encoder, post_to_s3, trigger_lambda_response
import json
import os
import base64
from aria_utils     import TemporalUnavailableException
import traceback
import uuid
import re
from outlook_utils  import Outlook

class BolProcesser(EmailProcesser):

    def __init__(self, logger_class, email_id, stage):
        self.app_id = os.environ['ARIA_APP_ID']
        super().__init__(logger_class, email_id, stage)
        self.client_outlook = Outlook(self.env + '_hennessy')

    def save_bol_in_s3(self, file_content, bucket, path, file_name, file_id):
        try:
            post_to_s3(file_content, bucket, path, file_name)
        except Exception as e:
            attachment_error = f"Error posting PDF to S3: {str(e)}"
            self.logger.error(attachment_error)
            self.crud_emails.failed_attachment(self.email['email_id'], file_id, attachment_error)
        
    def save_bol_in_db(self, file_id, file_name, s3_file_path, file_ocr, wi_id):
        try:
            self.crud_bols.insert_bol(
                self.email['email_id'], file_id, file_name, s3_file_path, file_ocr, wi_id
            )
        except Exception as e:
            attachment_error = f"WorkItem was created but could not be inserted in DB: {str(e)}"
            self.logger.error(attachment_error)
            self.crud_emails.failed_attachment(self.email['email_id'], file_id, attachment_error)

    def construct_msg_classifier_bol(self, image_uri):
        return {
            "provider": "openai",
            "host": "public",
            "llm_model":"gpt-4o-vision",
            "prompt": "Your a nice assistant",
            "message": """

            You are a lead data analyst tasked with classifying unstructured documents to determine if they are Bills of Lading (BOL). A BOL is defined as a legal document issued by a carrier to a shipper, confirming the receipt of goods for transport. It serves as a shipment receipt, contract of carriage, and document of title, detailing the type, quantity, and destination of the goods. The consignee requires it to claim the shipment upon arrival.

                For a document to be classified as a BOL, it must include:
                - At least one signature (sometimes two)
                - Vehicle Identification Numbers (VINs)
                - Additional related information
                
                Take in mind that a PRE DELIVERY (A Pre-Delivery Notice (PDN) is a document that serves as confirmation that a vehicle has undergone a final inspection before being delivered to the customer. It ensures that the vehicle meets quality standards and is free of defects.) is NOT a BOL 
                also a VEHICLE INVOICE (A vehicle invoice is a document issued by the manufacturer to a dealership, detailing the cost of a vehicle before any markups or discounts. It includes key information about the vehicle, its pricing, and additional fees.) is not a BOL. 

                If at the start of the document you find 'RELIABLE CARRIERS INC' we need to check if the 'Delivered on' is filled. If its filled we will take it as BOL, otherwise no (if its empty).

                Based on the provided document, generate the output following the raw specified format:
                
                {
                    "BOL": "<true or false>"
                }
                
                
                Output Requirements:
                - Provide only the JSON object without any additional text or explanations.
                - Do not include 'json' tags or any special symbols; output should only contain the `{}` characters.

            """,
            "files": [image_uri]
        }
    
    def clasify_if_attachment_is_bol(self, s3_file_path, file_name):
        pdf_processer_lambda_response = trigger_lambda_response(os.environ['PDF_PROCESSER_LAMBDA'], {"action": "process_document", "s3_file": f"s3://{self.bucket}/{s3_file_path}/{file_name}"})
        if isinstance(pdf_processer_lambda_response, str):
            pdf_processer_lambda_response = json.loads(pdf_processer_lambda_response)
       
        print(f" ****** RESPONSE FROM PDF_PROCESSOR ****** ")
        print(pdf_processer_lambda_response)
        print("********************************************")

        if pdf_processer_lambda_response['statusCode'] != 200:
            return {
                "statusCode": 500,
                "body": json.dumps({"message": "Error response from pdf_utils."})
            }
        
        pdf_processer_lambda_response = json.loads(pdf_processer_lambda_response['body'])


        msg_to_clasify =  self.construct_msg_classifier_bol(pdf_processer_lambda_response['splitted_images'][0])
        llm_lambda_response = trigger_lambda_response(os.environ['LLM_MESSENGER_LAMBDA'], msg_to_clasify)
        llm_lambda_response = json.loads(llm_lambda_response)
        print(f" ****** RESPONSE FROM LLM ****** ")
        print(llm_lambda_response)
        print("********************************************")

        if llm_lambda_response['statusCode'] != 200:
            return {
                "statusCode": 500,
                "body": json.dumps({"message": "Error response from llm."})
            }
        
        llm_lambda_response = json.loads(llm_lambda_response['body'])
        llm_lambda_response = llm_lambda_response['message']
        #print(llm_lambda_response, type(llm_lambda_response))

        if "false" in llm_lambda_response.lower():
            print("Skipping attachment, classified as no bol")
            return False, ""
        
        file_ocr = self.textract_handler.get_text_from_file(file_name, s3_file_path)    
 
        return True, file_ocr
    
    def process_bol(self, file_id, file_name, file_content):
        s3_file_path = self.generate_s3_path(os.environ['ATTACHMENTS_FOLDER'], file_id)
        self.save_bol_in_s3(file_content, self.bucket, s3_file_path, file_name, file_id)

        is_bol, file_ocr = self.clasify_if_attachment_is_bol(s3_file_path, file_name)

        if is_bol:
            
            wi_id = ""
            try:
                self.aria_utils.construct_create_request(base64.b64encode(file_content).decode('utf-8'), file_id)
                work_item_data = self.aria_utils.send_post_request()
                wi_id = work_item_data['id']
            except TemporalUnavailableException as e:
                wi_id = ""
            except Exception as e:
                print(f"Error processing bol {file_id}: {traceback.format_exc()}")
                return {
                    'statusCode': 500,
                    'body': json.dumps({"message": f"Error processing bol: {e}"})
                }
        

            self.save_bol_in_db(file_id, file_name, s3_file_path, file_ocr, wi_id)
            self.crud_emails.processed_attachment(self.email['email_id'], file_id)

            mov_email_msg = {
                "action": "move_email",
                "email_id": self.email['email_id'],
                "destination_folder": "Invoke",
                "user_id": "<EMAIL>"
            }

            trigger_lambda_response(os.environ['MOVE_EMAIL_LAMBDA'], mov_email_msg)


    def process_attachments(self):
        for attachment in self.attachments:
            self.logger.info(f"Processing attachment {attachment['attachment_id']}")

            # Check if attachment is a pdf
            if not self.is_pdf(attachment['attachment_name']):
                self.logger.info(f"Attachment {attachment['attachment_id']} is not a PDF. Skipping...")
                self.crud_emails.not_applicable_attachment(self.email['email_id'], attachment['attachment_id'])
                continue
            
        
            # Check if the folio already exists
            bol_exists = self.crud_bols.find_bol_by_id(attachment['attachment_id'])

            if bol_exists:
                # For now, the BRE will handle duplicated folios. Log the error and continue
                self.logger.info(f"Folio already exists for attachment {attachment['attachment_id']}")

            # Download the attachment
            try:
                file_content = self.client_outlook.download_attachment(attachment['attachment_id'], self.email['email_id'])
                attachment_path = f"/tmp/{attachment['attachment_name']}"
                with open(attachment_path, 'wb') as file:
                    file.write(file_content)

            except Exception as e:
                attachment_error = f"Error downloading attachment {attachment['attachment_id']}: {str(e)}"
                self.logger.error(attachment_error)
                self.crud_emails.failed_attachment(self.email['email_id'], attachment['attachment_id'], attachment_error)
                continue
            
            self.process_bol(attachment['attachment_id'], attachment['attachment_name'], file_content)
        
    def process_urls(self):
        email_body = self.email['body']
        urls = re.findall(r'http[s]*:\/\/[^\s<>"]+', email_body)

        filenames = []
        for url in urls:
            if self.is_pdf_url(url):
                filename, content = self.download_pdf(url)
                filenames.append((filename, content))

        for filename, content in filenames:
            attachment_id = uuid.uuid4()
            self.process_bol(attachment_id, filename, content)
        

    def run(self):

        self.process_attachments()
        self.process_urls()


        # Send logs to S3
        #self.logger_class.send_log_to_s3(os.environ['PROCESS_EMAIL_LAMBDA'])

        return {
            'statusCode': 200,
            'body': json.dumps({"message": "Email succesfully processed"})
        }