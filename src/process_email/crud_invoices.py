# This module contains a class for interacting with the Folios collection in the MongoDB database.

import os
import datetime

from mongo_utils import Mongo
from boto3_utils import get_secret


class CrudInvoices:
    def __init__(self):
        mongo_uri = get_secret(os.environ['ENV'] + '-mongodb_uri', return_json=False)
        self.mongo = Mongo(mongo_uri)
        self.mongo.select_db_and_collection(
            db_name=os.environ['MONGO_DATABASE'],
            collection_name='invoice'
        )

    def insert_invoice(self, stage, email_id, attachment_id, attachment_name, path, wi_id):
        """
        This function inserts a folio into the database.
        """

        invoice_document = {
            "vin": "",
            "file_path": path,
            "read_at": datetime.datetime.now(),
            "current_flow": stage,
            "aria_app_id": os.environ['ARIA_APP_ID'] if stage == "post-inventory" else os.environ['ARIA_APP_ID_USED_CARS'],
            "aria_wi_id": wi_id,
            "sent_to_aria": True,
            "email_id": email_id,
            "attachment_name": attachment_name,
            "attachment_id": attachment_id,
            "updated_at": datetime.datetime.now(),
            "status_history": ["Pending"],
            "status": "Pending",
        }

        self.mongo.insert_one(invoice_document)

    def find_invoice_by_id(self, attachment_id):
        """
        This function finds a invoice by its attachment ID.
        """
        query = {"attachment_id": attachment_id}
        return self.mongo.find_one(query)
    
    
    def find_invoice_by_email_id(self, invoice_id):
        """
        This function finds a invoice by its ID.
        """
        query = {"aria_wi_id": invoice_id}
        return self.mongo.find_one(query)

    def find_invoices_by_filter(self, filter):
        """
        This function finds invoices by a filter.
        """
        return list(self.mongo.find(filter))
    
    def __del__(self):
        self.mongo.close_connection()