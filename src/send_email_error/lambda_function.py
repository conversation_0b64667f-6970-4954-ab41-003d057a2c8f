import os
import json
from outlook_utils import Outlook

def lambda_handler(event, context):

    execution_id = event.get('execution_id', None)
    error_info = event.get('error_info', None)
    state_machine_name = event.get('state_machine_name', None)

    if not execution_id or not error_info or not state_machine_name:
        return {
            'statusCode': 400,
            'body': 'Missing required parameters: execution_id, error_info, or state_machine_name.'
        }

    try:
        env = os.environ.get('ENV')
        outlook = Outlook()
        support_emails = [email.strip() for email in os.environ.get('REPORTS_EMAIL', '').split(',')]
        copy_emails = [email.strip() for email in os.environ.get('BCC_EMAIL', '').split(',')]
        sender = os.environ.get('REPORTER_EMAIL')
        
        subject = f"{env.upper()} - Stepfunction Error Notification: {state_machine_name}"
        body = outlook.generate_email_body_html(state_machine_name, execution_id, error_info)
        
        outlook.send_email_notification(subject=subject, body=body, emails={
            "sender": sender,
            "recipients": support_emails,
            "bcc": copy_emails
        })
    except Exception as e:
        print(f"Error sending email: {e}")
        return {'statusCode': 500, 'body': json.dumps({'message': f"Error sending email: {e}"})}
    