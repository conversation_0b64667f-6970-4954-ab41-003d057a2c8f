from boto3_utils import trigger_lambda_response, download_file_from_s3
from aria_utils import AriaUtils
from crud_titles import CrudTitles
import traceback
import os
import json
import base64
import uuid

def lambda_handler(event, context):

    title_id = uuid.uuid4()

    print(f"***** PROCESSING {title_id} *****")
    print(event)

    try:

        try:
            titles_pages = event['title_page_to_process']
            from_title_file = event['from_file']
            is_mso = event['is_mso']
            concated_title = ""
        except Exception:
            print("Pages of titles not found on event!")
            return
        
        aria_utils = AriaUtils()
        crud_titles = CrudTitles()
        
        print(" ***** CONCATENATING IMAGES ***** ")

        pdf_processer_lambda_response = trigger_lambda_response(os.environ['PDF_PROCESSER_LAMBDA'], {"action": "concat_images", "s3_files": titles_pages})
        pdf_processer_lambda_response = json.loads(pdf_processer_lambda_response)

        print(f" ****** RESPONSE FROM PDF_PROCESSOR ****** ")
        print(pdf_processer_lambda_response)
        print("********************************************")

        if pdf_processer_lambda_response['statusCode'] != 200:
            print("Error response from pdf_utils.")
            raise Exception("Error response from pdf_utils.")
        
        pdf_processer_lambda_response = json.loads(pdf_processer_lambda_response['body'])

        concated_title = pdf_processer_lambda_response['file_url']

        file_name = concated_title.split("//")[-1].split("/")[-1]
        file_path = f"/tmp/{file_name}"

        download_file_from_s3(concated_title, file_path)

        with open(file_path, 'rb') as file:
            file_content = file.read()
        
        print(f" ****** SENDING TITLE TO ARIA {title_id} ******  ")
        
        aria_utils.construct_create_request(base64.b64encode(file_content).decode('utf-8'), title_id, is_mso, from_title_file.split("//")[-1].split("/")[-1])
        work_item_data = aria_utils.send_post_request()
        wi_id = work_item_data['id']
        crud_titles.insert_title(title_id, concated_title, titles_pages, from_title_file, wi_id, "Success", is_mso)

        return {
            "statusCode": 202,
            "response": "Title processed correctly"
        }
    
    except Exception as e:
        print(f"Error when processing title: {traceback.format_exc()}")
        crud_titles.insert_title(title_id, concated_title, titles_pages, from_title_file, "", "FailedFromARIA", is_mso)
        raise Exception(f"Error when processing title: {str(e)}")
