# This module contains a class for interacting with the Folios collection in the MongoDB database.

import os
from datetime import datetime
from mongo_utils import Mongo
from boto3_utils import get_secret


class CrudTitles:
    def __init__(self):
        mongo_uri = get_secret(os.environ['ENV'] + '-mongodb_uri', return_json=False)
        self.mongo = Mongo(mongo_uri)
        self.mongo.select_db_and_collection(
            db_name=os.environ['MONGO_DATABASE'],
            collection_name='title'
        )

    def insert_title(self, uuid, concated_title, images, title_file, aria_wi_id, status, is_mso=False):
        """
        This function inserts a folio into the database.
        """

        title_document = {
            "title_id": str(uuid),
            "images_of_title": images,
            "path": concated_title,
            "from_file": title_file,
            "aria_wi_id": aria_wi_id,
            "aria_app_id": os.environ['ARIA_APP_ID'],
            "read_at": datetime.now(),
            "status_history": ["Pending"],
            "is_mso": is_mso,
            "status": status
        }

        self.mongo.insert_one(title_document)
    
    def __del__(self):
        self.mongo.close_connection()