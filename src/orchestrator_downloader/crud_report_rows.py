# This module contains a class for interacting with the Emails collection in the MongoDB database.

import os
import datetime

from mongo_utils import Mongo
from boto3_utils import get_secret


class CrudReynolsReport:
    def __init__(self, stage):
        mongo_uri = get_secret(os.environ['ENV'] + '-mongodb_uri', return_json=False)
        self.mongo = Mongo(mongo_uri)
        self.mongo.select_db_and_collection(
            db_name=os.environ['MONGO_DATABASE'],
            collection_name='vin'
        )
        self.store_path = f"flows.{stage}.report-data.store"
        self.downloaded_path = f"flows.{stage}.docs.invoice.downloaded_at"

        secret_name = ""
        if stage == "post-inventory":
            secret_name = "supported_stores"
        elif stage == "pre-inventory":
            secret_name = "supported_stores_pre_inventory"
        elif stage == "used-cars":
            secret_name = ""

        self.stage = stage
        if secret_name != "":
            self.supported_stores = get_secret(f'{os.environ['ENV']}-{secret_name}', return_json=True)

    def find_report_row_by_vin(self, vin):
        """
        This function finds an email by its email ID.
        """
        query = {"vin": vin}
        return self.mongo.find_one(query)

    def get_vins_to_download(self, stage):
        """
        This function retrieves all VINs that have not been downloaded yet.
        - VINs in status 0 or 2; they can be retried every hour until they are downloaded
        - VINs in status 1; they can be retried every 4 hours and just before 3 days have passed since they were read
        """
        read_at_threshold = datetime.datetime.now() - datetime.timedelta(days=59, hours=22)
        updated_at_threshold = datetime.datetime.now() - datetime.timedelta(hours=4)

        # for porsche only download invoice if it has bol/ro date
        query = {
            "$or": [
                # Case 1: PORSCHE
                {
                    "$and": [
                        {f"flows.{stage}.report_data.make": "PORSCHE"},
                        {f"flows.{stage}.docs.invoice.downloaded_at": {"$in": [None, ""]}},
                        {"$or":
                            [
                                {
                                    "$and": [
                                        { f"flows.{stage}.docs.invoice.fields.bol_date.value": { "$exists": True } },
                                        { f"flows.{stage}.docs.invoice.fields.bol_date.value": { "$nin": [None, ""] } }
                                    ]
                                },
                                {
                                    "$and": [
                                        {f"flows.{stage}.docs.invoice.fields.ro_date.value": {"$exists": True}},
                                        {f"flows.{stage}.docs.invoice.fields.ro_date.value": {"$nin": [None, ""]}}
                                    ]
                                }
                            ]
                        }
                    ]
                },
                # Case 2: NON-PORSCHE
                {
                    "$and": [
                        {f"flows.{stage}.report_data.make": {"$ne": "PORSCHE"}},
                        {f"flows.{stage}.docs.invoice.downloaded_at": {"$in": [None, ""]}},
                        {
                            "$or": [
                                # Simple statuses
                                {f"flows.{stage}.status": {"$in": [0, 2]}},
                                # Status 1 with additional time-based checks
                                {
                                    "$and": [
                                        {f"flows.{stage}.status": 1},
                                        {
                                            "$or": [
                                                {f"flows.{stage}.read_at": {"$lte": read_at_threshold}},
                                                {f"flows.{stage}.updated_at": {"$lte": updated_at_threshold}},
                                            ]
                                        },
                                    ]
                                },
                            ]
                        },
                    ]
                },
            ]
        }

        stores = self.mongo.select_distinct(query, self.store_path)
        # Filter only supported stores
        stores = [store for store in stores if store in self.supported_stores]

        vins_per_store = []
        for store in stores:
            query[self.store_path] = store
            vins_per_store.append({
                "store": store,
                "vins": self.mongo.select_distinct(query, "vin")
            })

        return vins_per_store

    def get_vins_to_download_used_cars(self):
        """
        This function retrieves all VINs that have not been downloaded yet.
        - VINs in status 0 or 2; they can be retried every hour until they are downloaded
        - VINs in status 1; they can be retried every 4 hours and just before 3 days have passed since they were read
        """
        query = {
            self.downloaded_path: {"$in": [None, ""]},
            'flows.used-cars.report-data.stock': {"$regex": "P$"},
            '$or': [
                {'flows.used-cars.status': 0},
                {'flows.used-cars.status': 2},
                {'flows.used-cars.status': 1,
                 'flows.used-cars.read_at': {"$lte": datetime.datetime.now() - datetime.timedelta(days=59, hours=22)}},
                {'flows.used-cars.status': 1,
                 'flows.used-cars.updated_at': {"$lte": datetime.datetime.now() - datetime.timedelta(hours=4)}},
            ],

        }

        return [{
            "store": "MANHEIM",
            "vins": self.mongo.select_distinct(query, "vin")
        }]