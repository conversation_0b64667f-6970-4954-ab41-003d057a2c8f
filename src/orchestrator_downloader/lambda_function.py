from boto3_utils        import get_latest_files, download_file_from_s3
from crud_report_rows   import CrudReynolsReport

from datetime           import datetime
import json

def lambda_handler(event, context):

    print(event)

    stage = event.get("stage", None)
    action = event.get("action", "download_vins")
    if (stage is None or stage == "") or (action is None or action == ""):
        return {
            'statusCode': 500,
            'body': {
                "message": json.dumps(f"Error no stage or action provided!")
            }
        }


    crud_report_rows = CrudReynolsReport(stage)

    if action == "download_vins":
        
        vins_per_store = []
        if stage == "used-cars":
            vins_per_store = crud_report_rows.get_vins_to_download_used_cars()
        else:
            vins_per_store = crud_report_rows.get_vins_to_download(stage)

        print(" ****** DOWNLOADING INVOICES ****** ")
        print("Found the following VINs to download:\n")
        output = []
        for vin in vins_per_store:
            print("Store: ", vin['store'])
            print("VINs: ", vin['vins'], "\n"*2)

            store = vin['store']

            brands_vin_mapping = {} 
            batches_per_store = []
            for vin in vin['vins']:
                vin_data = crud_report_rows.find_report_row_by_vin(vin)
                brand = vin_data["flows"][stage]["report-data"]["make"]
                brands_vin_mapping.setdefault(brand, []).append(vin)

            for brand, vins in brands_vin_mapping.items():
                # Split in batches of 15
                vins_batches = [vins[i:i + 15] for i in range(0, len(vins), 15)]
                batches_per_store.extend({"brand": brand, "vins": vins_batch} for vins_batch in vins_batches)

            print(f"{len(batches_per_store)} batches to download")
            output.append({
                "store": store,
                "batches": batches_per_store
            })


        return {
            "statusCode": 200,
            "body": output,
            "stage": stage,
            "action": "invoice_download"
        }
    
    elif action == "download_new_vehicles_report":

        return {
            "statusCode": 200,
            "body": [{"store": store, } for store in crud_report_rows.supported_stores],
            "action": "download_new_vehicles_report",
            "stage": stage
        }
