import pymongo

class Mongo():
    def __init__(self, mongo_uri):
        self.client = pymongo.MongoClient(mongo_uri)
        self.db = ''
        self.col = ''

    def create_collection(self, collection_name):
        self.db.create_collection(collection_name)

    def check_if_collection_exists(self, collection_name):
        print('Collections already existing:')
        print(self.db.list_collection_names())
        return True if collection_name in self.db.list_collection_names() else False

    def select_db_and_collection(self, db_name, collection_name):
        self.db = self.client[db_name]
        if not self.check_if_collection_exists(collection_name):
            print('Creating collection!')
            self.create_collection(collection_name)
        self.col = self.db.get_collection(collection_name)

    def insert_one(self, data):
        self.col.insert_one(data)
        
    def insert_many(self, data):
        self.col.insert_many(data)

    def count_documents(self, data):
        x = self.col.count_documents(data)
        return x

    def find_one(self, data):
        x = self.col.find_one(data, {"_id": 0})
        return x

    def find(self, data, sort_field, limit=0, ascending=True):
        x = self.col.find(data, {"_id": 0}) \
                    .sort(sort_field, pymongo.ASCENDING if ascending else pymongo.DESCENDING) \
                    .limit(limit)
        return x
    
    def find_one_and_update(self, filter, data):
        x = self.col.find_one_and_update(filter, data, projection={'_id': 0})
        return x

    def update_one(self, filter, data):
        self.col.update_one(filter, data) 

    def update_many(self, filter, data):
        self.col.update_many(filter, data)

    def delete_one(self, data):
        self.col.delete_one(data) 

    def delete_many(self, data):
        self.col.delete_many(data)            

    def close_connection(self):
        self.client.close()