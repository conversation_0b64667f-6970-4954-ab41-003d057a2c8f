from crud_report_rows   import <PERSON>rud<PERSON>ey<PERSON>lsReport
from outlook_utils import Outlook
from boto3_utils import post_to_s3, trigger_lambda, get_secret
from datetime           import datetime
from mongo_utils import Mongo
import json
import os
from aria_utils import AriaUtils

def get_if_exists(obj, key, default=None):
    """
    Get the value of a key in a dictionary if it exists, otherwise return the default value.
    """
    key_parts = key.split('.')
    for part in key_parts:
        if obj is None:
            return default
        obj = obj.get(part, None)
    return obj

def generate_aria_report(stored_vins, failed_vins, vins_to_report):

    stored_vins_vin = [vin['vin'] for vin in stored_vins]
    print("stored_vins_vin", stored_vins_vin)

    failed_vins_vin = [vin['vin'] for vin in failed_vins]
    print("failed_vins_vin", failed_vins_vin)

    discarded_vins_vin = [vin['vin'] for vin in vins_to_report]
    print("discarded_vins_vin", discarded_vins_vin)

    all_vins = stored_vins_vin + failed_vins_vin + discarded_vins_vin

    trigger_lambda(os.environ['REPORT_TO_ARIA_LAMBDA'], {"body": {"vins": all_vins, "type": "download"}})

def lambda_handler(event, context):

    print(event)
    
    """
    Input:
    [
        [
            {
                "vin": "1HGCM82633A002352",
                "stored": True,
                "error": None
            },
            {
                "vin": "1HGCM82633A002353",
                "stored": False,
                "error": None
            }
        ],
        [
            {
                "vin": "1HGCM82633A002354",
                "stored": True,
                "error": None
            }
        ], 
        [
            {
                "vin": "1HGCM82633A002356",
                "stored": False,
                "error": "Failed to download"
            }
        ]
    ]

    This lambda function receives the output from the orchestrator in lotes of max 15 VINs per batch.
    It will update their status in the database and generate a report of every VIN that could not be
    downloaded in the last 3 days.
    """
    
    env = os.environ['ENV']
    
    stage = None
    # Firstly, join all the VINs in a single list    
    vins_to_update = []
    for result_set in event:
        for result in result_set:
            # Parse the result body
            result_body = json.loads(result.get('result', {}).get('body', '{}'))
            # Retrieve the VINs and their status
            for vin, vin_data in result_body.items():
                vins_to_update.append({
                    "vin": vin,
                    "stored": vin_data.get("stored", False),
                    "error": vin_data.get("error", None),
                    "store": result.get('store', None)
                })
                
            stage = result.get('stage', None)
    
    if (stage is None or stage == "") and len(vins_to_update) != 0:
        raise ValueError("No stage provided in the event data")
    
    crud_report_rows = CrudReynolsReport(stage)

    app_id = None
    if stage == 'post-inventory':
        app_id = os.environ['POST_INVENTORY_APP_ID']
    elif stage == 'pre-inventory':
        app_id = os.environ['PRE_INVENTORY_APP_ID']
    elif stage == 'used-cars':
        app_id = None

    aria_utils = AriaUtils(app_id) 

    
    print(" ****** UPDATING INVOICES STATUS ****** ")
    print(f"Found {len(vins_to_update)} VINs to update")

    # Failed vins
    failed_vins = list(filter(lambda vin: vin['stored'] == False, vins_to_update))
    # Split failed vins in group: Not found and Unexpected error
    handled_errors = ["VIN not found", "Invoice not found", "Not Avaliable", "Build sheet not found"]
    not_found_vins = list(filter(lambda vin: vin['error'] in handled_errors , failed_vins))
    unexpected_error_vins = list(filter(lambda vin: vin['error'] not in handled_errors, failed_vins))
    print(f"\t- {len(failed_vins)} VINs failed to download. From these, {len(unexpected_error_vins)} had an unexpected error")

    crud_report_rows.update_many(
        filter={'vin': {'$in': [vin['vin'] for vin in unexpected_error_vins]}},
        data={'$set': {f'flows.{stage}.status': 2, f'flows.{stage}.updated_at': datetime.now()}, '$push': {f'flows.{stage}.status_history': 2}}
    )

    crud_report_rows.update_many(
        filter={'vin': {'$in': [vin['vin'] for vin in not_found_vins]}},
        data={'$set': {f'flows.{stage}.status': 1, f'flows.{stage}.updated_at': datetime.now()}, '$push': {f'flows.{stage}.status_history': 1}}
    )

    # Stored vins
    stored_vins = list(filter(lambda vin: vin['stored'], vins_to_update))

    # Update the status of the stored vins
    crud_report_rows.update_many(
        filter={'vin': {'$in': [vin['vin'] for vin in stored_vins]}},
        data={'$set': {f'flows.{stage}.docs.invoice.downloaded_at': datetime.now(), f'flows.{stage}.status': 3}, '$push': {f'flows.{stage}.status_history': 3}}
    )

    print(" DATABASE UPDATED SUCCESSFULLY ")

    # Client report - failed vins in the last 30 days
    vins_to_report = []
    if stage == 'post-inventory':
        vins_to_report = crud_report_rows.get_vins_to_report_post_inventory()
    elif stage == 'pre-inventory':
        pass
    elif stage == 'used-cars':
        vins_to_report = crud_report_rows.get_vins_to_report_used_cars()


    vins_to_report_formatted = [vin['vin'] for vin in vins_to_report]

    if vins_to_report_formatted:
        # Update the status of the vins to report
        crud_report_rows.update_many(
            filter={'vin': {'$in': [vins_to_report_formatted]}},
            data={'$set': {f'flows.{stage}.status': 12}, '$push': {f'flows.{stage}.status_history': 12}} # Discarded
        )

        trigger_lambda(os.environ['REPORT_TO_ARIA_LAMBDA'], {"body": {"vins": vins_to_report_formatted, "type": "discarded", "stage": stage}})

    else:
        print("No failed vins in the last 3 days")


    if len(unexpected_error_vins) > 0:
        outlook_client = Outlook()
        mongo_uri = get_secret(os.environ['ENV'] + '-mongodb_uri', return_json=False)
        mongo_client = Mongo(mongo_uri)
        mongo_client.select_db_and_collection(
            db_name=os.environ['MONGO_DATABASE'],
            collection_name='vin'
        )
        pipeline = [
            {
                '$match': {
                    'vin': {'$in': [vin['vin'] for vin in unexpected_error_vins]}
                }
            },
            {
                "$project": {
                    "store": f"$flows.{stage}.report-data.store"
                }
            },
            {
                "$group": {
                    "_id": "$store"
                }
            }
        ]
        stores_errors_vins = list(mongo_client.col.aggregate(pipeline))

        stores_errors_vins = [store['_id'] for store in stores_errors_vins]

        body = outlook_client.generate_email_body_html(
            stage=stage,
            stores_error=stores_errors_vins
        )

        support_emails = [email.strip() for email in os.environ.get('REPORTS_EMAIL', '').split(',')]
        copy_emails = [email.strip() for email in os.environ.get('BCC_EMAIL', '').split(',')]
        sender = os.environ.get('REPORTER_EMAIL')
        
        outlook_client.send_email_notification(
            subject=f"{os.environ['ENV'].upper()} - Invoice Download Error Notification",
            body=body,
            emails={
                "sender": sender,
                "recipients": support_emails,
                "bcc": copy_emails
            }
        )
    

    # Update ARIA with the exception messages for the VINs that had been tried to download
    vin_groups = [
        (stored_vins, ""),
        (not_found_vins, "Invoice Not Found"),
        (unexpected_error_vins, "Invoice Download Error, Retrying"),
        (vins_to_report, "Invoice not found for more than 30 days")
    ]

    for vins, message in vin_groups:
        for vin in vins:
            vin_data = crud_report_rows.find_report_row_by_vin(vin['vin'])
            if not vin_data:
                print(f"No data found for VIN {vin['vin']}")
                continue

            aria_wi_id = (
                vin_data.get('flows', {})
                        .get(stage, {})
                        .get('docs', {})
                        .get('invoice', {})
                        .get('aria_data', {})
                        .get('aria_wi_id', None)
            )

            if not aria_wi_id:
                print(f"No ARIA work item ID found for VIN {vin['vin']}")
                continue

            aria_utils.construct_expection_message(aria_wi_id=aria_wi_id, exception_message=message)
            aria_utils.send_bre_request()

    return {
        'statusCode': 200,
        # Return only vins of each list
        'body': json.dumps({
            'failed_vins': [vin['vin'] for vin in failed_vins],
            'vins_to_report': [vin['vin'] for vin in vins_to_report],
            'downloaded_vins': [vin['vin'] for vin in stored_vins]
        }, default=str)
    }