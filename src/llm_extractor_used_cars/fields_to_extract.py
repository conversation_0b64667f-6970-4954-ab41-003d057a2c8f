fields = {
    "used_cars_invoices":{
        "VIN": "<17-character Vehicle Identification Number (VIN). Bear in mind that VIN numbers don’t have 'O', 'I', and 'Q' letters to avoid confusing them with '0', '1', and '9'. Shown as 'SERIAL NO' in the document.>",
        #"Company Name": "<The name of the invoicing-sales location company, appearing next to or above the address under 'INVOICING-SALES LOCATION'. Dont confuse with BUYER values that could be similar.>",
        #"Address": "<Street address of the invoicing-sales location, without city or state.>",
        #"City": "<City, state, and zip of the invoicing-sales location.>",
        "Transaction location": "<Is the location where the transaction has beend done. Is at the top of the document after the keywords 'TRANSACTION LOCATION'>",
        "Invoice Date": "<The invoice date, found near the top of the invoice following the words 'INVOICE DATE'.  I need it in american format that is MM/DD/YYYY (Ex: 25-MAR-2025 --> 03/25/2025))>",
        "Adjustments": "<Dollar value of adjustments, found as the value in the summary/bottom next to 'ADJUSTMENTS' above 'TOTAL BEFORE PAYMENTS'>",
        "Purchase price": "<Main purchase price of the car, found as the value in the summary/bottom next to 'TOTAL BEFORE PAYMENTS'.>",
    }
}

fields_json = {
    "used_cars_invoices":{
        # "Company Name": "MANHEIM [Id: 'unique_id_1'] ATLANTA [Id: 'unique_id_2']",
        # "Address": "4900 [Id: 'unique_id_3'] BUFFINGTON [Id: 'unique_id_4'] RD [Id: 'unique_id_5']",
        # "City": "COLLEGE [Id: 'unique_id_6'] PARK [Id: 'unique_id_7'] GA [Id: 'unique_id_8'] 30349 [Id: 'unique_id_9'] US [Id: 'unique_id_10']",
        "Transaction location": "123 [Id: 'unique_id_1'] antonio [Id: 'unique_id_2'] street [Id: 'unique_id_3']",
        "Invoice Date": "03/25/2025 [Id: 'unique_id_4']",
        "Adjustments": "$12345.12 [Id: 'unique_id_5']",
        "Purchase price": "$1231234.00 [Id: 'unique_id_6']",
        "VIN": "JTHGZ1B27S5082852[Id: 'unique_id_7']",

    }
}

fields_json_v2 = {
    "used_cars_invoices":{
        # "Company Name": "MANHEIM ATLANTA",
        # "Address": "4900 BUFFINGTON RD",
        # "City": "COLLEGE PARK GA 30349 US",
        "Transaction location": "123 antonio street",
        "Invoice Date": "03/25/2025",
        "Adjustments": "$12345.12",
        "Purchase price": "$1231234.00",
        "VIN": "JTHGZ1B27S5082852",
    }
}

fields_with_threshold = {
    "used_cars_invoices":{
        # "Company Name": {
        #     "threshold_up": None,
        #     "threshold_down": None,
        #     "threshold_left": 0.4,
        #     "threshold_right": 0.8,    
        # },
        # "Address": {
        #     "threshold_up": None,
        #     "threshold_down": None,
        #     "threshold_left": 0.4,
        #     "threshold_right": 0.8,    
        # },
        # "City": {
        #     "threshold_up": None,
        #     "threshold_down": None,
        #     "threshold_left": 0.4,
        #     "threshold_right": 0.8,    
        # }
        "Transaction location": {
            "threshold_up": None,
            "threshold_down": None,
            "threshold_left": 0.7,
            "threshold_right": None,    
        }
    }
}

fields_type = {
    "used_cars_invoices":{
        "VIN": {"type": "regular"},
        # "Company Name": {"type": "regular"},
        # "Address": {"type": "regular"},
        # "City": {"type": "regular"},
        "Transaction location": {"type": "regular"},
        "Invoice Date": {"type": "regular"},
        "Adjustments": {"type": "regular"},
        "Purchase price": {"type": "regular"}
    }
}