# This module contains a class for interacting with the Folios collection in the MongoDB database.

import os
from datetime import datetime
from mongo_utils import Mongo
from boto3_utils import get_secret


class CrudVins:
    def __init__(self, mongo):
        self.mongo = mongo
        self.collection_name='vin'


    def get_one(self, query):
        self.mongo.select_db_and_collection(db_name=os.environ['MONGO_DATABASE'], collection_name=self.collection_name)
        return self.mongo.find_one(query)
    
    def update_vin(self, query, data_to_set, data_to_push):
        data_to_set['updated_at'] = datetime.now()
        data = {"$set": data_to_set, "$push": data_to_push} 
        print(query)
        self.mongo.select_db_and_collection(db_name=os.environ['MONGO_DATABASE'], collection_name=self.collection_name)
        res = self.mongo.update_one(query, data)
        print(res)

    def __del__(self):
        self.mongo.close_connection()