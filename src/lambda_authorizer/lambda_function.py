import json
import re
import boto3
import os

from botocore.exceptions import ClientError

common_prefix = os.environ['COMMON_PREFIX']


def get_secret(secret_name):
    region_name = os.environ.get('AWS_REGION')
    if not region_name:
        raise Exception('AWS region must be set')

    # Create a Secrets Manager client
    session = boto3.session.Session()
    client = session.client(
        service_name='secretsmanager',
        region_name=region_name
    )

    try:
        get_secret_value_response = client.get_secret_value(
            SecretId=secret_name
        )
    except ClientError as e:
        raise e

    secret = get_secret_value_response['SecretString']

    return secret


def lambda_handler(event, context):
    print(event)
    # Extract token from petition
    token = event['headers']['authorization']
    token = re.sub("Bearer ", "", token)
    # Extract list of valid tokens
    valid_tokens = get_secret(f'{common_prefix}-api_gw_valid_tokens')
    valid_tokens = json.loads(valid_tokens)

    # Check if token is valid
    response = {
        "isAuthorized": True if token in valid_tokens['valid_tokens'] else False
    }

    return response