import json
import base64
from boto3_utilities import Boto3Utilities

def lambda_handler(event, context):
    """
   ################# DETECT TEXT #################
   detect text from a file (encoded in base64)
    {
      "file_b64":"file_in_base64",
      "data": { "get_lines": true }
    } 
    """    
    try:
        # Extracting event data
        event = json.loads(event['body'])
        file_b64 = event.get('file_b64','')
        filter = event.get('filter', {})
        get_lines = filter.get("get_lines", False)

        if not file_b64:
            raise Exception(f'No file bytes found')
        
        # Convert from b64 to bytes
        file_bytes = base64.b64decode(file_b64)

        # Perform action
        result = Boto3Utilities().detect_document_text(file_bytes, get_lines)
        
        output = result
        
        return {
            'statusCode': 200,
            'body': json.dumps(output) if isinstance(output, dict) else output
        }
    except Exception as e:
        print(f'Error: {e}')
        return {
            'statusCode': 500,
            'body': json.dumps('NOK!')
        }
