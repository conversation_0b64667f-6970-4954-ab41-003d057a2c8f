import json
import boto3

class Boto3Utilities:
    def __init__(self):
        self.client = boto3.client('textract')

    def parse_words(self, blocks):
        output_str = ''
        for block in blocks:
            if block['BlockType'] == 'WORD':
                output_str += block['Text'] + ' '
        return output_str.strip()

    def parse_lines(self, blocks):
        lines = []
        for block in blocks:
            if block['BlockType'] == 'LINE':
                lines.append(block['Text'])
        return "\n".join(lines)

    def detect_document_text(self, file_bytes, get_lines=False):
        try:
            response = self.client.detect_document_text(
                Document={
                    'Bytes': file_bytes,
                }
            )
            print(response)
            if response.get('DocumentMetadata'):
                if response['ResponseMetadata'].get('HTTPStatusCode') == 200:
                    if get_lines:
                        return self.parse_lines(response['Blocks'])
                    else:
                        return self.parse_words(response['Blocks'])
                else:
                    return '' if not get_lines else []
            else:
                return '' if not get_lines else []
        except Exception as e:
            print(f'Error: {e}')
            return '' if not get_lines else []