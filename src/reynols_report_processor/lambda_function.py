
from boto3_utils import get_latest_csv_metadata, download_csv_from_s3, download_file_from_s3, get_secret
import os
import pandas as pd
from crud_report_rows import CrudReynolsReport
from crud_invoices import CrudInvoices
from sftp_utils import get_report_file
import json
from vininfo import Vin
import traceback
from aria_utils import AriaUtils
from mongo_utils import Mongo


def mark_work_item_in_aria_as_deleted(app_id, work_item_id):
    mongo_client = Mongo(get_secret(os.environ['ENV'] + '-mongodb_uri', return_json=False))
    mongo_client.select_db_and_collection(os.environ['MONGO_DATABASE'], collection_name="aria_status")
    available_status = mongo_client.find_one({"app_id": app_id})
    completed_status_uuid = next(status_id for status_id in available_status["status"].keys() if available_status["status"][status_id]["label"] == "Delete")
    aria_client = AriaUtils()
    json_change_status = {
        "aria_status": {
            "value": completed_status_uuid
        }
    }

    aria_client.construct_reply_bre_request(app_id,work_item_id,json_change_status)
    return aria_client.send_bre_request()



def mark_missing_pending_invoice_vins_as_delete(report_vins, stage, vin_collection, pending_invoice_condition):
    vins = list(vin_collection.find({
        "vin": {"$nin": report_vins}, **pending_invoice_condition
    }))

    print(f"******** VINs That will be marked as delete for {stage} *******")
    vins_numbers =[]
    for vin in vins:
        try:
            app_id = vin["flows"][stage]["docs"]["invoice"]["aria_data"]["aria_app_id"]
            work_item_id = vin["flows"][stage]["docs"]["invoice"]["aria_data"]["aria_wi_id"]
            print("CALLING===")
            mark_work_item_in_aria_as_deleted(app_id, work_item_id)
            vins_numbers.append(vin["vin"])
        except Exception as e:
            print("EXCEPTION", e)
            traceback.print_exc()
            pass
    print(vins_numbers)
    vin_collection.update_many(
        {"vin": {"$nin": report_vins}, **pending_invoice_condition},
        {
            "$set": {
                f"flows.{stage}.status": 6,
                f"flows.{stage}.docs.invoice.aria_data.status": "Delete"
            },
            "$push": {
                f"flows.{stage}.status_history": 6,
                f"flows.{stage}.docs.invoice.aria_data.status_history": "Delete"
            }
        }
    )


def map_store_val(val):
    if "LA" in val:
        return "LOA"
    elif "LG" in val:
        return "LOG"
    elif "PO" in val:
        return "POR"
    elif "NW" in val:
        return "PNW"
    elif "HO" in val:
        return "HON"
    elif val in ['L4', 'FT', 'FO']:
        return "FOR"
    elif "J1" in val:
        return "JLRB"
    elif "L1" in val:
        return "JLRB"
    elif "L2" in val:
        return "JLRN"
    elif "L3" in val:
        return "JLRG"
    elif val in ['CD', 'TK']:
        return "CAD"
    elif val in ['MA', 'GC']:
        return "MBG"

def map_store_val_used_cars(store, acctg):
    acctg = str(acctg)
    if "LOA" in store:
        if "1" in acctg:
            return "LOA"
        elif "2" in acctg:
            return "LOG"
    elif "POR" in store:
        if "1" in acctg:
            return "POR"
        elif "2" in acctg:
            return "PNW"
    if "JLR" in store:
        if "1" in acctg:
            return "JLRB"
        elif "2" in acctg:
            return "JLRN"
        elif "3" in acctg:
            return "JLRG"

    return store

def get_missing_cars_invoices(aria_data_list, pending_invoice_vins, vin):
    for aria_data in aria_data_list:
        aria_data_status = (
            aria_data
            .get("status")
        )
        work_item_id = (
            aria_data
            .get("aria_wi_id")
        )
        app_id = (
            aria_data
            .get("aria_app_id")
        )
        if not aria_data_status:
            pending_invoice_vins.append({"app_id": app_id, "work_item_id": work_item_id, "vin": vin})



def process_post_inventory_report(file_path, latest_file_path):

    crud_reynols_report = CrudReynolsReport()
    reynols_report_df = pd.read_csv(latest_file_path)
    vins_report = []

    for idx, row in reynols_report_df.iterrows():
        row['STORE'] = map_store_val(row['ACCTG-MK'])
        row['SVC RO DATE'] = row['RO-CLOSE-DATE'] if str(row["RO-CLOSE-DATE"]).replace(" ", "") != "" and str(row["RO-CLOSE-DATE"]).lower() != "nan" else row['SVC RO DATE']
        existing_row = crud_reynols_report.find_report_row_by_vin(row['VIN'])
        # Avoid inserting those VINs starting with AM cause are sold vehicles
        if existing_row is None and row['VIN'][:2] != "AM":
            print(" Inserting in mongo VIN: ", row['VIN'])
            crud_reynols_report.insert_row(row, file_path)
        else:
            print(" Updating in mongo VIN: ", row['VIN'])
            crud_reynols_report.update_row_post_inventory(row, file_path, row['VIN'], existing_row)

        vins_report.append(row['VIN'])

    mark_missing_pending_invoice_vins_as_delete(
        vins_report,
        "post-inventory",
        crud_reynols_report.mongo,
        {
            "flows.post-inventory.docs.invoice.aria_data.aria_wi_id":{"$exists":True},
            "flows.post-inventory.docs.invoice.aria_data.status":{"$exists":False},
        }
    )


def process_used_cars_report(file_path, latest_file_path):

    crud_reynols_report = CrudReynolsReport()
    crud_invoices = CrudInvoices()
    reynols_report_df = pd.read_csv(latest_file_path)
    not_loaded_vins = []
    vins_report = []


    for idx, row in reynols_report_df.iterrows():
        store = row["STORE"]
        acctg = row["ACCTG-MK"]
        row['STORE'] = map_store_val_used_cars(store, acctg)

        existing_row = crud_reynols_report.find_report_row_by_vin(row['VIN'])

        vin_val = str(row['VIN'])
        if (not vin_val or vin_val == '' or len(vin_val) != 17) or Vin(vin_val).verify_checksum() == False:
            not_loaded_vins.append(vin_val)
            continue

        if existing_row is None:
            print(" Inserting in mongo VIN: ", vin_val)
            invoice_created_data = crud_invoices.find_invoice_used_car_by_vin_and_not_discarded(vin_val)
            if invoice_created_data is not None:
                crud_reynols_report.insert_row_used_car_with_metadata(row, file_path, invoice_created_data)
            else:
                crud_reynols_report.insert_row_used_car(row, file_path)
        else:
            print(" Updating in mongo VIN: ", vin_val)
            crud_reynols_report.update_row_used_car(row, file_path, vin_val, existing_row)

        vins_report.append(row['VIN'])

    mark_missing_pending_invoice_vins_as_delete(
        vins_report,
        "used-cars",
        crud_reynols_report.mongo,
        {
            "flows.used-cars.docs.invoice.aria_data.aria_wi_id":{"$exists":True},
            "flows.used-cars.docs.invoice.aria_data.status":{"$exists":False},
        }
    )

def lambda_handler(event, context):

    try:

        stage = event.get("stage", None)
        if stage is None or stage == "":
            return {
                'statusCode': 500,
                'body': {
                    "message": json.dumps(f"Error no stage provide!")
                }
            }

        print(" ****** DOWNLOADING REPORT ****** ")

        path_save_report = ""
        sftp_files_path = ""
        report_name_start_with = ""
        if stage == 'post-inventory':
            path_save_report = os.environ['REYNOLS_REPORT_FOLDER']
            sftp_files_path = os.environ['SFTP_FILES_PATH']
            report_name_start_with = "NC_"
        elif stage == 'pre-inventory':
            path_save_report = os.environ['PRE_INVENTORY_REPORT_FOLDER']
            sftp_files_path = os.environ['SFTP_FILES_PATH_PRE_INVENTORY']
            report_name_start_with = ""
        elif stage == 'used-cars':
            path_save_report = os.environ['USED_CARS_REPORT_FOLDER']
            sftp_files_path = os.environ['SFTP_FILES_PATH_USED_CARS']
            report_name_start_with = "UC_"

        filename = get_report_file(bucket_name=os.environ['BUCKET'], folder_name=path_save_report, remote_folder_path=sftp_files_path, report_name_start_with=report_name_start_with)

        # filename = "report_used_cars.csv"
        # path_save_report = "/tmp"

        if filename is None:
            print("No new report found")
            return {
                "statusCode": 200,
                "body": json.dumps({"message": "No new report found"})
            }

        file_path = f"{path_save_report}/{filename}"
        latest_file_path = f"/tmp/{filename}"

        # download_file_from_s3("s3://snd-hen-bucket/used_cars_reports/Book1.csv", latest_file_path)


        print(f" ****** DOWNLOADED REPORT: {filename} ****** ")

        if stage == 'post-inventory':
            process_post_inventory_report(file_path=file_path, latest_file_path=latest_file_path)
        elif stage == 'pre-inventory':
            pass
        elif stage == 'used-cars':
            process_used_cars_report(file_path=file_path, latest_file_path=latest_file_path)

    except Exception:
        print("Report couldnt be processed correctly")
        print(traceback.format_exc())

        raise Exception(f"Error processing report: {traceback.format_exc()}")

    return {
        "statusCode": 200,
        "body": json.dumps({"message": "Report processed correctly"})
    }







