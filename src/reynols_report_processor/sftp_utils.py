import os
import paramiko
from stat import S_ISREG
from boto3_utils import get_secret, post_to_s3, get_latest_csv_metadata

def connect_sftp(hostname, username, password, port):
    try:
        client = paramiko.SSHClient()
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())  # Automatically add host key
        client.connect(hostname, port=port, username=username, password=password)
        sftp = client.open_sftp()  # Open SFTP session
        return sftp, client
    except paramiko.AuthenticationException:
        print("Auth failed.")
        raise
    except paramiko.SSHException as e:
        print(f"SSH error: {e}")
        raise
    except Exception as e:
        print(f"Error: {e}")
        raise

def download_file_from_sftp(sftp, client, remote_folder, local_folder, bucket_name, folder_name, report_name_start_with):
    file_extension = os.environ['FILE_SFTP_EXTENSION']

    try:
        sftp.chdir(remote_folder)

        files = [
            (f, sftp.stat(f).st_mtime)
            for f in sftp.listdir()
            if f.endswith(file_extension) and f.startswith(report_name_start_with)
        ]

        if files:
            latest_file = max(files, key=lambda x: x[1])[0]  # Get the latest file
            return download_and_upload_file(sftp, latest_file, local_folder, bucket_name, folder_name)
        else:
            print(f"No files with extension {file_extension} found in the remote folder.")
            return None
    except Exception as e:
        print(f"Download error: {str(e)}")
    finally:
        if sftp:
            sftp.close()
        if client:
            client.close()

def download_specific_file_from_sftp(sftp, client, remote_folder, local_folder, bucket_name, folder_name, specific_file):
    try:
        sftp.chdir(remote_folder)
        files = sftp.listdir()

        if specific_file in files:
            return download_and_upload_file(sftp, specific_file, local_folder, bucket_name, folder_name)
        else:
            print(f"Specified file '{specific_file}' not found.")
            return None
    except Exception as e:
        print(f"Download error: {str(e)}")
    finally:
        if sftp:
            sftp.close()
        if client:
            client.close()

def download_and_upload_file(sftp, file_name, local_folder, bucket_name, folder_name):
    local_path = os.path.join(local_folder, file_name)
    os.makedirs(local_folder, exist_ok=True)

    print(f"Downloading file: {file_name}")
    sftp.get(file_name, local_path)

    latest_file_metadata = get_latest_csv_metadata(bucket_name, folder_name)
    if latest_file_metadata and file_name in latest_file_metadata['Key']:
        return None

    post_to_s3(bucket_name, folder_name, file_name, local_path)
    return file_name

def get_report_file(bucket_name, folder_name, specific_file=None, report_name_start_with = "", remote_folder_path = ""):
    secret = get_secret(os.environ['SFTP_CREDENTIALS'])
    hostname = secret["hostname"]
    username = secret["username"]
    password = secret["password"]
    port = secret["port"]

    local_download_path = '/tmp/'
    sftp, client = connect_sftp(hostname, username, password, port)

    if specific_file:
        return download_specific_file_from_sftp(sftp, client, remote_folder_path, local_download_path, bucket_name, folder_name, specific_file)
    else:
        return download_file_from_sftp(sftp, client, remote_folder_path, local_download_path, bucket_name, folder_name, report_name_start_with)
