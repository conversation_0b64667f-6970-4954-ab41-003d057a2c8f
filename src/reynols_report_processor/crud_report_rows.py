import os
import datetime
from functools import reduce

from mongo_utils import Mongo
from boto3_utils import get_secret

def deep_get(d, keys, default=None):
    """
    This will help to get the nested value
    It accept the dict, array of keys to look into and default value
    """
    return reduce(lambda c, k: c.get(k, {}) if isinstance(c, dict) else {}, keys, d) or default

class CrudReynolsReport:
    def __init__(self):
        mongo_uri = get_secret(os.environ['ENV'] + '-mongodb_uri', return_json=False)
        self.mongo = Mongo(mongo_uri)
        self.mongo.select_db_and_collection(
            db_name=os.environ['MONGO_DATABASE'],
            collection_name='vin'
        )
    
    def insert_row(self, reynols_row, report_name):
        """
        This function inserts a reynols report row into the database.
        STORE	RECEIVED	STOCK #	MAKE	DESC	VIN	INV AMT	SLS COST	STK IN NOTES	SVC RO DATE	STAT-CODE	JRNL-PURCH-DATE-DR
        """
        reynols_row_to_db = {
            "vin": reynols_row['VIN'],
            "flows": {
                "used-cars": {
                    "report-data": {},
                    "docs": {
                        "invoice": {
                            "downloaded_at": "",
                            "aria_data": {

                            },
                            "fields": {

                            }
                        }
                    }
                },
                "pre-inventory": {
                    "report-data": {},
                    "docs": {
                        "invoice": {
                            "downloaded_at": "",
                            "aria_data": {
                            
                            },
                            "fields": {

                            }
                        }
                    }
                },
                "post-inventory": {
                    "status": 0,
                    "status_history": [0],
                    "updated_at": datetime.datetime.now(),
                    "read_at": datetime.datetime.now(),
                    "report-data": {
                        "store": reynols_row['STORE'],
                        "stock": reynols_row['STOCK #'],
                        "make": reynols_row['MAKE'],
                        "received": reynols_row['RECEIVED'],
                        "desc": reynols_row['DESC'],
                        "inv_amt": reynols_row['INV AMT'],
                        "sls_cost": reynols_row['SLS COST'],
                        "stock_in_notes": reynols_row['STK IN NOTES'],
                        "svc_ro_date": reynols_row['SVC RO DATE'],
                        "stat_code": reynols_row['STAT-CODE'],
                        "jrnl_purch_date_dr": reynols_row['JRNL-PURCH-DATE-DR'],
                        "first_read_from_file": report_name
                    },
                    "docs": {
                        "invoice": {
                            "downloaded_at": "",
                            "aria_data": {

                            },
                            "fields": {

                            }
                        },
                        "bol": {},
                        "title": {}
                    }
                }
            }
        }

        self.mongo.insert_one(reynols_row_to_db)
        return reynols_row_to_db

    def update_row_post_inventory(self, reynols_row, report_name, vin, vin_data):
        """
        This function inserts a reynols report row into the database.
        STORE	RECEIVED	STOCK #	MAKE	DESC	VIN	INV AMT	SLS COST	STK IN NOTES	SVC RO DATE	STAT-CODE	JRNL-PURCH-DATE-DR
        """
        self.mongo.update_one(
            {"vin": vin, f"flows.post-inventory.read_at": {"$exists": False}},
            {"$set": {f"flows.post-inventory.read_at": datetime.datetime.now()}}
        )

        self.mongo.update_one(
            {"vin": vin, "flows.post-inventory.status": {"$exists": False}},
            {
                "$set": {f"flows.post-inventory.status": 0},
                "$push": {f"flows.post-inventory.status_history": 0}
            }
        )

        reynols_row_to_db = {
            "flows.post-inventory.updated_at": datetime.datetime.now(),
            "flows.post-inventory.report-data.store": reynols_row['STORE'],
            "flows.post-inventory.report-data.stock": reynols_row['STOCK #'],
            "flows.post-inventory.report-data.make": reynols_row['MAKE'],
            "flows.post-inventory.report-data.desc": reynols_row['DESC'],
            "flows.post-inventory.report-data.inv_amt": reynols_row['INV AMT'],
            "flows.post-inventory.report-data.sls_cost": reynols_row['SLS COST'],
            "flows.post-inventory.report-data.stock_in_notes": reynols_row['STK IN NOTES'],
            "flows.post-inventory.report-data.svc_ro_date": reynols_row['SVC RO DATE'],
            "flows.post-inventory.report-data.stat_code": reynols_row['STAT-CODE'],
            "flows.post-inventory.report-data.jrnl_purch_date_dr": reynols_row['JRNL-PURCH-DATE-DR'],
            "flows.post-inventory.report-data.last_read_from_file": report_name,
        }
        if deep_get(vin_data, ["flows", "post-inventory", "docs", "invoice", "aria_data", "status"]) == "Delete":
            print(f"MARK TO RETRY {vin}")
            reynols_row_to_db["flows.post-inventory.status"] = 13
            reynols_row_to_db["flows.post-inventory.docs.invoice.aria_data.status"] = "Reappeared"

        self.mongo.update_one({"vin": vin}, {"$set": reynols_row_to_db})
        return reynols_row_to_db

    def insert_row_used_car(self, reynols_row, report_name):
        """
        This function inserts a reynols report row into the database.
        STORE	RECEIVED	STOCK #	MAKE	DESC	VIN	INV AMT	SLS COST	STK IN NOTES	SVC RO DATE	STAT-CODE	JRNL-PURCH-DATE-DR
        """
        reynols_row_to_db = {
            "vin": reynols_row['VIN'],
            "flows": {
                "used-cars": {
                    "status": 0,
                    "status_history": [0],
                    "updated_at": datetime.datetime.now(),
                    "read_at": datetime.datetime.now(),
                    "report-data": {
                        "store": reynols_row['STORE'],
                        "received": reynols_row['RECEIVED'],
                        "stock": reynols_row['STOCK #'],
                        "make": reynols_row['MAKE'],
                        "inv_amt": reynols_row['INV-AMT'],
                        "sls_cost": reynols_row['SLS COST'],
                        "stock_in_notes": reynols_row['STK IN NOTES'],
                        "acctg_mk": reynols_row['ACCTG-MK'],
                        "memo": reynols_row['MEMO 2'],
                        "first_read_from_file": report_name
                    },
                    "docs": {
                        "invoice": {
                            "downloaded_at": "",
                            "aria_data": {

                            },
                            "fields": {

                            }
                        }
                    }
                },
                "pre-inventory": {
                    "report-data": {},
                    "docs": {
                        "invoice": {
                            "downloaded_at": "",
                            "aria_data": {

                            },
                            "fields": {

                            }
                        }
                    }
                },
                "post-inventory": {
                    "report-data": {},
                    "docs": {
                        "invoice": {
                            "downloaded_at": "",
                            "aria_data": {

                            },
                            "fields": {

                            }
                        },
                        "bol": {},
                        "title": {}
                    }
                }
            }
        }

        self.mongo.insert_one(reynols_row_to_db)
        return reynols_row_to_db

    def insert_row_used_car_with_metadata(self, reynols_row, report_name, invoice_created_data):

        reynols_row_to_db = {
            "vin": reynols_row['VIN'],
            "flows": {
                "used-cars": {
                    "status": 0,
                    "status_history": [0],
                    "updated_at": datetime.datetime.now(),
                    "read_at": datetime.datetime.now(),
                    "report-data": {
                        "store": reynols_row['STORE'],
                        "received": reynols_row['RECEIVED'],
                        "stock": reynols_row['STOCK #'],
                        "make": reynols_row['MAKE'],
                        "inv_amt": reynols_row['INV-AMT'],
                        "sls_cost": reynols_row['SLS COST'],
                        "stock_in_notes": reynols_row['STK IN NOTES'],
                        "acctg_mk": reynols_row['ACCTG-MK'],
                        "memo": reynols_row['MEMO 2'],
                        "first_read_from_file": report_name
                    },
                    "docs": {
                        "invoice": {
                            "downloaded_at": invoice_created_data["read_at"],
                            "aria_data": {
                                "aria_app_id": invoice_created_data["aria_app_id"],
                                "aria_wi_id": invoice_created_data["aria_wi_id"],
                                "created": invoice_created_data["read_at"],
                                "status": invoice_created_data["status"],
                                "status_history": invoice_created_data["status_history"]
                            },
                            "fields": invoice_created_data["fields"]
                        }
                    }
                },
                "pre-inventory": {
                    "report-data": {},
                    "docs": {
                        "invoice": {
                            "downloaded_at": "",
                            "aria_data": {

                            },
                            "fields": {

                            }
                        }
                    }
                },
                "post-inventory": {
                    "report-data": {},
                    "docs": {
                        "invoice": {
                            "downloaded_at": "",
                            "aria_data": {

                            },
                            "fields": {

                            }
                        },
                        "bol": {},
                        "title": {}
                    }
                }
            }
        }

        self.mongo.insert_one(reynols_row_to_db)
        return reynols_row_to_db

    def update_row_used_car(self, reynols_row, report_name, vin, vin_data):
        """
        This function inserts a reynols report row into the database.
        STORE	RECEIVED	STOCK #	MAKE	DESC	VIN	INV AMT	SLS COST	STK IN NOTES	SVC RO DATE	STAT-CODE	JRNL-PURCH-DATE-DR
        """

        self.mongo.update_one(
            {"vin": vin, f"flows.used-cars.read_at": {"$exists": False}},
            {"$set": {f"flows.used-cars.read_at": datetime.datetime.now()}}
        )

        self.mongo.update_one(
            {"vin": vin, "flows.used-cars.status": {"$exists": False}},
            {
                "$set": {f"flows.used-cars.status": 0},
                "$push": {f"flows.used-cars.status_history": 0}
            }
        )

        reynols_row_to_db = {
            "flows.used-cars.updated_at": datetime.datetime.now(),
            "flows.used-cars.report-data.store": reynols_row['STORE'],
            "flows.used-cars.report-data.received": reynols_row['RECEIVED'],
            "flows.used-cars.report-data.stock": reynols_row['STOCK #'],
            "flows.used-cars.report-data.make": reynols_row['MAKE'],
            "flows.used-cars.report-data.inv_amt": reynols_row['INV-AMT'],
            "flows.used-cars.report-data.sls_cost": reynols_row['SLS COST'],
            "flows.used-cars.report-data.stock_in_notes": reynols_row['STK IN NOTES'],
            "flows.used-cars.report-data.acctg_mk": reynols_row['ACCTG-MK'],
            "flows.used-cars.report-data.memo": reynols_row['MEMO 2'],
            "flows.used-cars.report-data.last_read_from_file": report_name
        }
        if deep_get(vin_data, ["flows", "used-cars", "docs", "invoice", "aria_data", "status"]) == "Delete":
            reynols_row_to_db["flows.used-cars.status"] = 13
            reynols_row_to_db["flows.used-cars.docs.invoice.aria_data.status"] = "Reappeared"

        self.mongo.update_one({"vin": vin}, {"$set": reynols_row_to_db})
        return reynols_row_to_db

    def find_report_row_by_vin(self, vin):
        """
        This function finds an email by its email ID.
        """
        query = {"vin": vin}
        return self.mongo.find_one(query)

    def __del__(self):
        self.mongo.close_connection()
