# This module contains a class for interacting with the titles collection in the MongoDB database.

import os
from datetime import datetime
from mongo_utils import Mongo
from boto3_utils import get_secret


class CrudTitles:
    def __init__(self):
        mongo_uri = get_secret(os.environ['ENV'] + '-mongodb_uri', return_json=False)
        self.mongo = Mongo(mongo_uri)
        self.mongo.select_db_and_collection(
            db_name=os.environ['MONGO_DATABASE'],
            collection_name='title'
        )

    def get_failed_titles(self):
        """
        This function inserts a folio into the database.
        """

        query = {
            "status": "FailedFromARIA"
        }

        return self.mongo.find(query).sort("read_at").limit(50)

    def remove_title(self, title_id):
        """
        This function removes a title_entry into mongo
        """

        query = {
            "title_id": title_id
        }

        self.mongo.delete_one(query)
    
    def update_read_time(self, title_id):
        """
        This function updates the last read at time
        """

        query = {
            'title_id': title_id
        }

        data = {
            '$set': {
                'read_at': datetime.now()
            }
        }

        self.mongo.update_one(query, data)
    
    def __del__(self):
        self.mongo.close_connection()

    
        