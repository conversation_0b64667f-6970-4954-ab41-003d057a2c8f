import os
from boto3_utils import trigger_lambda
from crud_titles import C<PERSON><PERSON>it<PERSON>
from aria_utils import AriaUtils
import traceback

def lambda_handler(event, context):
    try:
        crud_titles = CrudTitles()
        aria_utils = AriaUtils()
        function_name = os.environ['TITLE_TO_ARIA_LAMBDA']

        aria_response = aria_utils.get_app_status()

        if not aria_response:
            raise Exception("Failed to get ARIA status")
        else:
            print("✅ Aria is Active!")

        failed_titles = crud_titles.get_failed_titles()
        titles_to_processs = []

        for title in failed_titles:
            title_id = title['title_id']
            if 'images_of_title' not in title:
                crud_titles.update_read_time(title_id)
                continue
            from_file = title['from_file']
            is_mso = title.get('is_mso', False)
            title_page_to_process = title['images_of_title']
            
            #removing the title as a new entry will be created
            crud_titles.remove_title(title_id)

            payload = {
                "from_file": from_file,
                "is_mso": is_mso,
                "title_page_to_process": title_page_to_process,
                "retry": True
            }

            trigger_lambda(function_name, payload)
        
        return {
            "status": 200,
            "body": titles_to_processs
        }
    except Exception as e:
        print(f"An error occurred: {str(e)}")
        traceback.print_exc()

