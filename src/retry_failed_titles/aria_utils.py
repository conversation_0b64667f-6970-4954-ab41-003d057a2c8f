import os
import time
import requests

from boto3_utils import get_secret

class AriaUtils:
    def __init__(self):
        self.aria_env = os.environ['ARIA_ENV']
        self.credentials = self.get_credentials()
        self.app_id = os.environ['ARIA_APP_ID']
        
        # Construct headers
        token = self.credentials['token']
        self.headers = {
            'Content-Type': 'application/json',
            'Authorization': f'{token}'
        }

    def get_credentials(self):
        """
        This function retrieves the credentials from the AWS Secrets Manager.
        """

        env = os.environ['ENV']
        credentials = get_secret(f"{env}-aria_cm_tokens")
        return credentials.get(self.aria_env)
    
    def get_app_status(self):
        """
        This function gets app status by sending request to ARIA worktitems
        """

        # Construct the URL
        aria_url = self.credentials['url']
        app_id = self.app_id
        self.url = f"{aria_url}/public/v1/apps/{app_id}/case_management_middleware/work_items"

        response = requests.get(self.url, headers=self.headers)

        # Check status code
        if response.status_code != 200:
            try:
                raise Exception(f"Failed to send request to ARIA CM: {response.status_code} - {response.json()}")
            except requests.exceptions.JSONDecodeError:
                raise Exception(f"Failed to send request to ARIA CM: {response.status_code} - {response.text}")
        
        if response.status_code == 200:
            return response.json()