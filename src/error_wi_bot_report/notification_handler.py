import os
import logging
from datetime import datetime
from typing import Dict, List
from email_utils import EmailNotificationUtils

logger = logging.getLogger()

class NotificationHandler:
    def __init__(self, mongo_utils):
        self.mongo_utils = mongo_utils

    def send_email_alerts(self, alerts: List[Dict], reports: List[Dict] = None, status_changes: List[Dict] = None):
        """Send consolidated email report using email utils"""
        try:
            email_utils = EmailNotificationUtils()
            success = email_utils.send_consolidated_monitoring_report(alerts, reports, status_changes)
            
            if success:
                logger.info("Consolidated monitoring email sent successfully")
            else:
                logger.warning("Failed to send consolidated monitoring email")
            
            return success
                
        except Exception as e:
            logger.error(f"Error sending consolidated monitoring email: {str(e)}")
            return False

    def print_alerts(self, alerts: List[Dict], reports: List[Dict] = None, status_changes: List[Dict] = None):
        """Print alerts and reports to console"""
        
        if alerts or status_changes:
            print("=" * 80)
            print(f"WORK ITEM MONITORING ALERTS - {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')}")
            print("=" * 80)
            
            # Group alerts by app_id
            if alerts:
                alerts_by_app = {}
                for alert in alerts:
                    app_id = alert.get('app_id', 'Unknown')
                    if app_id not in alerts_by_app:
                        alerts_by_app[app_id] = []
                    alerts_by_app[app_id].append(alert)
                
                print("\nALERTS FOUND:")
                for app_id, app_alerts in alerts_by_app.items():
                    app_name = self.mongo_utils.get_app_name(app_id)
                    print(f"\nAPPLICATION: {app_name} ({app_id})")
                    print("-" * 80)
                    
                    # Separate alert types
                    error_alerts = [a for a in app_alerts if 'error' in a.get('message', '').lower() and 'moved from' in a.get('message', '')]
                    idle_with_bot = [a for a in app_alerts if a.get('bot_run_started') and 'idle' in a.get('message', '')]
                    idle_no_bot = [a for a in app_alerts if not a.get('bot_run_started') and 'idle' in a.get('message', '')]
                    bulk_alerts = [a for a in app_alerts if 'bulk movement' in a.get('message', '').lower()]
                    
                    if error_alerts:
                        print("\nPROCESSING FAILURES:")
                        for alert in error_alerts:
                            print(f"  • Work Item: {alert.get('work_item_id', 'N/A')}")
                            print(f"    VIN: {alert.get('vin', 'N/A')}, Make: {alert.get('make', 'N/A')}")
                            print(f"    File: {alert.get('from_file', 'N/A')}")
                            print(f"    Failed at: {alert.get('change_timestamp', 'N/A')}")
                            print(f"    ACTION: Check processing logs immediately!")
                    
                    if idle_with_bot:
                        print("\nBOT PROCESSING ISSUES:")
                        for alert in idle_with_bot:
                            idle_hours = alert.get('idle_hours_since_bot', 0)
                            severity = "CRITICAL" if idle_hours > 24 else "HIGH" if idle_hours > 12 else "MEDIUM"
                            print(f"  • Work Item: {alert.get('work_item_id', 'N/A')} [{severity}]")
                            print(f"    VIN: {alert.get('vin', 'N/A')}, Make: {alert.get('make', 'N/A')}")
                            print(f"    Bot started: {alert.get('bot_run_started', 'N/A')}")
                            print(f"    Idle for: {idle_hours:.1f} hours since bot start")
                            if idle_hours > 24:
                                print(f"    ACTION: IMMEDIATE bot restart required!")
                            else:
                                print(f"    ACTION: Check bot logs and status")
                    
                    if idle_no_bot:
                        print("\nINFRASTRUCTURE ISSUES:")
                        for alert in idle_no_bot:
                            idle_hours = alert.get('idle_hours', 0)
                            severity = "CRITICAL" if idle_hours > 24 else "HIGH" if idle_hours > 12 else "MEDIUM"
                            print(f"  • Work Item: {alert.get('work_item_id', 'N/A')} [{severity}]")
                            print(f"    VIN: {alert.get('vin', 'N/A')}, Make: {alert.get('make', 'N/A')}")
                            print(f"    Status since: {alert.get('status_since', 'N/A')}")
                            print(f"    Idle for: {idle_hours:.1f} hours total")
                            if idle_hours > 24:
                                print(f"    ACTION: IMMEDIATE infrastructure check required!")
                            else:
                                print(f"    ACTION: Check bot infrastructure and auto-start")
                    
                    if bulk_alerts:
                        print("\nBULK STATUS CHANGES:")
                        for alert in bulk_alerts:
                            count = alert.get('count', 0)
                            threshold = alert.get('threshold', 0)
                            affected_items = alert.get('affected_items', [])
                            print(f"  • {count} items moved from Ready for DMS to Needs Attention")
                            print(f"    Threshold: {threshold}, Date: {alert.get('date', 'N/A')}")
                            print(f"    Sample items: {', '.join(affected_items[:3])}{'...' if len(affected_items) > 3 else ''}")
                            print(f"    ACTION: Investigate processing quality issues")
        
        if reports:
            print("\n" + "=" * 80)
            print("DAILY STATUS REPORTS")
            print("=" * 80)
            
            total_processed = sum(r['total_processed'] for r in reports)
            total_successful = sum(r['successful_count'] for r in reports)
            overall_success_rate = (total_successful / total_processed * 100) if total_processed > 0 else 0
            
            print(f"OVERALL SUMMARY")
            print(f"Date: {reports[0]['date'] if reports else 'N/A'}")
            print(f"Applications: {len(reports)}")
            print(f"Total Items: {total_processed}")
            print(f"Successful: {total_successful}")
            print(f"Success Rate: {overall_success_rate:.2f}%")
            
            for report in reports:
                print(f"\n{report.get('app_name', report['app_id'])}")
                print(f"   Items: {report['total_processed']}, Success: {report['success_rate']:.1f}%")
                # Show detailed status breakdown
                print("   Status Breakdown:")
                if report.get('status_counts'):
                    for status, count in report['status_counts'].items():
                        print(f"     {status}: {count}")
                else:
                    print("     No status data available")
        
        # Summary
        if alerts or reports:
            print("\n" + "=" * 80)
            error_count = len([a for a in alerts if 'error' in a.get('message', '')])
            bot_issue_count = len([a for a in alerts if a.get('bot_run_started') and 'idle' in a.get('message', '')])
            infra_issue_count = len([a for a in alerts if not a.get('bot_run_started') and 'idle' in a.get('message', '')])
            bulk_count = len([a for a in alerts if 'bulk' in a.get('message', '')])
            
            print(f"ALERT SUMMARY:")
            print(f"   Processing Failures: {error_count}")
            print(f"   Bot Issues: {bot_issue_count}")
            print(f"   Infrastructure Issues: {infra_issue_count}")
            print(f"   Bulk Movements: {bulk_count}")
            print("=" * 80)