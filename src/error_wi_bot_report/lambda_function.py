import json
import logging
from work_item_monitor import WorkItemMonitor
from notification_handler import NotificationHandler

# Configure logging
logger = logging.getLogger()
logger.setLevel(logging.INFO)

def lambda_handler(event, context):
    """Lambda entry point with enhanced event handling"""
    monitor = WorkItemMonitor()
    
    try:
        # Connect to database
        monitor.connect_to_db()
        
        # Parse event parameters with defaults
        single_app_id = event.get('app_id')
        report_only = event.get('report_only', False)
        print_all = event.get('print_all', False)  # Default False
        send_email = event.get('send_email', True)  # Default True
        
        # Initialize notification handler
        notification_handler = NotificationHandler(monitor.mongo_utils)
        
        if single_app_id:
            # Monitor specific app (for testing or specific requests)
            logger.info(f"Monitoring single app: {single_app_id}")
            alerts, report, status_changes = monitor.monitor_app(single_app_id)
            
            # Add app_id to alerts
            for alert in alerts:
                alert['app_id'] = single_app_id
            
            reports = [report]
            all_status_changes = status_changes
        else:
            # Monitor all apps in the database
            logger.info("Monitoring all applications in database")
            alerts, reports, all_status_changes = monitor.monitor_all_apps()
        
        # Handle notifications based on event configuration
        if print_all:
            # Print everything regardless of alerts/reports
            notification_handler.print_alerts(alerts, reports, all_status_changes)
        else:
            # Only print if there are alerts or reports (default behavior)
            if alerts or reports:
                notification_handler.print_alerts(alerts, reports, all_status_changes)
        
        # Send consolidated email if enabled
        # CHANGE: Always send ONE email with all data (alerts, reports, status changes)
        email_sent = False
        if send_email:
            logger.info("Sending consolidated monitoring email...")
            # Send one comprehensive email with all data regardless of alert count
            email_sent = notification_handler.send_email_alerts(alerts, reports, all_status_changes)
        elif not send_email:
            logger.info("Email alerts disabled via event configuration")
        
        # Prepare response
        total_apps = len(reports)
        critical_alerts = len([a for a in alerts if a.get('type') == 'CRITICAL'])
        warning_alerts = len([a for a in alerts if a.get('type') == 'WARNING'])
        
        # Count status changes by type
        change_summary = {}
        for change in all_status_changes:
            change_key = f"{change['previous_status_key']} → {change['current_status_key']}"
            change_summary[change_key] = change_summary.get(change_key, 0) + 1
        
        response_data = {
            'message': 'Monitoring completed successfully',
            'execution_config': {
                'single_app_id': single_app_id,
                'report_only': report_only,
                'print_all': print_all,
                'send_email': send_email,
                'email_sent': email_sent
            },
            'total_applications': total_apps,
            'status_changes_count': len(all_status_changes),
            'status_changes_summary': change_summary,
            'alerts_count': len(alerts),
            'critical_alerts': critical_alerts,
            'warning_alerts': warning_alerts,
            'applications_monitored': [r['app_id'] for r in reports],
            'summary': {
                'total_work_items': sum(r['total_processed'] for r in reports),
                'total_successful': sum(r['successful_count'] for r in reports),
                'overall_success_rate': (sum(r['successful_count'] for r in reports) / 
                                       sum(r['total_processed'] for r in reports) * 100) 
                                       if sum(r['total_processed'] for r in reports) > 0 else 0
            }
        }
        
        response = {
            'statusCode': 200,
            'body': json.dumps(response_data, default=str)
        }
        
        logger.info(f"Monitoring completed. {len(all_status_changes)} status changes, {len(alerts)} alerts across {total_apps} applications.")
        return response
        
    except Exception as e:
        logger.error(f"Error in lambda_handler: {str(e)}")
        return {
            'statusCode': 500,
            'body': json.dumps({
                'error': str(e),
                'message': 'Failed to complete monitoring'
            })
        }
    
    finally:
        monitor.close_connection()

# # For local testing
# if __name__ == "__main__":
#     # Test scenarios
    
#     # 1. Monitor all applications with default settings (print_all=False, send_email=True)
#     print("=== Testing: Monitor All Applications (Default) ===")
#     test_event_all = {}
#     result = lambda_handler(test_event_all, None)
#     print(json.dumps(result, indent=2, default=str))
    
#     # 2. Monitor all applications with print_all=True, send_email=False
#     print("\n=== Testing: Monitor All Applications (Print All, No Email) ===")
#     test_event_print_all = {
#         'print_all': True,
#         'send_email': False
#     }
#     result = lambda_handler(test_event_print_all, None)
#     print(json.dumps(result, indent=2, default=str))
    
#     # 3. Monitor specific application
#     print("\n=== Testing: Monitor Specific Application ===")
#     test_event_single = {
#         'app_id': 'b137ea79-2397-456a-b213-2e93c3b67ac9',
#         'print_all': True
#     }
#     result = lambda_handler(test_event_single, None)
#     print(json.dumps(result, indent=2, default=str))
    
#     # 4. Daily report only
#     print("\n=== Testing: Daily Report Only ===")
#     test_event_report = {
#         'report_only': True,
#         'print_all': True
#     }
#     result = lambda_handler(test_event_report, None)
#     print(json.dumps(result, indent=2, default=str))