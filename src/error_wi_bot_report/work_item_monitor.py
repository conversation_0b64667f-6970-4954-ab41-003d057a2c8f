import json
import boto3
from datetime import datetime, timed<PERSON><PERSON>
import os
from typing import Dict, List, Tuple
import logging
from email_utils import EmailNotificationUtils
from mongo_utils import MongoClientUtils

# Configure logging
logger = logging.getLogger()
logger.setLevel(logging.INFO)

class WorkItemMonitor:
    def __init__(self):
        # Use your MongoClientUtils instead of direct connection
        self.mongo_utils = MongoClientUtils()
        
        # Thresholds
        self.BULK_MOVE_THRESHOLD = int(os.environ.get('BULK_MOVE_THRESHOLD', '10'))
        self.IDLE_THRESHOLD_HOURS = int(os.environ.get('IDLE_THRESHOLD_HOURS', '1'))
        self.NO_BOT_RUN_IDLE_THRESHOLD_HOURS = int(os.environ.get('NO_BOT_RUN_IDLE_THRESHOLD_HOURS', '24'))

    def connect_to_db(self):
        """Initialize database connection and indexes"""
        try:
            # Create indexes using mongo utils
            self.mongo_utils.create_tracking_indexes()
            logger.info("Successfully connected to MongoDB via MongoClientUtils")
        except Exception as e:
            logger.error(f"Failed to connect to MongoDB: {str(e)}")
            raise
    
    def get_status_key_by_id(self, status_config: Dict, status_id: str) -> str:
        """Get status key from status ID"""
        if not status_config or 'status' not in status_config:
            return "unknown"
        
        status_info = status_config['status'].get(status_id, {})
        return status_info.get('key', 'unknown')
    
    def get_status_id_by_key(self, status_config: Dict, status_key: str) -> str:
        """Get status ID from status key"""
        if not status_config or 'status' not in status_config:
            return None
        
        for status_id, status_info in status_config['status'].items():
            if status_info.get('key') == status_key:
                return status_id
        return None
    
    # def get_bot_run_start_time(self, work_item_id: str) -> datetime:
    #     """Get the last bot run start time for a work item"""
    #     return self.mongo_utils.get_bot_run_start_time(work_item_id)

    def sync_current_work_items(self, app_id: str) -> List[Dict]:
        """Sync current work items with tracking collection and detect changes"""
        status_changes = []
        
        try:
            status_config = self.mongo_utils.get_status_config(app_id)
            if not status_config:
                logger.warning(f"No status config found for app {app_id}")
                return status_changes
            
            # Get all current work items using mongo utils
            current_items = self.mongo_utils.get_current_work_items(app_id)
            
            for item in current_items:
                work_item_id = item.get("id")
                current_status_id = item.get("aria_status")
                current_timestamp = item.get("aria_last_update", datetime.utcnow())
                
                # Extract extra data
                extra_data = {
                    "vin": item.get('groups', {}).get('title', {}).get('fields', {}).get('vin', {}).get('value'),
                    "make": item.get('groups', {}).get('title', {}).get('fields', {}).get('make', {}).get('value'),
                    "from_file": item.get('groups', {}).get('title', {}).get('fields', {}).get('from_file', {}).get('value')
                }
                
                # Get the latest tracking record using mongo utils
                latest_record = self.mongo_utils.get_latest_tracking_record(work_item_id, app_id)
                logger.info(f"Latest tracking record for {work_item_id}: {latest_record}")
                
                status_change = {
                    "changed": False,
                    "previous_status": None,
                    "current_status": current_status_id,
                    "previous_timestamp": None,
                    "duration_in_previous_status": None
                }
                
                # Check if status changed
                if latest_record:
                    logger.info(f"Latest record found for {work_item_id}")
                    previous_status = latest_record.get("status_id")
                    previous_timestamp = latest_record.get("timestamp")
                    logger.info(f"Previous status for {work_item_id}: {previous_status} -> {current_status_id}")
                    
                    if previous_status != current_status_id:
                        logger.info(f"Status change detected for {work_item_id}")
                        # Status has changed
                        status_change["changed"] = True
                        status_change["previous_status"] = previous_status
                        status_change["previous_timestamp"] = previous_timestamp
                        status_change["duration_in_previous_status"] = current_timestamp - previous_timestamp
                        
                        # Insert new tracking record for status change
                        tracking_record = {
                            "work_item_id": work_item_id,
                            "app_id": app_id,
                            "status_id": current_status_id,
                            "previous_status_id": previous_status,
                            "timestamp": current_timestamp,
                            "status_change": True,
                            "duration_in_previous_status_seconds": (current_timestamp - previous_timestamp).total_seconds(),
                            "extra_data": extra_data,
                            "change_type": "detected_on_sync"
                        }
                        
                        self.mongo_utils.insert_tracking_record(tracking_record)
                        logger.info(f"Status change detected during sync: {work_item_id} from {previous_status} to {current_status_id}")
                        
                        # Add to status changes list
                        previous_status_key = self.get_status_key_by_id(status_config, previous_status)
                        current_status_key = self.get_status_key_by_id(status_config, current_status_id)
                        
                        status_changes.append({
                            "work_item_id": work_item_id,
                            "app_id": app_id,
                            "previous_status_id": previous_status,
                            "current_status_id": current_status_id,
                            "previous_status_key": previous_status_key,
                            "current_status_key": current_status_key,
                            "change_timestamp": current_timestamp,
                            "duration_in_previous_status": status_change["duration_in_previous_status"],
                            "extra_data": extra_data,
                            "change_type": "detected_on_sync"
                        })
                    else:
                        # No status change, but update the last_seen for idle tracking
                        self.mongo_utils.update_tracking_record(
                            latest_record["_id"],
                            {"last_seen": current_timestamp}
                        )
                else:
                    # First time seeing this work item
                    tracking_record = {
                        "work_item_id": work_item_id,
                        "app_id": app_id,
                        "status_id": current_status_id,
                        "previous_status_id": None,
                        "timestamp": current_timestamp,
                        "last_seen": current_timestamp,
                        "status_change": False,
                        "duration_in_previous_status_seconds": 0,
                        "extra_data": extra_data,
                        "change_type": "initial_discovery"
                    }
                    
                    self.mongo_utils.insert_tracking_record(tracking_record)
                    logger.debug(f"New work item tracked: {work_item_id} with status {current_status_id}")
            
            logger.info(f"Synced work items for app {app_id}. Found {len(status_changes)} status changes.")
            return status_changes
            
        except Exception as e:
            logger.error(f"Error syncing work items for app {app_id}: {str(e)}")
            return status_changes

    # def get_app_name(self, app_id: str) -> str:
    #     """Get application name from App collection"""
    #     return self.mongo_utils.get_app_name(app_id)

    def generate_daily_report(self, app_id: str) -> Dict:
        """Generate end-of-day status report"""
        report = {
            "date": datetime.utcnow().strftime("%Y-%m-%d"),
            "app_id": app_id,
            "app_name": self.mongo_utils.get_app_name(app_id),
            "status_counts": {},
            "total_processed": 0,
            "successful_count": 0,
            "status_changes_today": {}
        }
        
        try:
            status_config = self.mongo_utils.get_status_config(app_id)
            if not status_config:
                return report
            
            # Count current work items by status using mongo utils
            status_counts = self.mongo_utils.get_status_counts_by_app(app_id)
            
            for status_count in status_counts:
                status_id = status_count['_id']
                count = status_count['count']
                status_key = self.get_status_key_by_id(status_config, status_id)
                
                report['status_counts'][status_key] = count
                report['total_processed'] += count
                
                # Count completed items as successful
                if status_key in ['completed', 'completed_manually']:
                    report['successful_count'] += count
            
            # Calculate success rate
            if report['total_processed'] > 0:
                report['success_rate'] = (report['successful_count'] / report['total_processed']) * 100
            else:
                report['success_rate'] = 0
            
            # Count status changes today using mongo utils
            today = datetime.utcnow()
            status_changes = self.mongo_utils.get_daily_status_changes(app_id, today)
            
            for change in status_changes:
                from_status = self.get_status_key_by_id(status_config, change['_id']['from'])
                to_status = self.get_status_key_by_id(status_config, change['_id']['to'])
                change_key = f"{from_status} → {to_status}"
                report['status_changes_today'][change_key] = change['count']
        
        except Exception as e:
            logger.error(f"Error generating daily report for app {app_id}: {str(e)}")
        
        return report

    # def cleanup_old_tracking_data(self, days_to_keep: int = 30):
    #     """Clean up old tracking data to prevent collection growth"""
    #     return self.mongo_utils.cleanup_old_tracking_data(days_to_keep)

    def monitor_app(self, app_id: str) -> Tuple[List[Dict], Dict, List[Dict]]:
        """Main monitoring function for a single app"""
        from alert_checker import AlertChecker
        
        alert_checker = AlertChecker(self.mongo_utils)
        all_alerts = []
        
        # First, sync current work items and detect status changes
        logger.info(f"Syncing work items for app {app_id}")
        status_changes = self.sync_current_work_items(app_id)
        
        # Check all alert conditions
        critical_alerts = alert_checker.check_critical_alerts(app_id, status_changes)
        logger.info("==================================================================================================")
        logger.info(critical_alerts)
        logger.info("==================================================================================================")
        bulk_alerts = alert_checker.check_bulk_movement_from_ready_for_dms(app_id, status_changes)
        idle_alerts = alert_checker.check_idle_ready_for_dms_items(app_id, status_changes)
        
        all_alerts.extend(critical_alerts)
        all_alerts.extend(bulk_alerts)
        all_alerts.extend(idle_alerts)
        
        # Generate daily report
        daily_report = self.generate_daily_report(app_id)
        
        return all_alerts, daily_report, status_changes
    
    def monitor_all_apps(self) -> Tuple[List[Dict], List[Dict], List[Dict]]:
        """Monitor all applications in the database"""
        all_alerts = []
        all_reports = []
        all_status_changes = []
        
        # Get all app_ids from documents
        app_ids = self.mongo_utils.get_all_app_ids()
        
        if not app_ids:
            logger.warning("No app_ids found in Document collection")
            return all_alerts, all_reports, all_status_changes
        
        logger.info(f"Monitoring {len(app_ids)} applications...")
        
        for app_id in app_ids:
            try:
                logger.info(f"Processing app_id: {app_id}")
                alerts, report, status_changes = self.monitor_app(app_id)
                
                # Add app_id context to alerts
                for alert in alerts:
                    alert['app_id'] = app_id
                
                all_alerts.extend(alerts)
                all_reports.append(report)
                all_status_changes.extend(status_changes)
                
                logger.info(f"App {app_id}: {len(status_changes)} changes, {len(alerts)} alerts")
                
            except Exception as e:
                logger.error(f"Error monitoring app {app_id}: {str(e)}")
                # Add error alert
                all_alerts.append({
                    "type": "CRITICAL",
                    "app_id": app_id,
                    "message": f"Failed to monitor app {app_id}: {str(e)}",
                    "error": str(e)
                })
        
        # Cleanup old tracking data (keep 30 days)
        self.mongo_utils.cleanup_old_tracking_data(30)
        
        return all_alerts, all_reports, all_status_changes
    
    def close_connection(self):
        """Close MongoDB connection"""
        self.mongo_utils.close_connection()