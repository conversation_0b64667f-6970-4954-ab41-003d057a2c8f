import os
import logging
from datetime import datetime, timedelta
from typing import Dict, List

logger = logging.getLogger()

class AlertChecker:
    def __init__(self, mongo_utils):
        self.mongo_utils = mongo_utils
        self.BULK_MOVE_THRESHOLD = int(os.environ.get('BULK_MOVE_THRESHOLD', '10'))
        self.IDLE_THRESHOLD_HOURS = int(os.environ.get('IDLE_THRESHOLD_HOURS', '1'))
        self.NO_BOT_RUN_IDLE_THRESHOLD_HOURS = int(os.environ.get('NO_BOT_RUN_IDLE_THRESHOLD_HOURS', '24'))
    
    def get_status_key_by_id(self, status_config: Dict, status_id: str) -> str:
        """Get status key from status ID"""
        if not status_config or 'status' not in status_config:
            return "unknown"
        
        status_info = status_config['status'].get(status_id, {})
        return status_info.get('key', 'unknown')
    
    def get_status_id_by_key(self, status_config: Dict, status_key: str) -> str:
        """Get status ID from status key"""
        if not status_config or 'status' not in status_config:
            return None
        
        for status_id, status_info in status_config['status'].items():
            if status_info.get('key') == status_key:
                return status_id
        return None

    def check_critical_alerts(self, app_id: str, status_changes: List[Dict]) -> List[Dict]:
        """Check for work items that moved FROM ready_for_dms TO error status"""
        alerts = []
        logger.info(f"Checking for critical alerts for app {app_id}")
        logger.info(f"Status changes: {status_changes}")
        
        try:
            status_config = self.mongo_utils.get_status_config(app_id)
            if not status_config:
                return alerts
            
            ready_for_dms_key = 'ready_for_dms'
            error_key = 'error'
            
            # Check for status changes from ready_for_dms to error in current execution
            critical_changes = [
                change for change in status_changes
                if (change['previous_status_key'] == ready_for_dms_key and 
                    change['current_status_key'] == error_key)
            ]
            
            # Also check tracking collection for recent changes using mongo utils
            time_threshold = datetime.utcnow() - timedelta(hours=1)
            ready_for_dms_id = self.get_status_id_by_key(status_config, ready_for_dms_key)
            error_status_id = self.get_status_id_by_key(status_config, error_key)
            
            if ready_for_dms_id and error_status_id:
                recent_error_moves = self.mongo_utils.get_recent_error_moves(
                    app_id, ready_for_dms_id, error_status_id, time_threshold
                )
                
                # Create alerts for all critical transitions
                all_critical_items = []
                
                # Add current execution changes
                for change in critical_changes:
                    all_critical_items.append({
                        "work_item_id": change['work_item_id'],
                        "change_timestamp": change['change_timestamp'],
                        "extra_data": change['extra_data'],
                        "source": "current_execution"
                    })
                
                # Add recent tracking changes (avoid duplicates)
                current_item_ids = {change['work_item_id'] for change in critical_changes}
                for item in recent_error_moves:
                    if item['work_item_id'] not in current_item_ids:
                        all_critical_items.append({
                            "work_item_id": item['work_item_id'],
                            "change_timestamp": item['timestamp'],
                            "extra_data": item.get('extra_data', {}),
                            "source": "recent_tracking"
                        })
                
                # Create alerts
                for item in all_critical_items:
                    alerts.append({
                        "type": "CRITICAL",
                        "category": "PROCESSING_FAILURE",
                        "message": f"Work Item {item['work_item_id']} failed during processing (moved from 'ready_for_dms' to 'error')",
                        "work_item_id": item['work_item_id'],
                        "status_transition": "ready_for_dms → error",
                        "change_timestamp": item['change_timestamp'],
                        "vin": item['extra_data'].get('vin', 'N/A'),
                        "make": item['extra_data'].get('make', 'N/A'),
                        "from_file": item['extra_data'].get('from_file', 'N/A'),
                        "source": item['source'],
                        "probable_cause": "Processing failure or data validation error",
                        "recommended_action": "Check processing logs and fix underlying issue",
                        "severity": "CRITICAL",
                        "issue_type": "Processing failure"
                    })
        
        except Exception as e:
            logger.error(f"Error checking critical alerts for app {app_id}: {str(e)}")
        
        logger.info(f"Found {len(alerts)} critical alerts for app {app_id}")
        return alerts

    def check_bulk_movement_from_ready_for_dms(self, app_id: str, status_changes: List[Dict]) -> List[Dict]:
        """Check for bulk movement FROM ready_for_dms TO needs_attention"""
        alerts = []
        
        try:
            status_config = self.mongo_utils.get_status_config(app_id)
            if not status_config:
                return alerts
            
            ready_for_dms_key = 'ready_for_dms'
            needs_attention_key = 'needs_attention'
            
            # Count status changes from ready_for_dms to needs_attention today
            today_start = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
            
            # Check both current execution changes and historical changes from today
            current_changes = [
                change for change in status_changes
                if (change['previous_status_key'] == ready_for_dms_key and 
                    change['current_status_key'] == needs_attention_key)
            ]
            
            logger.info(f"Found {len(current_changes)} bulk movement changes from ready_for_dms to needs_attention today")
            logger.info(f"Status changes: {status_changes}")

            # Also check tracking collection for any changes today we might have missed using mongo utils
            ready_for_dms_id = self.get_status_id_by_key(status_config, ready_for_dms_key)
            needs_attention_id = self.get_status_id_by_key(status_config, needs_attention_key)
            
            historical_changes = self.mongo_utils.count_status_changes(
                app_id, ready_for_dms_id, needs_attention_id, today_start
            )

            logger.info(f"Found {historical_changes} bulk movement changes from ready_for_dms to needs_attention in tracking collection")
            
            total_moves = len(current_changes) + historical_changes
            
            if total_moves >= self.BULK_MOVE_THRESHOLD:
                alerts.append({
                    "type": "WARNING",
                    "category": "BULK_MOVEMENT_ISSUE",
                    "message": f"Bulk movement detected: {total_moves} work items moved from 'ready_for_dms' to 'needs_attention' today",
                    "count": total_moves,
                    "current_execution_count": len(current_changes),
                    "historical_count": historical_changes,
                    "threshold": self.BULK_MOVE_THRESHOLD,
                    "date": today_start.strftime("%Y-%m-%d"),
                    "affected_items": [change['work_item_id'] for change in current_changes],
                    "probable_cause": "Systematic processing quality issues or data problems",
                    "recommended_action": "Investigate processing quality and review affected items",
                    "severity": "MEDIUM",
                    "issue_type": "Processing quality issue"
                })
        
        except Exception as e:
            logger.error(f"Error checking bulk movement for app {app_id}: {str(e)}")
        
        return alerts

    def check_idle_ready_for_dms_items(self, app_id: str, status_changes: List[Dict]) -> List[Dict]:
        """Check for work items idle in ready_for_dms status AFTER bot run started"""
        alerts = []
        
        try:
            status_config = self.mongo_utils.get_status_config(app_id)
            if not status_config:
                return alerts
            
            ready_for_dms_id = self.get_status_id_by_key(status_config, 'ready_for_dms')
            if not ready_for_dms_id:
                return alerts
            
            # Get items currently in ready_for_dms using mongo utils
            current_ready_items = self.mongo_utils.get_work_items_by_status(app_id, ready_for_dms_id)
            
            # Exclude items that just changed status in this execution to avoid duplicate alerts
            recently_changed_items = {change['work_item_id'] for change in status_changes}
            logger.info(f"Recently changed items: {recently_changed_items}")
            
            for item in current_ready_items:
                work_item_id = item.get("id")
                
                # Get the tracking record to know when this status was set
                latest_tracking = self.mongo_utils.get_latest_tracking_record(work_item_id, app_id)
                
                # If no tracking record exists for ready_for_dms, use document timestamp
                if latest_tracking and latest_tracking.get('status_id') == ready_for_dms_id:
                    status_timestamp = latest_tracking['timestamp']
                    extra_data = latest_tracking.get('extra_data', {})
                else:
                    status_timestamp = item.get("aria_last_update", datetime.utcnow())
                    extra_data = {
                        "vin": item.get('groups', {}).get('title', {}).get('fields', {}).get('vin', {}).get('value'),
                        "make": item.get('groups', {}).get('title', {}).get('fields', {}).get('make', {}).get('value'),
                        "from_file": item.get('groups', {}).get('title', {}).get('fields', {}).get('from_file', {}).get('value')
                    }
                
                # Get bot run start time for this work item
                bot_run_start = self.mongo_utils.get_bot_run_start_time(work_item_id)
                
                if bot_run_start:
                    # Check if item has been idle since bot run started
                    time_since_bot_start = datetime.utcnow() - bot_run_start
                    idle_threshold = timedelta(hours=self.IDLE_THRESHOLD_HOURS)
                    
                    if time_since_bot_start > idle_threshold:
                        alerts.append({
                            "type": "WARNING",
                            "message": f"Work Item {work_item_id} idle in 'ready_for_dms' for {self.IDLE_THRESHOLD_HOURS}+ hours after bot run started",
                            "work_item_id": work_item_id,
                            "status": "ready_for_dms",
                            "bot_run_started": bot_run_start,
                            "status_since": status_timestamp,
                            "idle_duration_since_bot": str(time_since_bot_start),
                            "idle_hours_since_bot": time_since_bot_start.total_seconds() / 3600,
                            "vin": extra_data.get('vin', 'N/A'),
                            "make": extra_data.get('make', 'N/A'),
                            "from_file": extra_data.get('from_file', 'N/A')
                        })
                else:
                    # No bot run event found, use regular idle check from status timestamp
                    idle_duration = datetime.utcnow() - status_timestamp
                    idle_threshold = timedelta(hours=self.NO_BOT_RUN_IDLE_THRESHOLD_HOURS)
                    
                    if idle_duration > idle_threshold:
                        alerts.append({
                            "type": "WARNING", 
                            "message": f"Work Item {work_item_id} idle in 'ready_for_dms' for {self.NO_BOT_RUN_IDLE_THRESHOLD_HOURS}+ hours (no bot run event found)",
                            "work_item_id": work_item_id,
                            "status": "ready_for_dms",
                            "bot_run_started": None,
                            "status_since": status_timestamp,
                            "idle_duration": str(idle_duration),
                            "idle_hours": idle_duration.total_seconds() / 3600,
                            "vin": extra_data.get('vin', 'N/A'),
                            "make": extra_data.get('make', 'N/A'),
                            "from_file": extra_data.get('from_file', 'N/A')
                        })
        
        except Exception as e:
            logger.error(f"Error checking idle ready_for_dms items for app {app_id}: {str(e)}")
        
        return alerts