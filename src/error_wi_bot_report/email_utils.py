# email_utils.py
import json
import boto3
import logging
from datetime import datetime, timedelta
from typing import Dict, List
from outlook_utils import Outlook
import os

logger = logging.getLogger(__name__)

class EmailNotificationUtils:
    def __init__(self):
        self.outlook = Outlook()
        self.env = os.environ.get('ENV', 'DEV')
        self.support_emails = [email.strip() for email in os.environ.get('REPORTS_EMAIL', '').split(',')]
        self.copy_emails = [email.strip() for email in os.environ.get('BCC_EMAIL', '').split(',')]
        self.sender = os.environ.get('REPORTER_EMAIL')
    
    def convert_objects_to_string(self, obj):
        """Convert datetime and timedelta objects to strings for JSON serialization"""
        if isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, timedelta):
            return str(obj)
        elif isinstance(obj, dict):
            return {k: self.convert_objects_to_string(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self.convert_objects_to_string(item) for item in obj]
        else:
            return obj
    
    def generate_consolidated_monitoring_email(self, alerts_data: Dict) -> str:
        """Generate consolidated HTML email body for all monitoring data"""
        
        alerts = alerts_data.get('alerts', [])
        reports = alerts_data.get('reports', [])
        status_changes = alerts_data.get('status_changes', [])
        timestamp = alerts_data.get('timestamp', datetime.utcnow().isoformat())
        
        total_alerts = len(alerts)
        total_apps = alerts_data.get('total_applications', 0)
        critical_count = alerts_data.get('critical_count', 0)
        warning_count = alerts_data.get('warning_count', 0)
        
        # Determine email type and color scheme
        if critical_count > 0:
            header_color = "#d32f2f"
            email_type = "CRITICAL ALERTS"
            urgency = "IMMEDIATE ATTENTION REQUIRED"
        elif warning_count > 0:
            header_color = "#ff9800"
            email_type = "WARNING ALERTS"
            urgency = "ATTENTION REQUIRED"
        elif total_alerts == 0 and reports:
            header_color = "#4caf50"
            email_type = "DAILY REPORT"
            urgency = "NO ISSUES DETECTED"
        else:
            header_color = "#2196f3"
            email_type = "STATUS UPDATE"
            urgency = "INFORMATIONAL"
        
        html = f"""
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }}
                .header {{ background-color: {header_color}; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }}
                .summary-box {{ background-color: #f8f9fa; border-left: 4px solid {header_color}; padding: 15px; margin: 20px 0; border-radius: 5px; }}
                .alert-section {{ margin: 25px 0; }}
                .critical {{ border-left: 4px solid #d32f2f; background-color: #ffebee; padding: 15px; margin: 10px 0; }}
                .warning {{ border-left: 4px solid #ff9800; background-color: #fff8e1; padding: 15px; margin: 10px 0; }}
                .info {{ border-left: 4px solid #2196f3; background-color: #e3f2fd; padding: 15px; margin: 10px 0; }}
                .success {{ border-left: 4px solid #4caf50; background-color: #e8f5e9; padding: 15px; margin: 10px 0; }}
                table {{ border-collapse: collapse; width: 100%; margin: 15px 0; }}
                th, td {{ border: 1px solid #ddd; padding: 12px; text-align: left; }}
                th {{ background-color: #f5f5f5; font-weight: bold; }}
                .app-section {{ margin: 30px 0; border: 1px solid #ddd; border-radius: 8px; overflow: hidden; }}
                .app-header {{ background-color: #1976d2; color: white; padding: 15px; font-size: 18px; font-weight: bold; }}
                .metric {{ display: inline-block; margin: 10px 15px 0 0; }}
                .metric-value {{ font-size: 24px; font-weight: bold; color: #3f3434; }}
                .metric-label {{ font-size: 12px; color: #666; }}
                .status-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }}
                .status-card {{ background: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #ddd; }}
                .no-alerts {{ background-color: #e8f5e9; color: #2e7d32; padding: 20px; text-align: center; border-radius: 8px; margin: 20px 0; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🔍 Work Item Monitoring Report - {email_type}</h1>
                <p><strong>Report Time:</strong> {timestamp}</p>
                <p><strong>Status:</strong> {urgency}</p>
                <div style="margin-top: 15px;">
                    <span class="metric">
                        <div class="metric-value">{total_apps}</div>
                        <div class="metric-label">Applications</div>
                    </span>
                    <span class="metric">
                        <div class="metric-value">{total_alerts}</div>
                        <div class="metric-label">Total Alerts</div>
                    </span>
                    <span class="metric">
                        <div class="metric-value">{critical_count}</div>
                        <div class="metric-label">Critical</div>
                    </span>
                    <span class="metric">
                        <div class="metric-value">{warning_count}</div>
                        <div class="metric-label">Warnings</div>
                    </span>
                </div>
            </div>
        """
        
        # Executive Summary
        if reports:
            total_items = sum(r.get('total_processed', 0) for r in reports)
            total_successful = sum(r.get('successful_count', 0) for r in reports)
            success_rate = (total_successful / total_items * 100) if total_items > 0 else 0
            
            html += f"""
            <div class="summary-box">
                <h3>📊 Executive Summary</h3>
                <div class="status-grid">
                    <div class="status-card">
                        <strong>{total_items:,}</strong><br>
                        <small>Total Work Items</small>
                    </div>
                    <div class="status-card">
                        <strong>{total_successful:,}</strong><br>
                        <small>Successfully Processed</small>
                    </div>
                    <div class="status-card">
                        <strong>{success_rate:.1f}%</strong><br>
                        <small>Success Rate</small>
                    </div>
                    <div class="status-card">
                        <strong>{len(status_changes):,}</strong><br>
                        <small>Status Changes Today</small>
                    </div>
                </div>
            </div>
            """
        
        # Alerts Section
        if alerts:
            # Group alerts by app and type
            alerts_by_app = {}
            for alert in alerts:
                app_id = alert.get('app_id', 'Unknown')
                if app_id not in alerts_by_app:
                    alerts_by_app[app_id] = {'critical': [], 'warning': [], 'info': []}
                
                alert_type = alert.get('type', 'INFO').lower()
                if alert_type == 'critical':
                    alerts_by_app[app_id]['critical'].append(alert)
                elif alert_type == 'warning':
                    alerts_by_app[app_id]['warning'].append(alert)
                else:
                    alerts_by_app[app_id]['info'].append(alert)
            
            html += """
            <div class="alert-section">
                <h2>🚨 Alert Details</h2>
            """
            
            for app_id, app_alerts in alerts_by_app.items():
                # Get app name
                app_name = app_id
                if reports:
                    for report in reports:
                        if report.get('app_id') == app_id:
                            app_name = report.get('app_name', app_id)
                            break
                
                total_app_alerts = len(app_alerts['critical']) + len(app_alerts['warning']) + len(app_alerts['info'])
                
                html += f"""
                <div class="app-section">
                    <div class="app-header">
                        {app_name} 
                        <span style="float: right; font-size: 14px;">
                            {len(app_alerts['critical'])} Critical | {len(app_alerts['warning'])} Warning | {len(app_alerts['info'])} Info
                        </span>
                    </div>
                    <div style="padding: 20px;">
                """
                
                # Critical Alerts
                if app_alerts['critical']:
                    html += """
                    <div class="critical">
                        <h4>🔥 CRITICAL: Processing Failures</h4>
                        <p><strong>Issue:</strong> Work items failed during processing and moved to ERROR status</p>
                        <table>
                            <tr><th>Work Item ID</th><th>VIN</th><th>Make</th><th>Source File</th><th>Failure Time</th><th>Action Required</th></tr>
                    """
                    
                    for alert in app_alerts['critical']:
                        html += f"""
                        <tr>
                            <td><strong>{alert.get('work_item_id', 'N/A')}</strong></td>
                            <td>{alert.get('vin', 'N/A')}</td>
                            <td>{alert.get('make', 'N/A')}</td>
                            <td>{alert.get('from_file', 'N/A')}</td>
                            <td>{alert.get('change_timestamp', 'N/A')}</td>
                            <td><strong style="color: #d32f2f;">CHECK LOGS IMMEDIATELY</strong></td>
                        </tr>
                        """
                    
                    html += "</table></div>"
                
                # Warning Alerts
                if app_alerts['warning']:
                    html += """
                    <div class="warning">
                        <h4>⚠️ WARNINGS: Processing Issues</h4>
                        <table>
                            <tr><th>Type</th><th>Work Item ID</th><th>Details</th><th>Duration/Count</th><th>Action Required</th></tr>
                    """
                    
                    for alert in app_alerts['warning']:
                        message = alert.get('message', '')
                        if 'bulk movement' in message.lower():
                            alert_type = "Bulk Movement"
                            details = f"Ready for DMS → Needs Attention"
                            duration = f"{alert.get('count', 0)} items"
                            action = "Review processing quality"
                        elif alert.get('bot_run_started'):
                            alert_type = "Bot Processing"
                            details = f"VIN: {alert.get('vin', 'N/A')}"
                            duration = f"{alert.get('idle_hours_since_bot', 0):.1f}h since bot start"
                            action = "Check bot status/restart" if alert.get('idle_hours_since_bot', 0) > 12 else "Monitor bot"
                        else:
                            alert_type = "Infrastructure"
                            details = f"VIN: {alert.get('vin', 'N/A')}"
                            duration = f"{alert.get('idle_hours', 0):.1f}h total idle"
                            action = "Check infrastructure"
                        
                        html += f"""
                        <tr>
                            <td><strong>{alert_type}</strong></td>
                            <td>{alert.get('work_item_id', 'N/A')}</td>
                            <td>{details}</td>
                            <td>{duration}</td>
                            <td>{action}</td>
                        </tr>
                        """
                    
                    html += "</table></div>"
                
                html += "</div></div>"
            
            html += "</div>"
        else:
            # No alerts
            html += """
            <div class="no-alerts">
                <h3>✅ No Alerts Detected</h3>
                <p>All systems are operating normally. No critical issues or warnings found.</p>
            </div>
            """
        
        # Daily Reports Section
        if reports:
            html += """
            <div class="alert-section">
                <h2>📈 Application Status Reports</h2>
            """
            
            for report in reports:
                app_name = report.get('app_name', report.get('app_id', 'Unknown'))
                total = report.get('total_processed', 0)
                successful = report.get('successful_count', 0)
                success_rate = report.get('success_rate', 0)
                
                html += f"""
                <div class="info">
                    <h4>{app_name}</h4>
                    <div class="status-grid">
                        <div class="status-card">
                            <strong>{total:,}</strong><br><small>Total Items</small>
                        </div>
                        <div class="status-card">
                            <strong>{successful:,}</strong><br><small>Successful</small>
                        </div>
                        <div class="status-card">
                            <strong>{success_rate:.1f}%</strong><br><small>Success Rate</small>
                        </div>
                    </div>
                """
                
                # Status breakdown
                status_counts = report.get('status_counts', {})
                if status_counts:
                    html += "<p><strong>Status Breakdown:</strong> "
                    status_parts = []
                    for status, count in sorted(status_counts.items()):
                        status_parts.append(f"{status.replace('_', ' ').title()}: {count}")
                    html += " | ".join(status_parts)
                    html += "</p>"
                
                html += "</div>"
            
            html += "</div>"
        
        # Recent Activity Summary
        if status_changes:
            change_summary = {}
            for change in status_changes:
                change_key = f"{change['previous_status_key']} → {change['current_status_key']}"
                change_summary[change_key] = change_summary.get(change_key, 0) + 1
            
            if change_summary:
                html += """
                <div class="alert-section">
                    <h2>📊 Recent Status Changes</h2>
                    <div class="info">
                        <table>
                            <tr><th>Status Transition</th><th>Count</th></tr>
                """
                
                for transition, count in sorted(change_summary.items(), key=lambda x: x[1], reverse=True):
                    html += f"<tr><td>{transition}</td><td><strong>{count}</strong></td></tr>"
                
                html += "</table></div></div>"
        
        # Footer
        html += f"""
            <hr style="margin: 30px 0;">
            <div style="text-align: center; color: #666; font-size: 12px;">
                <p>This consolidated report was generated by the Work Item Monitoring System</p>
                <p>Environment: {self.env} | Report Time: {timestamp}</p>
            </div>
        </body>
        </html>
        """
        
        return html
    
    def send_consolidated_monitoring_report(self, alerts: List[Dict], reports: List[Dict] = None, status_changes: List[Dict] = None) -> bool:
        """Send consolidated monitoring report via email"""
        try:
            # Always send one email, even if no alerts (for daily reports)
            email_data = {
                'alerts': self.convert_objects_to_string(alerts),
                'reports': self.convert_objects_to_string(reports or []),
                'status_changes': self.convert_objects_to_string(status_changes or []),
                'timestamp': datetime.utcnow().isoformat(),
                'total_applications': len(reports) if reports else 0,
                'critical_count': len([a for a in alerts if a.get('type') == 'CRITICAL']),
                'warning_count': len([a for a in alerts if a.get('type') == 'WARNING']),
                'change_count': len(status_changes) if status_changes else 0
            }
            
            # Generate subject based on content
            critical_count = email_data['critical_count']
            warning_count = email_data['warning_count']
            total_apps = email_data['total_applications']

            # Get app names for subject
            app_names = []
            if reports:
                app_names = [r.get('app_name', r.get('app_id', 'Unknown')) for r in reports[:3]]  # Limit to 3 apps, 20 chars each
            
            apps_text = ", ".join(app_names) if len(app_names) <= 2 else f"{app_names[0]}, {app_names[1]} +{total_apps-2} more"
            apps_suffix = f" [{apps_text}]" if app_names else f" ({total_apps} apps)"
            
            if critical_count > 0:
                subject = f"{self.env.upper()} - {os.environ.get('CUSTOMER')} - 🔥 CRITICAL: {critical_count} Critical Issues{apps_suffix}"
                priority = "High"
            elif warning_count > 0:
                subject = f"{self.env.upper()} - {os.environ.get('CUSTOMER')} - ⚠️ WARNING: {warning_count} Issues Detected{apps_suffix}"
                priority = "Normal"
            elif reports:
                total_items = sum(r.get('total_processed', 0) for r in reports)
                success_rate = (sum(r.get('successful_count', 0) for r in reports) / total_items * 100) if total_items > 0 else 0
                subject = f"{self.env.upper()} - {os.environ.get('CUSTOMER')} - ✅ Daily Report: {total_items:,} items, {success_rate:.1f}% success{apps_suffix}"
                priority = "Low"
            else:
                subject = f"{self.env.upper()} - {os.environ.get('CUSTOMER')} - 📊 Work Item Monitoring Update{apps_suffix}"
                priority = "Low"
            
            # Generate consolidated email body
            body = self.generate_consolidated_monitoring_email(email_data)

                        
            print('------------------------------------------------------------------------------------')
            print(subject)
            print(body)
            print('------------------------------------------------------------------------------------')
            
            # Send single consolidated email
            self.outlook.send_email_notification(
                subject=subject, 
                body=body, 
                emails={
                    "sender": self.sender,
                    "recipients": self.support_emails,
                    "bcc": self.copy_emails
                },
                priority=priority
            )
            
            logger.info(f"Consolidated monitoring email sent successfully: {subject}")
            return True
            
        except Exception as e:
            logger.error(f"Error sending consolidated monitoring email: {e}")
            return False
