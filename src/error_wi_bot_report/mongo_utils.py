from boto3_utils import get_secret
from pymongo import MongoClient
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import logging

logger = logging.getLogger()

class MongoClientUtils:
    def __init__(self):
        mongo_uri = get_secret(f"{os.environ['ENV']}-mongodb_uri", return_json=False)
        self.client = MongoClient(mongo_uri)
        self.db = self.client[os.environ['MONGO_DATABASE']]
        
        # Collection names
        self.DOCUMENT_COLLECTION = 'Document'
        self.STATUS_CONFIG_COLLECTION = 'StatusConfig'
        self.TRACKING_COLLECTION = 'WorkItemTracking'
        self.EVENT_COLLECTION = 'Event'
        self.VIN_COLLECTION = 'vin'
        self.APP_COLLECTION = 'App'

    def create_tracking_indexes(self):
        """Create necessary indexes for the tracking collection"""
        try:
            # Compound index for efficient querying
            self.db[self.TRACKING_COLLECTION].create_index([
                ("work_item_id", 1),
                ("app_id", 1),
                ("status_id", 1),
                ("timestamp", -1)
            ])
            
            # Index for cleanup queries
            self.db[self.TRACKING_COLLECTION].create_index([("timestamp", 1)])
            
            logger.info("Tracking collection indexes created/verified")
        except Exception as e:
            logger.error(f"Error creating indexes: {str(e)}")

    def get_all_app_ids(self) -> List[str]:
        """Get all unique app_ids from the Document collection"""
        try:
            app_ids = self.db[self.DOCUMENT_COLLECTION].distinct("app_id")
            logger.info(f"Found {len(app_ids)} unique app_ids: {app_ids}")
            return app_ids
        except Exception as e:
            logger.error(f"Error fetching app_ids: {str(e)}")
            return []

    def get_status_config(self, app_id: str) -> Optional[Dict]:
        """Get status configuration for an app"""
        try:
            status_config = self.db[self.STATUS_CONFIG_COLLECTION].find_one({"app_id": app_id})
            return status_config
        except Exception as e:
            logger.error(f"Error fetching status config for app {app_id}: {str(e)}")
            return None

    def get_app_name(self, app_id: str) -> str:
        """Get application name from App collection"""
        try:
            app_doc = self.db[self.APP_COLLECTION].find_one({"app_id": app_id})
            if app_doc:
                return app_doc.get('name', app_id)
            else:
                return app_id
        except Exception as e:
            logger.error(f"Error fetching app name for {app_id}: {str(e)}")
            return app_id

    def get_bot_run_start_time(self, work_item_id: str) -> Optional[datetime]:
        """Get the last bot run start time for a work item from Event collection"""
        try:
            logger.info(f"Getting bot run start time for {work_item_id}")
            bot_event = self.db[self.EVENT_COLLECTION].find_one(
                {
                    "document_id": work_item_id,
                    "title": "Bot run started"
                },
                sort=[("date", -1)]
            )
            logger.info(f"Bot event: {bot_event}")
            if bot_event:
                return bot_event.get("date")
            else:
                logger.debug(f"No bot run start event found for work item {work_item_id}")
                return None
                
        except Exception as e:
            logger.error(f"Error getting bot run start time for {work_item_id}: {str(e)}")
            return None

    def get_current_work_items(self, app_id: str) -> List[Dict]:
        """Get all current work items for an app"""
        try:
            current_items = self.db[self.DOCUMENT_COLLECTION].find(
                {"app_id": app_id},
                {
                    "id": 1,
                    "aria_status": 1,
                    "aria_last_update": 1,
                    "groups.title.fields.vin.value": 1,
                    "groups.title.fields.make.value": 1,
                    "groups.title.fields.from_file.value": 1
                }
            )
            return list(current_items)
        except Exception as e:
            logger.error(f"Error fetching work items for app {app_id}: {str(e)}")
            return []

    def get_work_items_by_status(self, app_id: str, status_id: str) -> List[Dict]:
        """Get work items by specific status"""
        try:
            items = self.db[self.DOCUMENT_COLLECTION].find(
                {
                    "app_id": app_id,
                    "aria_status": status_id
                },
                {
                    "id": 1,
                    "aria_status": 1,
                    "aria_last_update": 1,
                    "groups.title.fields.vin.value": 1,
                    "groups.title.fields.make.value": 1,
                    "groups.title.fields.from_file.value": 1
                }
            )
            return list(items)
        except Exception as e:
            logger.error(f"Error fetching work items by status for app {app_id}: {str(e)}")
            return []

    def get_latest_tracking_record(self, work_item_id: str, app_id: str) -> Optional[Dict]:
        """Get the latest tracking record for a work item"""
        try:
            latest_record = self.db[self.TRACKING_COLLECTION].find_one(
                {"work_item_id": work_item_id, "app_id": app_id},
                sort=[("timestamp", -1)]
            )
            return latest_record
        except Exception as e:
            logger.error(f"Error fetching latest tracking record for {work_item_id}: {str(e)}")
            return None

    def insert_tracking_record(self, tracking_record: Dict) -> bool:
        """Insert a new tracking record"""
        try:
            self.db[self.TRACKING_COLLECTION].insert_one(tracking_record)
            return True
        except Exception as e:
            logger.error(f"Error inserting tracking record: {str(e)}")
            return False

    def update_tracking_record(self, record_id, update_data: Dict) -> bool:
        """Update an existing tracking record"""
        try:
            result = self.db[self.TRACKING_COLLECTION].update_one(
                {"_id": record_id},
                {"$set": update_data}
            )
            return result.modified_count > 0
        except Exception as e:
            logger.error(f"Error updating tracking record: {str(e)}")
            return False

    def count_status_changes(self, app_id: str, from_status_id: str, to_status_id: str, 
                           since_date: datetime) -> int:
        """Count status changes between specific statuses since a date"""
        try:
            count = self.db[self.TRACKING_COLLECTION].count_documents({
                "app_id": app_id,
                "previous_status_id": from_status_id,
                "status_id": to_status_id,
                "timestamp": {"$gte": since_date},
                "status_change": True
            })
            return count
        except Exception as e:
            logger.error(f"Error counting status changes: {str(e)}")
            return 0

    def get_recent_error_moves(self, app_id: str, from_status_id: str, to_status_id: str,
                              since_date: datetime) -> List[Dict]:
        """Get recent moves to error status"""
        try:
            recent_moves = self.db[self.TRACKING_COLLECTION].find({
                "app_id": app_id,
                "previous_status_id": from_status_id,
                "status_id": to_status_id,
                "status_change": True,
                "timestamp": {"$gte": since_date}
            })
            return list(recent_moves)
        except Exception as e:
            logger.error(f"Error fetching recent error moves: {str(e)}")
            return []

    def get_status_counts_by_app(self, app_id: str) -> List[Dict]:
        """Get current status counts for an app"""
        try:
            pipeline = [
                {"$match": {"app_id": app_id}},
                {"$group": {"_id": "$aria_status", "count": {"$sum": 1}}}
            ]
            status_counts = list(self.db[self.DOCUMENT_COLLECTION].aggregate(pipeline))
            return status_counts
        except Exception as e:
            logger.error(f"Error getting status counts for app {app_id}: {str(e)}")
            return []

    def get_daily_status_changes(self, app_id: str, date: datetime) -> List[Dict]:
        """Get status changes for a specific day"""
        try:
            day_start = date.replace(hour=0, minute=0, second=0, microsecond=0)
            day_end = day_start + timedelta(days=1)
            
            pipeline = [
                {
                    "$match": {
                        "app_id": app_id,
                        "timestamp": {"$gte": day_start, "$lt": day_end},
                        "status_change": True
                    }
                },
                {
                    "$group": {
                        "_id": {
                            "from": "$previous_status_id",
                            "to": "$status_id"
                        },
                        "count": {"$sum": 1}
                    }
                }
            ]
            
            status_changes = list(self.db[self.TRACKING_COLLECTION].aggregate(pipeline))
            return status_changes
        except Exception as e:
            logger.error(f"Error getting daily status changes for app {app_id}: {str(e)}")
            return []

    def cleanup_old_tracking_data(self, days_to_keep: int = 30) -> int:
        """Clean up old tracking data"""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days_to_keep)
            result = self.db[self.TRACKING_COLLECTION].delete_many({
                "timestamp": {"$lt": cutoff_date}
            })
            logger.info(f"Cleaned up {result.deleted_count} old tracking records")
            return result.deleted_count
        except Exception as e:
            logger.error(f"Error cleaning up tracking data: {str(e)}")
            return 0

    def close_connection(self):
        """Close MongoDB connection"""
        if self.client:
            self.client.close()