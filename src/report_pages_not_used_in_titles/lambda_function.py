import pandas as pd
from boto3_utils import get_secret
import pymongo
import os
from datetime import datetime, timedelta
from outlook_utils import Outlook
import traceback
import io
import json

def group_pages(pages):
    """Converts a sorted list of page numbers into grouped ranges."""
    if not pages:
        return ""
    
    grouped = []
    start = pages[0]
    prev = pages[0]

    for num in pages[1:]:
        if num == prev + 1:
            prev = num  # Extend the current range
        else:
            # If discontinuity, save the previous range and start a new one
            if start == prev:
                grouped.append(f"{start}")
            else:
                grouped.append(f"{start}-{prev}")
            start = prev = num

    # Add the last range
    if start == prev:
        grouped.append(f"{start}")
    else:
        grouped.append(f"{start}-{prev}")

    return ", ".join(grouped)
    
def get_not_used_pages(vins_title_field, total_pages):
    total_pages = int(total_pages)
    rows = vins_title_field.get('rows', {}) or vins_title_field.get('value', {})

    total_pages_used = []
    for k, v in rows.items():
        vin_pages_used = v['cells']['pages']['value']                          
        for x in vin_pages_used.split(","):
            total_pages_used.append(int(x))

    # Generate a set of all possible pages (1 to total_pages)
    all_pages = set(range(1, total_pages + 1))
    
    # Find the unused pages by subtracting used pages from all pages
    print(total_pages_used)
    not_used_pages = all_pages - set(total_pages_used)
    
    # Return the sorted list of not used pages
    return group_pages(sorted(list(not_used_pages)))


def lambda_handler(event, context):

    print(event)
    
    print(f"******+ EXECUTING REPORT FOR TITLE ******+")

    try:

        # Connect to MongoDB
        collection_name = 'title'
        mongo_uri = get_secret(os.environ['ENV'] + '-mongodb_uri', return_json=False)
        client = pymongo.MongoClient(mongo_uri)
        db = client[os.environ['MONGO_DATABASE']]
        collection = db[collection_name]

        # Get data from the last 1 hour
        today_midnight = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
        query = {"read_at": {"$gte": today_midnight}}
        cursor = collection.find(query)

        today_titles = list(cursor)

        # Convert to DataFrame
        df = pd.DataFrame(columns=['Title name', 'Read', 'Pages not used'])

        for title in today_titles:
            title_name = title['title_name']
            readed = title['read_at']
            pages_not_used = get_not_used_pages(title['fields']['vins'], title['fields']['pdf_pages']['value']) 
            df.loc[df.shape[0]] = [title_name, readed, pages_not_used]
            
        df["Read"] = df["Read"].dt.strftime("%Y-%m-%d %H:%M:%S")


        now_date = datetime.now()
        file_date_format = str(now_date.year) + str(now_date.month) + str(now_date.day) + "_" + str(now_date.hour)
        summary_file_name = f"{file_date_format}_REPORT_NOT_USED_PAGES_DATA_{collection_name.upper()}_report.csv"

        csv_attachments = {}
        csv_attachments[summary_file_name] = io.StringIO()
        df.to_csv(csv_attachments[summary_file_name] , index=False)
        csv_attachments[summary_file_name].seek(0)


        try:
            env = os.environ.get('ENV')
            outlook = Outlook()
            support_emails = [email.strip() for email in os.environ.get('REPORTS_EMAIL', '').split(',')]
            copy_emails = [email.strip() for email in os.environ.get('BCC_EMAIL', '').split(',')]
            sender = os.environ.get('REPORTER_EMAIL')
            
            subject = f"{env.upper()} - ARIA Work Item Loading for {collection_name.upper()}"
            body = outlook.generate_email_body_html()
            
            outlook.send_email_notification(subject=subject, body=body, attachment_files=csv_attachments, emails={
                "sender": sender,
                "recipients": support_emails,
                "bcc": copy_emails
            })

            return {'statusCode': 200, 'body': json.dumps({'message': f"Report for {collection_name.upper()} processed correctly."})}

        except Exception as e:
            print(f"Error sending email: {e}")
            return {'statusCode': 500, 'body': json.dumps({'message': f"Error sending email: {e}"})}

    except Exception as e:
        print("Error when procesing report: ", traceback.format_exc())
        return {'statusCode': 500, 'body': json.dumps({'message': f"Error when procesing report: {e}"})}
