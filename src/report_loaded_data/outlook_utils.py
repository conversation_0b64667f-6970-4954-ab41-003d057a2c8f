import os
import msal
import smtplib
import requests
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON><PERSON>
from email.mime.text import MIMEText
from datetime import datetime
from boto3_utils import get_secret
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email import encoders

IDENTITY_ENDPOINT = 'https://login.microsoftonline.com/'
GRAPH_ENDPOINT = 'https://graph.microsoft.com/v1.0/users/'

class Outlook:
    def __init__(self):
        """
        This method initializes the Outlook class with the client credentials.
        """
        credentials = get_secret(os.environ['ENV'] + '-email_credentials')
        self.config = credentials['aria_cloud']
        
        self.username = self.config['USERNAME_REPORTER']
        self.password = self.config['PASS_REPORTER_EMAIL']
        self.smtp = self.config['SMTP']
        self.port = self.config['PORT']


    def send_email_notification(self, subject, body, attachment_files, emails):
        message = MIMEMultipart()
        message['From'] = emails['sender']
        message['To'] = ", ".join(emails['recipients'])
        message['Subject'] = subject
        message.attach(MIMEText(body, 'html'))
    
        for filename, csv_content in attachment_files.items():
            part = MIMEBase("application", "octet-stream")
            part.set_payload(csv_content.getvalue().encode('utf-8'))  # Convertir a bytes
            encoders.encode_base64(part)
            part.add_header("Content-Disposition", f"attachment; filename={filename}")
            message.attach(part)

        try:
            server = smtplib.SMTP(self.smtp, self.port)
            server.starttls() 

            server.login(self.username, self.password)

            text = message.as_string()
            server.sendmail(emails['sender'], emails['recipients'] + emails['bcc'], text)
            print('Email sent successfully')
        except Exception as e:
            print(f"Error: {e}")
            
        finally:
            server.quit()

    def get_access_token(self):
        """
        This function retrieves an access token from Azure AD using the client credentials flow.
        """
        authority_url = f"{IDENTITY_ENDPOINT}{self.tenan_id}"
        scopes = ['https://graph.microsoft.com/.default']

        # Initialize the MSAL app with the client credentials
        app = msal.ConfidentialClientApplication(
            self.client_id,
            authority=authority_url,
            client_credential=self.client_secret
        )
        # Acquire a token for the client
        result = app.acquire_token_for_client(scopes)

        # Check if the access token was successfully acquired
        if 'access_token' in result:
            return result['access_token']
        else:
            # If the access token was not acquired, print the error details and raise an exception
            print('Failed to acquire token:', result.get('error'), result.get('error_description'))
            raise Exception('Failed to acquire access token')

    def generate_email_body_html(self):
        """
        Generates an HTML email body with a table of stuck work items for each ARIA app.
        """
        env = os.environ.get('ENV')
        timestamp = datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')

        body = f"""
        <html>
        <body>
            <p>Hello,</p>
            <p>This is an automated message. Please find attached the report.</p>
            <p><strong>Environment:</strong> {env}</p>
            <p><strong>Execution Timestamp:</strong> {timestamp}</p>
            <hr>
            <p>Thank you,<br>The ARIA System Team</p>
        </body>
        </html>
        """
        return body
