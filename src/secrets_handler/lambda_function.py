import json
from boto3_utilities import Boto3Utilities


def lambda_handler(event, context):
   """
   ################# CREATE #################
   create a secret with a json value
   {
      "action":"create",
      "secret_data":{
         "rpa_test_secret_v6":{
            "key": "value"
         }
      }
   }

   create a secret with a str value
   {
      "action":"create",
      "secret_data":{
         "rpa_test_secret_v6": "cacatua"
      }
   }


   ################# UPDATE #################
   update a secret with a str value
   {
      "action":"update",
      "secret_data":{
         "rpa_test_secret_v6": "cacatua"
      }
   }

   update a secret with a json value
   {
      "action":"update",
      "secret_data":{
         "rpa_test_secret_v6": {
            "key": "value"
         }
      }
   }


   ################# GET #################
   {
      "action":"get",
      "secret_data":{
         "rpa_test_secret_v6": ""
      }
   }


   ################# DELETE #################
   {
      "action":"delete",
      "secret_data":{
         "rpa_test_secret_v6": ""
      }
   }
    
   """    
   try:
      # Extracting event data
      event = json.loads(event['body']) if isinstance(event['body'], str) else event['body']
      action = event.get('action','')
      if action not in ['create', 'get', 'delete', 'update']:
         raise Exception(f'Action not supported: {action}')
      secret_data = event.get('secret_data','')
      if not secret_data:
         raise Exception(f'Secret data not found: {secret_data}')
      secret_name = list(secret_data.keys())[0]
      secret_value = list(secret_data.values())[0]
      
      # Perform action
      result = getattr(Boto3Utilities(), "%s_secret" % str(action))(secret_name, secret_value)

      return {
         'statusCode': 200,
         'body': json.dumps(result) if isinstance(result, dict) else result
      }
   except Exception as e:
      print(f'Error: {e}')
      return {
         'statusCode': 500,
         'body': json.dumps('NOK!')
      }