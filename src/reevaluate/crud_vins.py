# This module contains a class for interacting with the Folios collection in the MongoDB database.

import os
from datetime import datetime
from mongo_utils import Mongo
from boto3_utils import get_secret


class CrudVins:
    def __init__(self, mongo):
        self.mongo = mongo
        self.collection_name='vin'
    
    def get_vins_in_on_hold(self):
        
        query = {
            "$and": [
                {"flows.post-inventory.docs.invoice.aria_data.status": "On Hold"},
                {
                    "$or": [
                        {"flows.post-inventory.report-data.svc_ro_date": {"$nin": [None, ""]}},
                        {"flows.post-inventory.report-data.svc_ro_date": {"$exists": False}}
                    ]
                }
            ]
        }

        self.mongo.select_db_and_collection(db_name=os.environ['MONGO_DATABASE'], collection_name=self.collection_name)
        return self.mongo.find(query)
    
    def get_used_cars_vins_in_on_hold(self):
        
        query = {
            "$and": [
                {"flows.used-cars.docs.invoice.aria_data.status": "On Hold"},
                {
                    "$or": [
                        {"flows.used-cars.report-data.memo": {"$nin": [None, ""]}},
                        {"flows.used-cars.report-data.memo": {"$exists": False}},
                        {"flows.used-cars.report-data.store": {"$nin": [None, ""]}},
                        {"flows.used-cars.report-data.store": {"$exists": False}}
                    ]
                }
            ]
        }

        self.mongo.select_db_and_collection(db_name=os.environ['MONGO_DATABASE'], collection_name=self.collection_name)
        return self.mongo.find(query)
    
    def __del__(self):
        self.mongo.close_connection()