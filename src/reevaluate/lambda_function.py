import json
import os
from boto3_utils import get_secret
from mongo_utils import Mongo
from crud_vins import CrudVins
from crud_invoices import CrudInvoices
from crud_statuses import CrudStatuses
from crud_bols import CrudBols
from crud_titles import Crud<PERSON><PERSON>les
import traceback
from typing import Dict, Any, <PERSON><PERSON>
from boto3_utils import trigger_lambda
from datetime import datetime

class Reevaluate():
    def __init__(self):
        self.mongo_uri = get_secret(f'{os.environ["ENV"]}-mongodb_uri', False)
        self.mongo_client = Mongo(self.mongo_uri)

        self.crud_vins = CrudVins(self.mongo_client)
        self.crud_invoices = CrudInvoices(self.mongo_client)  
        self.crud_bols = CrudBols(self.mongo_client)  
        self.crud_statuses = CrudStatuses(self.mongo_client)
        self.crud_titles = CrudTitles(self.mongo_client)


    def get_status_label_info(self, status: str, status_info: Dict[str, Any]) -> <PERSON>ple[str, str]:
        for k, v in status_info.items():
            if status in v['label'].lower():
                return k, v['label']
        return '', ''

    def get_titles_not_in_stock(self):
        """
        Get all the titles that are in Not in stock
        """
        titles_list = self.crud_titles.get_titles_not_in_stock()

        print("************ REEVALUATING TITLES ************")

        for title in titles_list:

            print("***** PROCESSING TITLE *******")
            print(title['title_id'])
            print("***** ************** *******")


            document_id = title['aria_wi_id']
            app_id = title['aria_app_id']
            actual_status_label = title['status']
            
            
            status_dict = self.crud_statuses.get_statuses_by_app_id(app_id)['status']
            actual_aria_status_key, _ = self.get_status_label_info(actual_status_label.lower(), status_dict)
            target_aria_status_key, _ = self.get_status_label_info("ready", status_dict)

            fields_data = title['fields']
            
            bre_event = {
                "document": {
                    "id": document_id,
                    "app_id": app_id,
                    "aria_status": actual_aria_status_key,
                    "groups": {
                        "title": {"fields": fields_data}
                    },
                    "ocr_groups": ["title"]
                },
                "action": {
                    "source_status": actual_aria_status_key,
                    "target_status": target_aria_status_key,
                    "action_id": "000000000",
                    "action_label": "reprocess"
                },
                "status": status_dict
            }
                    
            # Trigger the BRE to reprocess the folio
            try:
                trigger_lambda(os.getenv('BRE_HANDLER_LAMBDA'), {'body': json.dumps(bre_event)})
            except Exception as e:
                return {
                    'statusCode': 500,
                    'body': json.dumps({'message': 'Failed to trigger BRE: {}'.format(str(e))})
                }

    def get_invoices_in_on_hold(self):
        """
        Get all the invoices that have not a BOL or a title
        """
        vins_list = self.crud_vins.get_vins_in_on_hold()

        print("************ REEVALUATING INVOICES ************")

        for vin_data in vins_list:

            print("***** PROCESSING VIN *******")
            print(vin_data['vin'])
            print("***** ************** *******")

            vin = vin_data["vin"]
            something_changed = False

            document_id = vin_data["flows"]["post-inventory"]["docs"]['invoice']['aria_data']['aria_wi_id']
            app_id = vin_data["flows"]["post-inventory"]["docs"]['invoice']['aria_data']['aria_app_id']
            actual_status_label = vin_data["flows"]["post-inventory"]["docs"]['invoice']['aria_data']['status']
            
            
            status_dict = self.crud_statuses.get_statuses_by_app_id(app_id)['status']
            actual_aria_status_key, _ = self.get_status_label_info(actual_status_label.lower(), status_dict)
            target_aria_status_key, _ = self.get_status_label_info("ready", status_dict)

            fields_data = vin_data["flows"]["post-inventory"]["docs"]['invoice']['fields']
            
            report_data = vin_data["flows"]["post-inventory"]['report-data']

            fields_data['ro_date']['value'] = ""
            if str(report_data['svc_ro_date']).lower() !=  "nan":
                fields_data['ro_date']['value'] = report_data['svc_ro_date']
                something_changed = True

            mongo_vin_date = vin_data["flows"]["post-inventory"]['read_at']
            days_passed = (datetime.today() - mongo_vin_date).days
            if days_passed > 30:
                something_changed = True

            print("Have we change anything? ", something_changed)
            print("***** END PROCESSING VIN *******")
            
            if something_changed:
                
                bre_event = {
                    "document": {
                        "id": document_id,
                        "app_id": app_id,
                        "aria_status": actual_aria_status_key,
                        "groups": {
                            "invoice": {"fields": fields_data}
                        },
                        "ocr_groups": ["invoice"]
                    },
                    "action": {
                        "source_status": actual_aria_status_key,
                        "target_status": target_aria_status_key,
                        "action_id": "000000000",
                        "action_label": "reprocess"
                    },
                    "status": status_dict
                }
                    
                # Trigger the BRE to reprocess the folio
                try:
                    trigger_lambda(os.getenv('BRE_HANDLER_LAMBDA'), {'body': json.dumps(bre_event)})
                except Exception as e:
                    return {
                        'statusCode': 500,
                        'body': json.dumps({'message': 'Failed to trigger BRE: {}'.format(str(e))})
                    }
                
    def get_bols_to_reevaluate(self):

        print("************ REEVALUATING BOLS ************")

        bols_to_reevaluate = self.crud_bols.find_bols_by_filter({"status": "Needs Attention"})
        for bol_data in bols_to_reevaluate:

            document_id = bol_data['aria_wi_id']
            app_id = bol_data['aria_app_id']
            actual_status_label = bol_data['status']
            
            fields_data = bol_data['fields']
            
            status_dict = self.crud_statuses.get_statuses_by_app_id(app_id)['status']
            actual_aria_status_key, _ = self.get_status_label_info(actual_status_label.lower(), status_dict)
            target_aria_status_key, _ = self.get_status_label_info("complete", status_dict)
        
            bre_event = {
                "document": {
                    "id": document_id,
                    "app_id": app_id,
                    "aria_status": actual_aria_status_key,
                    "groups": {
                        "bol": {"fields": fields_data}
                    },
                    "ocr_groups": ["bol"]
                },
                "action": {
                    "source_status": actual_aria_status_key,
                    "target_status": target_aria_status_key,
                    "action_id": "000000000",
                    "action_label": "reprocess"
                },
                "status": status_dict
            }

            try:
                trigger_lambda(os.getenv('BRE_HANDLER_LAMBDA'), {'body': json.dumps(bre_event)})
            except Exception as e:
                return {
                    'statusCode': 500,
                    'body': json.dumps({'message': 'Failed to trigger BRE: {}'.format(str(e))})
                }

    def get_invoices_in_on_hold_used_cars(self):
        """
        Get all the invoices that have not a BOL or a title
        """
        vins_list = self.crud_vins.get_used_cars_vins_in_on_hold()

        print("************ REEVALUATING USED CARS INVOICES ************")

        for vin_data in vins_list:

            print("***** PROCESSING VIN *******")
            print(vin_data['vin'])
            print("***** ************** *******")

            vin = vin_data["vin"]
            something_changed = False

            document_id = vin_data["flows"]["used-cars"]["docs"]['invoice']['aria_data']['aria_wi_id']
            app_id = vin_data["flows"]["used-cars"]["docs"]['invoice']['aria_data']['aria_app_id']
            actual_status_label = vin_data["flows"]["used-cars"]["docs"]['invoice']['aria_data']['status']
            
            
            status_dict = self.crud_statuses.get_statuses_by_app_id(app_id)['status']
            actual_aria_status_key, _ = self.get_status_label_info(actual_status_label.lower(), status_dict)
            target_aria_status_key, _ = self.get_status_label_info("ready", status_dict)

            fields_data = vin_data["flows"]["used-cars"]["docs"]['invoice']['fields']
            report_data = vin_data["flows"]["used-cars"]['report-data']

            fields_data['transport_amount']['value'] = ""
            if str(report_data['memo']).lower() !=  "nan":
                fields_data['transport_amount']['value'] = report_data['memo']
                something_changed = True

            if fields_data['store']['value'] == "" and str(report_data['store']).lower() !=  "nan":
                something_changed = True

            mongo_vin_date = vin_data["flows"]["used-cars"]['read_at']
            days_passed = (datetime.today() - mongo_vin_date).days
            if days_passed > 30:
                something_changed = True

            print("Have we change anything? ", something_changed)
            print("***** END PROCESSING VIN *******")
            
            if something_changed:
                
                bre_event = {
                    "document": {
                        "id": document_id,
                        "app_id": app_id,
                        "aria_status": actual_aria_status_key,
                        "groups": {
                            "used_cars_invoices": {"fields": fields_data}
                        },
                        "ocr_groups": ["used_cars_invoices"]
                    },
                    "action": {
                        "source_status": actual_aria_status_key,
                        "target_status": target_aria_status_key,
                        "action_id": "000000000",
                        "action_label": "reprocess"
                    },
                    "status": status_dict
                }
                    
                # Trigger the BRE to reprocess the folio
                try:
                    trigger_lambda(os.getenv('BRE_HANDLER_LAMBDA'), {'body': json.dumps(bre_event)})
                except Exception as e:
                    return {
                        'statusCode': 500,
                        'body': json.dumps({'message': 'Failed to trigger BRE: {}'.format(str(e))})
                    }
                
    def run(self, stage, app):

        if stage == "post-inventory":
        
            if app == "titles":
                self.get_titles_not_in_stock()
            else:
                self.get_invoices_in_on_hold()
                self.get_bols_to_reevaluate()

        elif stage == "pre-inventory":
            pass
        elif stage == "used-cars":
            self.get_invoices_in_on_hold_used_cars()

def lambda_handler(event, context):

    print("Starting reevaluate lambda function")
    print(event)

    stage = event.get("stage")
    app = event.get("app", "")

    if stage is None or stage == "":
        return {
            'statusCode': 500,
            'body': {
                "message": json.dumps(f"Error no stage provide!")
            }
        }


    try:
        
        reevaluate = Reevaluate()
        reevaluate.run(stage, app)

        return {
            'statusCode': 200,
            'body': json.dumps({'message': 'All vins processed correctly.'})
        }
    
    except Exception as e:
        print("Exception -> ", traceback.format_exc())
        return {
            'statusCode': 500,
            'body': json.dumps({'message': 'Issue while processing the petition: ' + str(e)})
        }
    