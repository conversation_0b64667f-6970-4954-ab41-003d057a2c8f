# This module contains a class for interacting with the Emails collection in the MongoDB database.

import os
import datetime

from mongo_utils import Mongo
from boto3_utils import get_secret


class CrudReynolsReport:
    def __init__(self):
        mongo_uri = get_secret(os.environ['ENV'] + '-mongodb_uri', return_json=False)
        self.mongo = Mongo(mongo_uri)
        self.mongo.select_db_and_collection(
            db_name=os.environ['MONGO_DATABASE'],
            collection_name='vin'
        )
        self.store_path = "flows.post-inventory.report-data.store"
        self.downloaded_path = "flows.post-inventory.docs.invoice.downloaded_at"
        self.supported_stores = get_secret(os.environ['ENV'] + '-supported_stores', return_json=True)
    
    def get_vins_to_report(self):
        """
        This function retrieves all VINs that have not been downloaded and need to be reported.
        """
        query = {
            self.downloaded_path: {"$in": [None, ""]},
            'status': {'$in': [0, 1, 2]},
            "read_at": {"$lt": datetime.datetime.now() - datetime.timedelta(days=3)},
            self.store_path: {"$in": self.supported_stores}
        }

        return list(self.mongo.find(query))
    

    def get_vins_info_of_selected(self, vins):
        """
        This function retrieves all VINs that have not been downloaded and need to be reported.
        """
        query = {
            'vin': {"$in": vins}
        }

        return list(self.mongo.find(query))


    def __del__(self):
        self.mongo.close_connection()
