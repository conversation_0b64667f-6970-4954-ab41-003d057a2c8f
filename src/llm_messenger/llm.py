from openai import AzureOpenAI
import boto3
import os
from boto3_utils import get_secret, download_file_from_s3
import os
import json
import base64
import requests
from datetime import datetime
from openai import OpenAI

class LlmHandler:
    def __init__(self, provider, host, model, response_format):
        """
        Initialize LlmHandler with configurations for Azure OpenAI and Bedrock.
        """
        self.llm_params = get_secret(os.environ['SECRET_LLM_CREDENTIALS'], return_json=True)[provider][host][model]['llm_params']
        self.response_format = response_format

        if 'gpt' in model:
            if host == 'azure':
                self.client = AzureOpenAI(
                    azure_endpoint = self.llm_params['endpoint'], 
                    api_key = self.llm_params['api_key'],  
                    api_version = self.llm_params['api_version']
                )
            elif host == 'public':
                self.client = OpenAI(api_key=self.llm_params['api_key'])
            else:
                return 'Deployment not supported'
        elif self.llm_params['deployment'] == 'bedrock':
            self.client = boto3.client("bedrock-runtime", region_name=self.llm_params['region_name'])
        
        self.model = self.llm_params['model']
        self.engine = self.llm_params['engine']
        self.deployment = self.llm_params['deployment']
        self.prompt = "You are a nice assistant!"

    def send_message_llm(self, prompt, message, images = []):
        if 'gpt' in self.engine:
            if 'vision' in self.engine:
                print('------------Message to GPT Vision-----------')
                response = self.send_message_gpt_vision(prompt, message, images)
                print('------------GPT Vision output-----------')
                print(response)
                print('-----------------------')
                return response
            else:
                print('------------Message to GPT-----------')
                print(message)
                print('-----------------------')
                response = self.client.chat.completions.create(
                    model = self.model,
                    messages=[
                        {"role": "system", "content": prompt},
                        {"role": "user", "content": message}
                    ],
                    response_format = {"type": "json_object"} if self.response_format == "json" else None,
                    timeout = 120
                )
                print('------------GPT output-----------')
                print(response)
                print('-----------------------')
                return response.choices[0].message.content
        elif self.deployment == 'bedrock':
            print(f'------------Message to {self.engine}-----------')
            print(message)
            print('-----------------------')
            response = self.client.converse(
                modelId=self.model,
                messages= [
                    {"role": "user", "content": [{"text": message}]}
                ],
                system=[
                    {'text': prompt if prompt != "" else self.prompt},
                ],
                inferenceConfig={"maxTokens": 2000, "temperature": 0.7, "topP": 1},
            )
            print(f'------------{self.engine} output-----------')
            print(response)
            print('-----------------------')
            return response["output"]["message"]["content"][0]["text"]
        else:
            return 'Engine not supported'
    

    def encode_image_to_base64(self, image):
        with open(image, "rb") as img_file:
            return base64.b64encode(img_file.read()).decode('utf-8')

    def send_to_gpt_vision(self, prompt, message, images_encoding):
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.llm_params['api_key']}"
        }

        data_imgs = [
            {
                "type": "text",
                "text": message
            }
        ]

        for image_encoding in images_encoding:
            data_imgs.append(
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpeg;base64,{image_encoding}",
                        "detail": "high"
                    }
                }
            )

        payload = {
            "model": "gpt-4.1",
            "messages": [
                {
                    "role": "system",
                    "content": prompt
                },
                {
                    "role": "user",
                    "content": data_imgs
                }
            ],
            "max_tokens": 8000
        }

        print(payload)
        
        response = requests.post("https://api.openai.com/v1/chat/completions", headers=headers, json=payload)
        
        if response.status_code != 200:
            print(f"Error: API request failed with status code {response.status_code}")
            print(f"Response: {response.text}")
            raise Exception("API request failed")
            
        outputJson = response.json()
        
        if 'choices' not in outputJson:
            print(f"Unexpected API response format: {outputJson}")
            raise Exception("Invalid API response format")
            
        outputInfo = outputJson['choices'][0]['message']['content']
        return outputInfo
    
    def send_message_gpt_vision(self, prompt, message, s3_files):
        print("FILES ", s3_files)
        encoded_images = []
        for s3_file in s3_files:
            extension = s3_file.split(".")[-1]

            if extension not in ["png", "jpg", "jpeg"]:
                return "Extension not supported"
            
            file = f"/tmp/temp_file_{datetime.now().strftime('%Y%m%d%H%M%S%f')}.{extension}"
            download_file_from_s3(s3_file, file)

            encoded_image = self.encode_image_to_base64(file)
            encoded_images.append(encoded_image)

        print("B64 ", encoded_images)
        llm_response = self.send_to_gpt_vision(prompt, message, encoded_images)
        return llm_response
        

