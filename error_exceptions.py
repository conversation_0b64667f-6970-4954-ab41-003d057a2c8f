class ErrorAlreadyHandledException(Exception):
    """Exception that indicates an error has already been handled and notified"""
    def __init__(self, original_error, error_id=None):
        self.original_error = original_error
        self.error_id = error_id
        super().__init__(f"Error already handled: {original_error}")

class StoreLoginException(ErrorAlreadyHandledException):
    """Login error that has been handled at store level"""
    pass

class StoreProcessingException(ErrorAlreadyHandledException):
    """Processing error that has been handled at store level"""
    pass