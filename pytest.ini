[tool:pytest]
# Pytest configuration for Lambda microservices testing

# Test discovery
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test* *Test
python_functions = test_*

# Minimum version
minversion = 7.0

# Add options
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=hennessy-aria/src
    --cov=hennessy
    --cov=tagtitle/src
    --cov-report=html:htmlcov
    --cov-report=term-missing
    --cov-report=xml
    --cov-fail-under=80
    --html=reports/report.html
    --self-contained-html
    --json-report
    --json-report-file=reports/report.json

# Markers
markers =
    unit: Unit tests for individual functions/classes
    integration: Integration tests with external services
    slow: Tests that take a long time to run
    aws: Tests that interact with AWS services
    mongo: Tests that interact with MongoDB
    email: Tests related to email functionality
    s3: Tests related to S3 functionality
    secrets: Tests related to Secrets Manager
    lambda: Tests for Lambda functions
    smoke: Smoke tests for basic functionality
    regression: Regression tests
    skip_ci: Skip in CI environment

# Filtering
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:boto3.*
    ignore::UserWarning:botocore.*

# Test timeout (in seconds)
timeout = 300

# Parallel execution
# Use with: pytest -n auto
# Requires pytest-xdist

# Environment variables for testing
env =
    ENV = test
    AWS_DEFAULT_REGION = us-east-1
    MONGO_DATABASE = test_database
    REPORTS_EMAIL = <EMAIL>
    REPORTER_EMAIL = <EMAIL>
    BCC_EMAIL = <EMAIL>
    ARIA_ENV = test

# Log configuration
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

log_file = tests/logs/pytest.log
log_file_level = DEBUG
log_file_format = %(asctime)s [%(levelname)8s] %(filename)s:%(lineno)d %(funcName)s(): %(message)s
log_file_date_format = %Y-%m-%d %H:%M:%S
