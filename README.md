# Hennessy Tag and Title - Serverless Document Processing System

A serverless application built with the Serverless Framework for automated document processing, OCR extraction, and business rule validation. This system processes automotive documents (titles, bills of sale, driver licenses) using AI-powered extraction and validation workflows.

## Prerequisites

- Node.js (v14 or higher)
- npm or yarn
- AWS CLI configured with appropriate credentials
- Serverless Framework CLI (installed as project dependency)
- Python 3.11 (for Lambda functions)
- MongoDB (for data storage)

## Project Structure

```
hennessy-tag-and-title/
├── serverless.yml                      # Main serverless configuration
├── package.json                        # Node.js dependencies and scripts
├── .env.sample                         # Environment variables template
├── README.md                           # Project documentation
│
├── serverless-config/                  # Serverless Framework Configuration
│   ├── custom.yml                      # Custom variables and plugin settings
│   ├── layers.yml                      # Lambda layers (boto3, pymongo, openai)
│   ├── package.yml                     # Packaging configuration
│   ├── plugins.yml                     # Serverless plugins
│   ├── provider/                       # AWS Provider Configuration
│   │   ├── base.yml                    # Main provider settings, VPC, API Gateway
│   │   ├── environment.yml             # Environment variables for functions
│   │   └── vpc.yml                     # VPC configuration
│   └── resources/                      # AWS Resources Definitions
│       ├── functions.yml               # Lambda function configurations
│       └── roles/                      # IAM Role Definitions
│           ├── lambda_authorizer_role.yml
│           ├── bre_handler_role.yml
│           ├── llm_extractor.yml
│           ├── bre_role.yml
│           └── bre_validation_role.yml
│
├── src/                                # Python Source Code
│   ├── lambda_authorizer/              # API Gateway Authorization
│   │   └── lambda_function.py          # Token validation logic
│   │
│   ├── bre_handler/                    # Main Orchestrator (Entry Point)
│   │   └── lambda_function.py          # Request routing and workflow
│   │
│   ├── llm_extractor/                  # AI-Powered Document Extraction
│   │   └── lambda_function.py          # Multi-LLM extraction pipeline
│   │
│   ├── bre/                            # Business Rule Engine
│   │   └── lambda_function.py          # Intermediate validation logic
│   │
│   ├── bre_validation/                 # Final Validation & ARIA Integration
│   │   └── lambda_function.py          # Final validation and status updates
│   │
│   ├── common/                         # Shared Libraries & Utilities
│   │   ├── aria_helper/                # ARIA System Integration
│   │   │   ├── aria_utils.py           # ARIA API client
│   │   │   ├── mongo_utils.py          # MongoDB operations
│   │   │   ├── boto3_utils.py          # AWS services utilities
│   │   │   ├── crud_statuses.py        # Status management
│   │   │   └── crud_handler_execution.py # Execution tracking
│   │   │
│   │   ├── extraction_engine/          # Document Processing Engine
│   │   │   ├── strategy/               # Strategy Pattern Implementation
│   │   │   │   ├── extraction_engine_abs.py
│   │   │   │   ├── ocr_extraction_engine_abs.py
│   │   │   │   └── ocr/
│   │   │   │       ├── full_page_ocr_extraction_engine.py
│   │   │   │       └── expense_ocr_extraction_engine.py
│   │   │   ├── prompts.py              # LLM prompts for extraction
│   │   │   └── fields_to_extract.py    # Field definitions per document type
│   │   │
│   │   ├── llm_helper/                 # Multi-LLM Integration
│   │   │   ├── llm_factory.py          # Factory pattern for LLM creation
│   │   │   ├── llm_models.py           # LLM type definitions
│   │   │   └── llm/                    # LLM Provider Implementations
│   │   │       ├── base_model.py       # Abstract base class
│   │   │       ├── openai_model.py     # OpenAI/Azure integration
│   │   │       └── bedrock_model.py    # AWS Bedrock integration
│   │   │
│   │   └── validation_helper/          # Business Rule Validation
│   │       ├── validation_execution.py # Main validation orchestrator
│   │       └── core/
│   │           └── validation_engine.py # Core validation logic
│   │
│   └── seeder/                         # Database Setup & Configuration
│       ├── mongo_seeder.py             # Database seeding script
│       ├── action_config.json          # Action workflow configurations
│       ├── llm_config.json             # LLM model configurations
│       └── *.json                      # Various seed data files
│
├── layers/                             # Lambda Layer Artifacts
│   ├── boto3.zip                       # AWS SDK for Python
│   ├── pymongoAWS.zip                  # MongoDB driver
│   ├── requests.zip                    # HTTP library
│   ├── open_ai.zip                     # OpenAI Python SDK
│   └── pydantic.zip                    # Data validation library
│
└── assets/                             # Documentation Assets
    └── workflow.svg                    # Workflow diagram
```

# Architecture Overview

### Document Processing Workflow

The system follows a sophisticated branching workflow that handles different document states and processing requirements:

![Document Processing Workflow](assets/workflow.svg)



#### Core Components

| Component | Purpose | Key Dependencies |
|-----------|---------|------------------|
| **lambda_authorizer** | API Gateway security | `boto3_utils`, `get_secret` |
| **bre_handler** | Request orchestration | `mongo_utils`, `aria_utils`, `crud_*` |
| **llm_extractor** | AI document extraction | `extraction_engine`, `llm_helper` |
| **bre** | Business rule validation | `validation_helper`, `mongo_utils` |
| **bre_validation** | Final validation & ARIA updates | `validation_helper`, `aria_utils` |

#### Shared Libraries Architecture

**aria_helper/** - ARIA System Integration Layer
- Handles all external system communication
- Manages MongoDB connections and operations
- Provides AWS service utilities (Secrets Manager, Lambda triggers)
- Tracks document status and execution history

**extraction_engine/** - Document Processing Engine
- Implements Strategy pattern for different extraction methods
- Manages OCR data processing and field extraction
- Provides configurable prompts and field definitions
- Supports multiple document types (invoices, BOL, titles)

**llm_helper/** - Multi-LLM Integration Framework
- Factory pattern for creating different LLM providers
- Abstracts OpenAI, Azure OpenAI, and AWS Bedrock
- Enables easy switching between models and providers
- Handles model-specific configurations and parameters

**validation_helper/** - Business Rule Engine
- Configuration-driven validation system
- Supports field-level and group-level validation rules
- Provides extensible validation engine architecture
- Manages validation results and error reporting

#### Data Flow

1. **Configuration**: MongoDB stores validation rules, prompts, and action configs
2. **Processing**: Documents flow through extraction → validation → status updates
3. **Integration**: ARIA system receives updates and manages document lifecycle
4. **Monitoring**: Execution tracking and status history maintained in MongoDB

#### External Integrations

- **ARIA System**: Case management and workflow orchestration
- **MongoDB**: Configuration storage and document tracking
- **AWS Textract**: OCR data source (via S3 URLs)
- **OpenAI/Bedrock**: AI models for document extraction
- **AWS Secrets Manager**: Secure credential storage

## Lambda Functions Overview

This application consists of 5 main Lambda functions that work together in a document processing workflow:

### 1. **lambda_authorizer**
- **Purpose**: API Gateway authorizer for securing HTTP endpoints
- **Runtime**: Python 3.11
- **Memory**: 128 MB
- **Timeout**: 180 seconds
- **Functionality**: Validates Bearer tokens against stored valid tokens in AWS Secrets Manager

### 2. **bre_handler** (Entry Point)
- **Purpose**: Main orchestrator that receives HTTP requests and triggers the processing workflow
- **Runtime**: Python 3.11
- **Memory**: 256 MB
- **Timeout**: 180 seconds
- **API Endpoint**: `POST /{stage}-{project}-{process}/bre_handler`
- **Functionality**:
  - Receives document processing requests via HTTP API
  - Determines the next function to execute based on document type and action
  - Orchestrates the workflow by triggering appropriate Lambda functions
  - Handles first-time document processing and action-based workflows

### 3. **llm_extractor**
- **Purpose**: AI-powered document data extraction using OpenAI GPT models
- **Runtime**: Python 3.11
- **Memory**: 256 MB
- **Timeout**: 900 seconds (15 minutes)
- **Functionality**:
  - Downloads and processes OCR data from documents
  - Uses OpenAI GPT-4 for intelligent field extraction
  - Processes different document types (invoices, BOL, titles)
  - Implements retry logic for robust extraction
  - Updates document status in MongoDB and ARIA system

### 4. **bre** (Business Rule Engine)
- **Purpose**: Validates extracted data against business rules
- **Runtime**: Python 3.11
- **Memory**: 2048 MB
- **Timeout**: 900 seconds
- **Functionality**:
  - Performs validation of extracted document data
  - Applies business rules specific to document types
  - Returns validation results and any errors found
  - Prepares data for ARIA system updates

### 5. **bre_validation**
- **Purpose**: Final validation step before sending to DMS (Document Management System)
- **Runtime**: Python 3.11
- **Memory**: 256 MB
- **Timeout**: 900 seconds
- **Functionality**:
  - Performs final validation checks
  - Determines if document is ready for DMS
  - Updates document status to "ready_for_dms" or back to default status
  - Sends final updates to ARIA system

### Workflow Overview:

The diagram above shows the complete document processing pipeline with clear distinction between synchronous responses and asynchronous background processing:

#### **Synchronous Response Path:**
1. **Authentication & Orchestration**: All requests flow through `lambda_authorizer` → `bre_handler`
2. **Decision Point**: `bre_handler` checks if action name is present
3. **Immediate Response**: System returns response immediately while processing continues in background

#### **Two Main Processing Paths:**

**Path 1: No Action Name Present (First-Time Documents)**
- Checks if OCR data is available
- If OCR available: Triggers `llm_extractor` asynchronously and returns "Working on it" response
- If no OCR: Handles exception and updates status
- Background: `llm_extractor` → `bre_validation` → Final ARIA update

**Path 2: Action Name Present (User Actions)**
- Looks up action configuration (approve/reject/review)
- Routes based on BRE type:
  - **"post"**: Calls `bre` function and returns BRE response immediately
  - **"validation"**: Calls `bre_validation` function and updates ARIA system

#### **Key Features:**
- **Asynchronous Processing**: OCR extraction happens in background while user gets immediate response
- **Synchronous Actions**: User actions get immediate validation responses
- **Exception Handling**: Documents without OCR are handled gracefully
- **Status Management**: Final ARIA updates set documents to "ready_for_dms" or "manual review"

### Workflow Paths Explained:

#### **Path 1: First-Time Document Processing (New Documents)**
1. **Authentication**: `lambda_authorizer` validates Bearer token
2. **Orchestration**: `bre_handler` determines this is a new document (no action)
3. **OCR Check**: Verifies if OCR data is available
4. **AI Extraction**: `llm_extractor` processes document using multiple LLM models
5. **Final Validation**: `bre_validation` performs business rule validation
6. **Status Update**: Document marked as "ready_for_dms" or flagged for review

#### **Path 2: Action-Based Processing (User Actions)**
1. **Authentication**: `lambda_authorizer` validates Bearer token
2. **Action Processing**: `bre_handler` identifies user action (approve/reject/review)
3. **Config Lookup**: Determines next function based on action configuration
4. **Validation Type**: Routes to either `bre` (intermediate) or `bre_validation` (final)
5. **ARIA Integration**: Results sent back to ARIA system

#### **Path 3: Documents Without OCR**
1. **Authentication**: `lambda_authorizer` validates Bearer token
2. **OCR Check**: No OCR groups available
3. **Exception Handling**: Document marked with appropriate exception status
4. **ARIA Update**: Status updated for manual intervention

### Key Decision Points:

- **Action Present?**: Determines if this is first-time processing or user action
- **OCR Groups Available?**: Checks if document has processable OCR data
- **BRE Type?**: Routes to appropriate validation function based on action type
- **Document Valid?**: Final validation determines document readiness for DMS

## Common Folder Architecture

The `src/common/` directory contains shared utilities and business logic used across all Lambda functions:

### Directory Structure:
```
src/common/
├── aria_helper/                    # ARIA system integration utilities
│   ├── aria_utils.py              # ARIA API client for system communication
│   ├── boto3_utils.py             # AWS services utilities (Secrets, Lambda triggers)
│   ├── mongo_utils.py             # MongoDB connection and operations
│   ├── crud_statuses.py           # Status management in ARIA database
│   └── crud_handler_execution.py  # Execution tracking and logging
├── extraction_engine/             # Document processing and AI extraction
│   ├── strategy/                  # Strategy pattern for different extraction types
│   │   ├── extraction_engine_abs.py      # Abstract base for extraction engines
│   │   ├── ocr_extraction_engine_abs.py  # OCR-specific extraction interface
│   │   └── ocr/
│   │       ├── full_page_ocr_extraction_engine.py  # Main OCR extraction implementation
│   │       └── expense_ocr_extraction_engine.py    # Alternative extraction strategy
│   ├── prompts.py                 # LLM prompts for data extraction
│   └── fields_to_extract.py       # Field definitions and extraction schemas
├── llm_helper/                    # LLM integration and management
│   ├── llm_factory.py            # Factory pattern for LLM model creation
│   ├── llm_models.py             # Enum definitions for supported LLM types
│   └── llm/                      # Individual LLM implementations
│       ├── base_model.py         # Abstract base class for LLM models
│       ├── openai_model.py       # OpenAI/Azure OpenAI implementation
│       └── bedrock_model.py      # AWS Bedrock implementation
└── validation_helper/             # Business rule validation system
    ├── validation_execution.py   # Main validation orchestrator
    └── core/
        └── validation_engine.py  # Core validation logic and rule processing
```

### Key Components:

#### 1. **ARIA Helper (`aria_helper/`)**
- **Purpose**: Manages integration with the ARIA case management system
- **Key Files**:
  - `aria_utils.py`: HTTP client for ARIA API calls (BRE replies, event creation)
  - `mongo_utils.py`: MongoDB wrapper with connection management and CRUD operations
  - `boto3_utils.py`: AWS service utilities (Secrets Manager, Lambda invocation)
  - `crud_statuses.py`: Manages document status tracking in ARIA database

#### 2. **Extraction Engine (`extraction_engine/`)**
- **Purpose**: Handles document processing and AI-powered data extraction
- **Architecture**: Uses Strategy pattern for different extraction approaches
- **Key Components**:
  - `FullPageOCRExtractionEngine`: Main extraction implementation using multiple LLM models
  - `prompts.py`: Contains extraction prompts optimized for document processing
  - `fields_to_extract.py`: Defines extractable fields per document type

#### 3. **LLM Helper (`llm_helper/`)**
- **Purpose**: Provides abstraction layer for multiple LLM providers
- **Architecture**: Factory pattern with provider-specific implementations
- **Supported Models**:
  - OpenAI GPT-4 (primary extraction)
  - AWS Bedrock Claude (validation)
  - Azure OpenAI (alternative deployment)

#### 4. **Validation Helper (`validation_helper/`)**
- **Purpose**: Implements business rule validation engine
- **Features**: Configurable validation rules, field-level and group-level validation

## How LLM Extraction Works

The `llm_extractor` Lambda function implements a sophisticated multi-model AI extraction pipeline:

### 1. **OCR Data Processing**
```python
# Downloads OCR data from provided URL
def get_ocr_data(self, link_to_download):
    r = requests.get(link_to_download)
    ocr_data = json.loads(r.content.decode('utf-8'))
    # Processes AWS Textract output into structured format
    # Maps word IDs to coordinates and page numbers
    # Organizes text by pages for processing
```

### 2. **Multi-Model LLM Strategy**
The extraction engine uses multiple LLM models for accuracy and validation:

```python
def get_used_llm_dict(self):
    return {
        "gpt": OpenAI GPT-4 (primary extraction),
        "gpt_mini": OpenAI GPT-4-mini (lightweight tasks),
        "aux1": AWS Bedrock Claude 3.7 Sonnet (validation),
        "aux2": AWS Bedrock Claude 3.5 Sonnet (cross-validation)
    }
```

### 3. **Extraction Process Flow**

#### Step 1: Primary Extraction with GPT-4
- Uses optimized prompts for document field extraction
- Processes OCR text with word IDs for coordinate mapping
- Extracts fields based on document type (invoice, BOL, title)
- Returns structured JSON with field values and coordinates

#### Step 2: Cross-Validation with Claude Models
```python
def recursive_validation(self, message_without_ids, execution_id):
    # Calls Claude 3 Haiku for independent extraction
    claude_output = self.llms_wapper_map['aux1'].send_message_to_llm(...)

    # Calls Claude 3.5 Sonnet for additional validation
    llama3_output = self.llms_wapper_map['aux2'].send_message_to_llm(...)

    # Compares results across models for accuracy
    return claude_output, llama3_output
```

#### Step 3: Field Validation and Correction
```python
def validate_extracted_data(self, gpt_output, claude_output, llama3_output):
    # Compares field values across different LLM outputs
    # Identifies discrepancies and flags uncertain fields
    # Returns lists of validated and problematic fields
    fields_ok, fields_nok = self.compare_extractions(...)

    # Re-extracts problematic fields using additional context
    if fields_nok:
        gpt_output2 = self.retry_extraction_with_context(...)
```

### 4. **Coordinate Extraction**
For each extracted field, the system:
- Identifies word IDs from OCR text that contain the field value
- Maps word IDs to document coordinates (x, y, width, height)
- Uses additional LLM call to determine precise field boundaries
- Stores coordinates for UI highlighting and validation

### 5. **Document Type Handling**
Different extraction schemas for different document types:
- **Invoices**: VIN, amounts, dates, dealer information
- **Bills of Sale**: Vehicle details, buyer/seller information, transaction data
- **Titles**: Title numbers, vehicle identification, ownership details

### 6. **Error Handling and Retry Logic**
```python
def lambda_handler(event, context):
    retrys = int(os.getenv("RETRYS", 3))
    finished_by_no_problem_with_llm = False

    while (finished_by_no_problem_with_llm == False and retrys > 0):
        response = llm_extractor(event)
        if response['statusCode'] != 404:
            finished_by_no_problem_with_llm = True
        retrys -= 1
```

### 7. **Output Format**
The extraction produces structured data for BRE processing:
```json
{
  "groups": {
    "document_type": {
      "fields": {
        "field_name": {
          "value": "extracted_value",
          "coordinates": {
            "x": 0.123,
            "y": 0.456,
            "width": 0.078,
            "height": 0.023
          }
        }
      }
    }
  }
}
```

## How BRE Validation Works

The Business Rule Engine (BRE) validation system implements a sophisticated rule-based validation framework:

### 1. **Validation Architecture**

#### Core Components:
- **`ValidatorExecution`**: Main orchestrator that manages the validation process
- **`ValidationEngine`**: Core engine that processes individual validation rules
- **Configuration-driven**: Rules defined in MongoDB collections, not hardcoded

### 2. **Validation Process Flow**

#### Step 1: Configuration Loading
```python
def __init__(self, event):
    # Load validation configuration from MongoDB
    self.validation_config = self.mongo_client[database_name]["validation_config"].find_one({
        "app_id": self.app_id
    })

    # Load document statuses for workflow management
    self.statuses = self.mongo_client[aria_database]["aria_status"].find_one({
        "app_id": self.app_id
    })
```

#### Step 2: Multi-Level Validation
```python
def validate_data(self, data: dict, config: Dict) -> Tuple[bool, dict]:
    all_valid = True

    # 1. Group-level validations (cross-field business rules)
    group_level_rules = self._find_validation_rules(config_data, is_group_level=True)
    for rule in group_level_rules:
        rule_errors = self.validation_engine.validate_field(data, field_config, ...)
        if rule_errors:
            all_valid = False

    # 2. Field-level validations (individual field rules)
    for group_name, group_data in groups.items():
        for field_name, field_data in fields.items():
            # Apply field-specific validation rules
            field_errors = self.validate_individual_field(...)
```

### 3. **Validation Rule Types**

#### Field-Level Rules:
- **Required Field Validation**: Ensures mandatory fields are present
- **Format Validation**: Validates field formats (dates, numbers, patterns)
- **Range Validation**: Checks numeric ranges and date boundaries
- **Pattern Matching**: Regex-based validation for specific formats

#### Group-Level Rules:
- **Cross-Field Dependencies**: Validates relationships between fields
- **Business Logic Rules**: Complex business rules spanning multiple fields
- **Document Consistency**: Ensures document-wide data consistency

### 4. **BRE vs BRE Validation Differences**

#### **`bre` Function** (Intermediate Validation):
```python
def run(self):
    # Performs validation and returns results without status changes
    is_valid, validation_result = self.validator_execution.validate_data(...)

    aria_update_request = {k: v["fields"] for k, v in validation_result["groups"].items()}
    if is_valid:
        aria_update_request["aria_exception"] = {"value": ""}
    else:
        aria_update_request["aria_exception"] = {"value": validation_result.get('errors', "")}
        aria_update_request["aria_status"] = {"value": self.current_status_id}

    # Returns validation results for further processing
    return {'statusCode': 200, 'body': json.dumps(aria_update_request)}
```

#### **`bre_validation` Function** (Final Validation):
```python
def run(self):
    # Performs final validation and updates document status
    is_valid, validation_result = self.validator_execution.validate_data(...)

    if is_valid:
        # Document ready for DMS
        next_status_id = [k for k, v in self.statuses.items() if v["key"] == "ready_for_dms"]
        validation_errors = ""
    else:
        # Return to default status for review
        next_status_id = [k for k,v in self.statuses.items() if v["default"] == True]
        validation_errors = validation_result.get('errors', [])

    # Send final update to ARIA system
    self.post_to_aria(aria_update_request)
    return {'statusCode': 200, 'body': aria_update_request}
```

### 5. **Validation Configuration Structure**

Validation rules are stored in MongoDB with this structure:
```json
{
  "app_id": "document_type_id",
  "validation_rules": [
    {
      "id": "rule_001",
      "field": "vin_number",
      "type": "required",
      "groups": ["title", "invoice"],
      "error_message": "VIN number is required"
    },
    {
      "id": "rule_002",
      "field": "sale_date",
      "type": "date_format",
      "pattern": "MM/DD/YYYY",
      "groups": ["bill_of_sale"]
    },
    {
      "id": "rule_003",
      "type": "cross_field",
      "groups": [],
      "logic": "sale_amount > 0 AND sale_date <= today",
      "error_message": "Invalid sale transaction"
    }
  ]
}
```

### 6. **Error Handling and Status Management**

#### Status Flow:
1. **Initial Processing**: Document enters with default status
2. **Validation Success**: Status updated to "ready_for_dms"
3. **Validation Failure**: Status remains at default for manual review
4. **Exception Handling**: Errors logged with specific rule violations

#### Error Reporting:
- Field-level errors: Specific field validation failures
- Group-level errors: Business rule violations
- System errors: Technical issues during validation
- All errors tracked in MongoDB for audit and debugging

## Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

3. Set up environment variables:
   ```bash
   cp .env.sample .env
   ```
   Edit the `.env` file with your specific configuration values.

4. Build Lambda layers (if needed):
   ```bash
   # Layers should be pre-built and available in the layers/ directory
   # boto3.zip, pymongoAWS.zip, requests.zip, open_ai.zip, pydantic.zip
   ```

## Environment Configuration

Environment variables are managed through the `serverless-dotenv-plugin`. The application uses the `.env.sample` file as a template.

### Required Environment Variables

Copy from `.env.sample` and configure:
```bash
# AWS Configuration
REGION=us-east-1
DEPLOYMENT_BUCKET_NAME=your-deployment-bucket

# VPC Configuration (for secure database access)
SECURITY_GROUP_ID_1=sg-xxxxxxxxx
SECURITY_GROUP_ID_2=sg-xxxxxxxxx
PRIVATE_SUBNET_1=subnet-xxxxxxxxx
PRIVATE_SUBNET_2=subnet-xxxxxxxxx

# Application Configuration
PROJECT_NAME=hen
PROCESS_NAME=tag-and-title
DATABASE_NAME=your-database-name
MONGO_DATABASE=document_processing
ARIA_ENVIRONMENT=dev|staging|prod

# Additional Configuration
XRAY_ENABLED=true
```

## Deployment

### Available Scripts

```bash
# Package for SND (Sandbox) environment
npm run package:snd

# Deploy to SND environment
npm run deploy:snd
```

These scripts are defined in `package.json` and handle the serverless deployment process with the appropriate stage configuration.

## Lambda Layers

The application uses several Lambda layers to optimize deployment size and share common dependencies:

- **Boto3Layer**: AWS SDK for Python
- **PyMongoAWSLayer**: MongoDB driver for Python
- **RequestsLayer**: HTTP library for Python
- **OpenAILayer**: OpenAI Python SDK for GPT integration
- **PydanticLayer**: Data validation library

Layers are pre-built and stored as ZIP files in the `layers/` directory.

## AWS Resources Created

When deployed, the serverless application creates the following AWS resources:

### Lambda Functions
- `{stage}-{project}-{process}-lambda_authorizer`
- `{stage}-{project}-{process}-bre_handler`
- `{stage}-{project}-{process}-llm_extractor`
- `{stage}-{project}-{process}-bre`
- `{stage}-{project}-{process}-bre_validation`

### IAM Roles
- `{stage}-{project}-{process}-lambda_authorizer` (with Secrets Manager access)
- `{stage}-{project}-{process}-bre_handler` (with Lambda invoke permissions)
- `{stage}-{project}-{process}-llm_extractor` (with comprehensive AWS access)
- `{stage}-{project}-{process}-bre` (with MongoDB and ARIA access)
- `{stage}-{project}-{process}-bre_validation` (with validation permissions)

### API Gateway
- HTTP API with Lambda authorizer
- Endpoint: `POST /{stage}-{project}-{process}/bre_handler`
- Authorization via Bearer token

### Lambda Layers
- 5 shared layers for common dependencies

## Configuration Files

### Serverless Configuration Structure

The serverless configuration is modularized across multiple files:

- **`serverless.yml`**: Main configuration file that imports other configurations
- **`serverless-config/provider/base.yml`**: AWS provider settings, VPC, and API Gateway
- **`serverless-config/resources/functions.yml`**: Lambda function definitions
- **`serverless-config/resources/roles/`**: IAM role definitions for each function
- **`serverless-config/custom.yml`**: Custom variables and plugin settings
- **`serverless-config/layers.yml`**: Lambda layer definitions
- **`serverless-config/plugins.yml`**: Serverless plugins configuration

### Key Configuration Features

1. **VPC Integration**: Functions run within VPC for secure database access
2. **Individual Packaging**: Each function is packaged individually for optimization
3. **Environment Variables**: Managed through dotenv plugin
4. **X-Ray Tracing**: Enabled for API Gateway monitoring
5. **Deployment Bucket**: Uses specified S3 bucket for deployments

## Database Setup

The application requires MongoDB for storing:
- Document processing configurations
- Validation rules
- Processing logs and status
- LLM extraction results

### Seeding the Database

Use the provided seeder script to populate initial data:

```bash
cd src/seeder
python mongo_seeder.py
```

The seeder populates these collections:
- `validation_config`: Document validation rules
- `prompts`: LLM extraction prompts
- `Workitem`: Work item configurations
- `Wordblock`: Word block definitions
- `action_config`: Action workflow configurations
- `llm_config`: LLM model configurations

## Monitoring and Logging

### CloudWatch Logs
Each Lambda function creates its own log group:
- `/aws/lambda/{stage}-{project}-{process}-{function-name}`

### X-Ray Tracing
API Gateway tracing is enabled for request monitoring and debugging.

### Error Handling
- Comprehensive error handling in each Lambda function
- Retry logic implemented in LLM extractor
- Status tracking in MongoDB for audit trails

## Security

### Authentication
- API Gateway uses Lambda authorizer for request validation
- Bearer tokens stored in AWS Secrets Manager
- Token validation with configurable TTL (300 seconds)

### Network Security
- Lambda functions run in private VPC subnets
- Security groups control network access
- No direct internet access for enhanced security

### Secrets Management
- MongoDB connection strings stored in Secrets Manager
- API tokens and credentials managed through AWS Secrets Manager
- Environment-specific secret naming convention

## Troubleshooting

### Common Issues

1. **Deployment Failures**
   - Ensure all environment variables are set correctly
   - Verify AWS credentials and permissions
   - Check that deployment bucket exists and is accessible

2. **Lambda Function Errors**
   - Check CloudWatch logs for detailed error messages
   - Verify MongoDB connectivity and credentials
   - Ensure all required secrets exist in Secrets Manager

3. **API Gateway Issues**
   - Verify Bearer token is valid and exists in Secrets Manager
   - Check API Gateway logs for authorization failures
   - Ensure correct endpoint URL format

### Debugging Commands

```bash
# View function logs
npx serverless logs -f bre_handler --stage dev

# Invoke function locally (if configured)
npx serverless invoke local -f bre_handler --data '{"body": {...}}'

# Remove deployment (cleanup)
npx serverless remove --stage dev
```

## Development Workflow

1. **Local Development**: Use LocalStack for local testing (configured in plugins)
2. **Testing**: Deploy to development stage for integration testing
3. **Staging**: Deploy to staging for pre-production validation
4. **Production**: Deploy to production with appropriate environment variables

## Support

For issues and questions:
1. Check CloudWatch logs for error details
2. Verify all environment variables and secrets are configured
3. Ensure MongoDB connectivity and data seeding is complete
4. Review IAM permissions for each Lambda function role