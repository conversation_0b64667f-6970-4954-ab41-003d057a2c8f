Resources:
  ProcessingInvoicesWorkflowRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: ${self:provider.stage}-process_invoices_workflow
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - states.amazonaws.com
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      Policies:
        - PolicyName: ProcessingInvoicesWorkflowPolicy
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Sid: StepfunctionsPermissions
                Effect: Allow
                Action:
                  - lambda:InvokeFunction
                Resource:
                  - arn:aws:lambda:us-east-1:*:function:${self:provider.stage}-invoice_downloader
                  - arn:aws:lambda:us-east-1:*:function:${self:provider.stage}-invoice_downloader:*
                  - arn:aws:lambda:us-east-1:*:function:${self:provider.stage}-invoice_to_aria
                  - arn:aws:lambda:us-east-1:*:function:${self:provider.stage}-invoice_to_aria:*
                  - arn:aws:lambda:us-east-1:*:function:${self:provider.stage}-loading_wi_report
                  - arn:aws:lambda:us-east-1:*:function:${self:provider.stage}-loading_wi_report:*                  
                  - arn:aws:lambda:us-east-1:*:function:${self:provider.stage}-report_loaded_data
                  - arn:aws:lambda:us-east-1:*:function:${self:provider.stage}-report_loaded_data:*
                  - arn:aws:lambda:us-east-1:*:function:${self:provider.stage}-send_email_error
                  - arn:aws:lambda:us-east-1:*:function:${self:provider.stage}-send_email_error:*
              - Sid: CloudWatchLogsFullAccess
                Effect: "Allow"
                Action: 
                  - logs:*
                  - cloudwatch:GenerateQuery
                
                Resource: 
                  - "*"

              - Sid: StepFunctionsExecutionPermissions
                Effect: Allow
                Action:
                  - 'states:CreateStateMachine'
                  - 'states:DeleteStateMachine'
                  - 'states:UpdateStateMachine'
                  - 'states:TagResource'
                  - 'states:CreateActivity'
                  - 'states:DeleteActivity'
                  - 'states:DescribeActivity'
                  - 'states:StartExecution'
                  - 'states:DescribeStateMachine'
                  - 'states:DescribeExecution'
                  - 'states:StopExecution'
                  - 'states:UpdateStateMachine'
                  - 'events:PutRule'
                  - 'events:PutTargets'
                  - 'events:DeleteRule'
                  - 'events:RemoveTargets'
                  - 'iam:PassRole'
                Resource: '*'

              - Sid: EC2Permissions
                Effect: Allow
                Action:
                  - 'ec2:DescribeNetworkInterfaces'
                  - 'ec2:DescribeSubnets'
                  - 'ec2:DescribeVpcs'
                Resource: '*'
