Resources:
  ErrorWiReportRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: ${self:provider.stage}-error_wi_report
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Action: sts:AssumeRole
      Policies:
        - PolicyName: HENErrorWiReportPolicy
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action:
                  - ssm:GetParameter
                Resource: "*"
              # S3 permissions
              - Effect: Allow
                Action:
                  - s3:PutObject
                  - s3:GetObject
                  - s3:ListBucket
                  - s3:DeleteObject
                  - s3:*
                  - s3-object-lambda:*
                Resource:
                  - "arn:aws:s3:::${self:provider.stage}-bucket/"
                  - "arn:aws:s3:::${self:provider.stage}-bucket/*"
              # Logs permissions
              - Effect: Allow
                Action:
                  - logs:CreateLogGroup
                Resource: "arn:aws:logs:*:*:*"
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                Resource:
                  - "arn:aws:logs:*:*:log-group:/aws/lambda/${self:provider.stage}-error_wi_report:*"
              # Secrets Manager permissions
              - Effect: Allow
                Action:
                  - secretsmanager:GetSecretValue
                  - secretsmanager:ListSecrets
                Resource: "*"
              # Systems Manager permissions
              - Effect: Allow
                Action:
                  - ssm:GetParameter
                Resource: "*"
              # EC2 permissions (for VPC connectivity)
              - Effect: Allow
                Action:
                  - ec2:DescribeNetworkInterfaces
                  - ec2:CreateNetworkInterface
                  - ec2:DeleteNetworkInterface
                Resource: "*"
              # KMS permissions (for encrypted resources)
              - Effect: Allow
                Action:
                  - kms:DescribeKey
                  - kms:ListAliases
                  - kms:ListKeys
                Resource: "*"
              # Lambda permissions (if this Lambda interacts with other Lambdas)
              - Effect: Allow
                Action:
                  - lambda:AddPermission
                  - lambda:CreateFunction
                  - lambda:GetFunction
                  - lambda:InvokeFunction
                  - lambda:UpdateFunctionConfiguration
                Resource:
                  - "arn:aws:lambda:*:*:function:*"
              # CloudFormation permissions
              - Effect: Allow
                Action:
                  - cloudformation:DescribeStacks
                  - cloudformation:CreateChangeSet
                  - cloudformation:DescribeChangeSet
                  - cloudformation:ExecuteChangeSet
                Resource: "*"
              # Additional AWS services or actions
              - Effect: Allow
                Action:
                  - rds:DescribeDBClusters
                  - rds:DescribeDBInstances
                  - redshift:DescribeClusters
                Resource: "*"
              - Effect: "Allow"
                Action: 
                  - logs:*
                  - cloudwatch:GenerateQuery
                Resource: 
                  - "*"