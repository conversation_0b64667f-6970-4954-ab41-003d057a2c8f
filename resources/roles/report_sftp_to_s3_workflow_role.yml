Resources:
  ProcessingReportWorkflowRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: ${self:provider.stage}-report_sftp_to_s3_workflow
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - states.amazonaws.com
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      Policies:
        - PolicyName: HENProcessingReportWorkflowPolicy
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Sid: StepfunctionsPermissions
                Effect: Allow
                Action:
                  - lambda:InvokeFunction
                Resource:
                  - arn:aws:lambda:us-east-1:*:function:${self:provider.stage}-report_sftp_to_s3
                  - arn:aws:lambda:us-east-1:*:function:${self:provider.stage}-report_sftp_to_s3:*
                  - arn:aws:lambda:us-east-1:*:function:${self:provider.stage}-send_email_error
                  - arn:aws:lambda:us-east-1:*:function:${self:provider.stage}-send_email_error:*
              - Sid: CloudWatchLogsFullAccess
                Effect: "Allow"
                Action: 
                  - logs:*
                  - cloudwatch:GenerateQuery
                
                Resource: 
                  - "*"

              - Sid: EC2Permissions
                Effect: Allow
                Action:
                  - 'ec2:DescribeNetworkInterfaces'
                  - 'ec2:DescribeSubnets'
                  - 'ec2:DescribeVpcs'
                Resource: '*'
