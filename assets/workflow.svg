<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .box { fill: #f8f9fa; stroke: #343a40; stroke-width: 2; }
      .decision { fill: #fff3cd; stroke: #856404; stroke-width: 2; }
      .process { fill: #d1ecf1; stroke: #0c5460; stroke-width: 2; }
      .async { fill: #e7f3ff; stroke: #0066cc; stroke-width: 2; stroke-dasharray: 5,5; }
      .endpoint { fill: #d4edda; stroke: #155724; stroke-width: 2; }
      .response { fill: #f0f8ff; stroke: #4169e1; stroke-width: 2; }
      .error { fill: #f8d7da; stroke: #721c24; stroke-width: 2; }
      .text { font-family: Arial, sans-serif; font-size: 12px; text-anchor: middle; }
      .small-text { font-family: Arial, sans-serif; font-size: 10px; text-anchor: middle; }
      .arrow { stroke: #343a40; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .async-arrow { stroke: #0066cc; stroke-width: 2; fill: none; marker-end: url(#arrowhead); stroke-dasharray: 5,5; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#343a40" />
    </marker>
  </defs>
  
  <!-- HTTP Request -->
  <rect x="520" y="20" width="120" height="40" class="box" />
  <text x="580" y="45" class="text">HTTP Request</text>
  
  <!-- lambda_authorizer -->
  <rect x="500" y="100" width="160" height="40" class="process" />
  <text x="580" y="125" class="text">lambda_authorizer</text>
  
  <!-- bre_handler -->
  <rect x="520" y="180" width="120" height="40" class="process" />
  <text x="580" y="205" class="text">bre_handler</text>
  
  <!-- Action Name Present Decision -->
  <polygon points="580,260 630,290 580,320 530,290" class="decision" />
  <text x="580" y="290" class="text">Action Name</text>
  <text x="580" y="305" class="text">Present?</text>
  
  <!-- NO ACTION PATH (LEFT SIDE) -->
  <!-- OCR Check -->
  <polygon points="350,380 400,410 350,440 300,410" class="decision" />
  <text x="350" y="415" class="text">OCR Available?</text>
  
  <!-- Trigger llm_extractor (Async) -->
  <rect x="250" y="500" width="160" height="40" class="async" />
  <text x="330" y="520" class="text">Trigger llm_extractor</text>
  <text x="330" y="535" class="small-text">(Async)</text>
  
  <!-- Return Response -->
  <rect x="250" y="580" width="160" height="40" class="response" />
  <text x="330" y="600" class="text">Return "Working on it"</text>
  <text x="330" y="615" class="small-text">Response</text>
  
  <!-- ACTION PRESENT PATH (RIGHT SIDE) -->
  <!-- Action Config Lookup -->
  <rect x="750" y="380" width="140" height="40" class="process" />
  <text x="820" y="405" class="text">Action Config Lookup</text>
  
  <!-- BRE Type Decision -->
  <polygon points="820,460 870,490 820,520 770,490" class="decision" />
  <text x="820" y="495" class="text">BRE Type?</text>
  
  <!-- bre (returns response) -->
  <rect x="950" y="500" width="120" height="40" class="process" />
  <text x="1010" y="525" class="text">bre</text>
  
  <!-- Return BRE Response -->
  <rect x="950" y="580" width="120" height="40" class="response" />
  <text x="1010" y="600" class="text">Return BRE</text>
  <text x="1010" y="615" class="small-text">Response</text>

  <!-- ASYNC PROCESSING (BACKGROUND) -->
  <!-- llm_extractor (background) -->
  <rect x="50" y="680" width="160" height="40" class="async" />
  <text x="130" y="700" class="text">llm_extractor</text>
  <text x="130" y="715" class="small-text">(Background Process)</text>
  
  <!-- bre_validation (background) -->
  <rect x="250" y="680" width="160" height="40" class="async" />
  <text x="330" y="700" class="text">bre_validation</text>
  <text x="330" y="715" class="small-text">(Background Process)</text>
  
  <!-- Final ARIA Update -->
  <rect x="450" y="680" width="160" height="40" class="endpoint" />
  <text x="530" y="700" class="text">Final ARIA Update</text>
  <text x="530" y="715" class="small-text">(ready_for_dms or review)</text>
  
  <!-- Exception Path -->
  <rect x="80" y="500" width="140" height="40" class="error" />
  <text x="150" y="520" class="text">No OCR</text>
  <text x="150" y="535" class="small-text">Exception Status</text>
  
  <!-- ARROWS -->
  <!-- Main flow -->
  <line x1="580" y1="60" x2="580" y2="100" class="arrow" />
  <line x1="580" y1="140" x2="580" y2="180" class="arrow" />
  <line x1="580" y1="220" x2="580" y2="260" class="arrow" />
  
  <!-- No Action Path -->
  <line x1="550" y1="290" x2="380" y2="380" class="arrow" />
  <text x="450" y="340" class="text" style="font-size: 10px;">NO</text>
  
  <!-- Action Present Path -->
  <line x1="610" y1="290" x2="780" y2="380" class="arrow" />
  <text x="710" y="340" class="text" style="font-size: 10px;">YES</text>
  
  <!-- OCR Available -->
  <line x1="370" y1="430" x2="350" y2="500" class="arrow" />
  <text x="370" y="470" class="text" style="font-size: 10px;">YES</text>
  
  <!-- No OCR -->
  <line x1="320" y1="410" x2="180" y2="500" class="arrow" />
  <text x="240" y="460" class="text" style="font-size: 10px;">NO</text>
  
  <!-- Async trigger to response -->
  <line x1="330" y1="540" x2="330" y2="580" class="arrow" />
  
  <!-- Action Config to BRE Type -->
  <line x1="820" y1="420" x2="820" y2="460" class="arrow" />
  
  <!-- BRE Type to bre -->
  <line x1="850" y1="490" x2="980" y2="500" class="arrow" />
  <text x="920" y="485" class="text" style="font-size: 10px;">post</text>
  
  <!-- bre to response -->
  <line x1="1010" y1="540" x2="1010" y2="580" class="arrow" />
  
  <!-- Background async flow -->
  <line x1="130" y1="720" x2="250" y2="720" class="async-arrow" />
  <line x1="330" y1="720" x2="450" y2="720" class="async-arrow" />
  
  <!-- Labels for clarity -->
  <text x="100" y="650" class="text" style="font-size: 14px; font-weight: bold;">ASYNC BACKGROUND PROCESSING:</text>
  <text x="600" y="350" class="text" style="font-size: 14px; font-weight: bold;">SYNCHRONOUS RESPONSE:</text>
  
</svg>
