# Makefile for Lambda microservices testing

.PHONY: help install test test-unit test-integration test-coverage clean lint format setup-dev

# Default target
help:
	@echo "Available targets:"
	@echo "  install         - Install testing dependencies"
	@echo "  test           - Run all tests"
	@echo "  test-unit      - Run unit tests only"
	@echo "  test-integration - Run integration tests only"
	@echo "  test-coverage  - Run tests with coverage report"
	@echo "  test-parallel  - Run tests in parallel"
	@echo "  lint           - Run code linting"
	@echo "  format         - Format code"
	@echo "  clean          - Clean up test artifacts"
	@echo "  setup-dev      - Set up development environment"

# Install testing dependencies
install:
	pip install -r tests/requirements.txt

# Run all tests
test:
	pytest

# Run unit tests only
test-unit:
	pytest -m unit

# Run integration tests only
test-integration:
	pytest -m integration

# Run tests with coverage
test-coverage:
	pytest --cov-report=html --cov-report=term

# Run tests in parallel
test-parallel:
	pytest -n auto

# Run specific test file
test-file:
	@if [ -z "$(FILE)" ]; then \
		echo "Usage: make test-file FILE=path/to/test_file.py"; \
	else \
		pytest $(FILE); \
	fi

# Run tests for specific Lambda function
test-lambda:
	@if [ -z "$(LAMBDA)" ]; then \
		echo "Usage: make test-lambda LAMBDA=function_name"; \
	else \
		pytest tests/unit/test_$(LAMBDA).py -v; \
	fi

# Run tests with specific marker
test-marker:
	@if [ -z "$(MARKER)" ]; then \
		echo "Usage: make test-marker MARKER=marker_name"; \
	else \
		pytest -m $(MARKER); \
	fi

# Run slow tests
test-slow:
	pytest -m slow

# Run AWS-related tests
test-aws:
	pytest -m aws

# Run MongoDB-related tests
test-mongo:
	pytest -m mongo

# Generate test report
test-report:
	pytest --html=reports/report.html --self-contained-html

# Run linting
lint:
	@echo "Running flake8..."
	flake8 hennessy-aria/src hennessy tagtitle/src tests --max-line-length=120
	@echo "Running pylint..."
	pylint hennessy-aria/src hennessy tagtitle/src tests --disable=C0114,C0115,C0116

# Format code
format:
	@echo "Running black..."
	black hennessy-aria/src hennessy tagtitle/src tests --line-length=120
	@echo "Running isort..."
	isort hennessy-aria/src hennessy tagtitle/src tests

# Clean up test artifacts
clean:
	rm -rf htmlcov/
	rm -rf reports/
	rm -rf .pytest_cache/
	rm -rf tests/__pycache__/
	rm -rf tests/*/__pycache__/
	rm -rf tests/*/*/__pycache__/
	rm -f .coverage
	rm -f tests/logs/*.log
	find . -name "*.pyc" -delete
	find . -name "__pycache__" -type d -exec rm -rf {} +

# Set up development environment
setup-dev: install
	@echo "Setting up development environment..."
	mkdir -p tests/logs
	mkdir -p reports
	mkdir -p htmlcov
	@echo "Development environment ready!"

# Watch for changes and run tests
test-watch:
	@echo "Watching for changes... (requires pytest-watch)"
	ptw --runner "pytest -x"

# Run tests with debugging
test-debug:
	pytest -s -vv --tb=long

# Run tests and open coverage report
test-coverage-open: test-coverage
	@if command -v open >/dev/null 2>&1; then \
		open htmlcov/index.html; \
	elif command -v xdg-open >/dev/null 2>&1; then \
		xdg-open htmlcov/index.html; \
	else \
		echo "Coverage report generated at htmlcov/index.html"; \
	fi

# Validate test configuration
validate-config:
	pytest --collect-only

# Run security checks
security-check:
	@echo "Running bandit security check..."
	bandit -r hennessy-aria/src hennessy tagtitle/src

# Run type checking
type-check:
	@echo "Running mypy type checking..."
	mypy hennessy-aria/src hennessy tagtitle/src --ignore-missing-imports

# Full quality check
quality-check: lint type-check security-check test-coverage

# CI pipeline simulation
ci: clean install quality-check test-parallel

# Quick test (fast tests only)
test-quick:
	pytest -m "not slow" --tb=short

# Smoke tests
test-smoke:
	pytest -m smoke

# Regression tests
test-regression:
	pytest -m regression
