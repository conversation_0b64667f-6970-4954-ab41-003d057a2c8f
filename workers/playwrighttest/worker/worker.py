from playwright.sync_api import sync_playwright


with sync_playwright() as playwright:
        browser = getattr(playwright, "chromium").launch(
            headless=True,
            args=["--disable-gpu", "--single-process"],
        )
        context = browser.new_context(
            java_script_enabled=True,
            user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        )
        page = context.new_page()

        page.goto("https://www.invokeinc.com/")

        page.click("text=Let's work together")
        print(page.locator('//*[@id="element-59dd3701-0d7e-41f0-b11a-7f948af3e1fc"]/div/div/div/div[3]').inner_text())

        print("success")
        