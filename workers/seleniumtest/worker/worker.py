import os
from selenium.common.exceptions import TimeoutException
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriverWait

use_pydisplay = True

# Start pyvirtualdisplay needed for windows useres using a WSL environment
if use_pydisplay:
    from pyvirtualdisplay import Display
    os.environ['PYVIRTUALDISPLAY_DISPLAYFD'] = '0'
    display = Display(visible=0, size=(1366, 768))
    display.start()
    print("Display initialized")

# Import inside the context manager to avoid importing it when not needed
import undetected_chromedriver as uc


options = uc.ChromeOptions()
options.headless = False

options.add_argument("--disable-component-update")
options.add_argument("--remote-debugging-port=9222")
options.add_argument("--disable-background-networking")
options.add_argument("--disable-default-apps")
options.add_argument("--incognito")
options.add_argument("--no-first-run")
options.add_argument("--no-sandbox")
options.add_argument("--disable-dev-shm-usage")
options.add_argument("--disable-gpu")
options.add_argument("--single-process")
options.add_argument("--no-zygote")
options.add_argument("--disable-setuid-sandbox")
#options.add_argument("--user-data-dir=/tmp/chrome-user-data")
options.add_argument("--crash-dumps-dir=/tmp")
options.set_capability("goog:loggingPrefs", {"performance": "ALL", "browser": "ALL"})
driver = uc.Chrome(options=options, version_main=131)
driver.set_window_size(1366, 768)
print("Selenium Init")

driver.get("https://www.invokeinc.com/")

driver.find_element(By.CLASS_NAME, "wsite-button-inner").click()
contact_us_text =  driver.find_element(By.XPATH, '//*[@id="element-59dd3701-0d7e-41f0-b11a-7f948af3e1fc"]/div/div/div/div[3]')

print(contact_us_text.text)

print("Success")