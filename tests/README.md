# Lambda Microservices Testing Framework

This testing framework provides comprehensive unit and integration testing capabilities for Lambda microservices that interact with AWS services (S3, Secrets Manager) and MongoDB.

## Framework Architecture

The testing framework follows SOLID principles and provides:

- **Base Classes**: Abstract base classes and interfaces for dependency injection
- **Mock Services**: Mock implementations for AWS services and MongoDB
- **Test Utilities**: Data providers, assertions, and helper functions
- **Configuration**: Pytest configuration with markers, fixtures, and reporting

## Directory Structure

```
tests/
├── base/                   # Base classes and interfaces
│   ├── __init__.py
│   ├── interfaces.py       # Service interfaces (SOLID principles)
│   └── base_test.py       # Base test classes
├── mocks/                  # Mock implementations
│   ├── __init__.py
│   ├── aws_services.py    # AWS service mocks
│   └── mongo_services.py  # MongoDB mocks
├── utils/                  # Test utilities
│   ├── __init__.py
│   ├── test_data_provider.py  # Test data generation
│   ├── test_assertions.py     # Custom assertions
│   └── test_helpers.py        # Helper functions
├── unit/                   # Unit tests
│   └── __init__.py
├── integration/            # Integration tests
│   └── __init__.py
├── fixtures/               # Test fixtures and data
│   └── __init__.py
├── conftest.py            # Pytest configuration and fixtures
├── requirements.txt       # Testing dependencies
└── README.md             # This file
```

## Getting Started

### 1. Install Dependencies

```bash
# Install testing dependencies
pip install -r tests/requirements.txt

# Or use the Makefile
make install
```

### 2. Run Tests

```bash
# Run all tests
pytest

# Run unit tests only
pytest -m unit

# Run integration tests only
pytest -m integration

# Run with coverage
pytest --cov

# Use Makefile shortcuts
make test
make test-unit
make test-coverage
```

### 3. Create Your First Test

Create a test file in `tests/unit/` for your Lambda function:

```python
# tests/unit/test_email_sender.py
import pytest
from tests.base import BaseLambdaTest
from tests.utils import TestDataProvider, TestAssertions

class TestEmailSenderLambda(BaseLambdaTest):
    
    def _setup_dependencies(self):
        """Set up test dependencies"""
        self.test_data_provider = TestDataProvider()
        self.test_assertion = TestAssertions(self)
    
    @pytest.mark.unit
    def test_lambda_handler_success(self, mock_secrets_manager, mock_s3_service):
        """Test successful email sending"""
        # Arrange
        event = self.test_data_provider.get_lambda_event('email_sender')
        context = self.test_data_provider.get_lambda_context()
        
        # Act
        from email_sender.lambda_function import lambda_handler
        response = lambda_handler(event, context)
        
        # Assert
        self.test_assertion.assert_lambda_success_response(response)
```

## Key Features

### 1. SOLID Principles Implementation

The framework follows SOLID principles:

- **Single Responsibility**: Each class has a single, well-defined purpose
- **Open/Closed**: Extensible through interfaces without modifying existing code
- **Liskov Substitution**: Mock implementations can replace real services
- **Interface Segregation**: Specific interfaces for different service types
- **Dependency Inversion**: Depends on abstractions, not concrete implementations

### 2. Mock Services

#### AWS Services
```python
# Use in tests
def test_with_aws_services(self, mock_secrets_manager, mock_s3_service):
    # Mock services are automatically injected
    mock_secrets_manager.add_test_secret('my-secret', {'key': 'value'})
    mock_s3_service.add_test_object('bucket', 'key', b'content')
```

#### MongoDB
```python
# Use in tests
def test_with_mongo(self, mock_mongo_database):
    # Add test documents
    mock_mongo_database.add_test_document('collection', {'field': 'value'})
    
    # Test operations
    result = mock_mongo_database.find_one('collection', {'field': 'value'})
    assert result is not None
```

### 3. Test Data Providers

```python
# Generate test data
def test_with_test_data(self, test_data_provider):
    # Get Lambda events
    api_event = test_data_provider.get_lambda_event('api_gateway')
    s3_event = test_data_provider.get_lambda_event('s3_trigger')
    
    # Get sample documents
    invoice = test_data_provider.get_sample_document('invoice')
    email = test_data_provider.get_sample_document('email')
```

### 4. Custom Assertions

```python
# Use custom assertions
def test_with_assertions(self, test_assertions):
    response = {'statusCode': 200, 'body': '{"success": true}'}
    
    # Assert Lambda response format
    test_assertions.assert_lambda_response(response)
    
    # Assert MongoDB document structure
    document = {'_id': '123', 'name': 'test'}
    test_assertions.assert_mongo_document(document, ['_id', 'name'])
```

## Test Markers

Use pytest markers to categorize tests:

```python
@pytest.mark.unit          # Unit tests
@pytest.mark.integration   # Integration tests
@pytest.mark.aws          # AWS service tests
@pytest.mark.mongo        # MongoDB tests
@pytest.mark.slow         # Slow running tests
@pytest.mark.smoke        # Smoke tests
```

Run specific test categories:
```bash
pytest -m unit
pytest -m "aws and not slow"
pytest -m smoke
```

## Configuration

### Environment Variables

The framework automatically sets up test environment variables:

- `ENV=test`
- `AWS_DEFAULT_REGION=us-east-1`
- `MONGO_DATABASE=test_database`
- `REPORTS_EMAIL=<EMAIL>`

### Pytest Configuration

See `pytest.ini` for detailed configuration including:
- Coverage settings
- HTML reporting
- Log configuration
- Test discovery patterns

## Best Practices

### 1. Test Structure

Follow the Arrange-Act-Assert pattern:

```python
def test_function(self):
    # Arrange
    event = self.create_test_event()
    context = self.create_test_context()
    
    # Act
    result = lambda_handler(event, context)
    
    # Assert
    assert result['statusCode'] == 200
```

### 2. Mock Usage

Use dependency injection for better testability:

```python
class MyService:
    def __init__(self, secrets_manager: ISecretsManager):
        self.secrets_manager = secrets_manager
    
    def get_config(self):
        return self.secrets_manager.get_secret('config')

# In tests
def test_service(self, mock_secrets_manager):
    service = MyService(mock_secrets_manager)
    config = service.get_config()
    assert config is not None
```

### 3. Test Data Management

Use the test data provider for consistent test data:

```python
def test_with_realistic_data(self, test_data_provider):
    # Get realistic test data
    invoice_data = test_data_provider.get_sample_document('invoice')
    
    # Customize as needed
    invoice_data['amount'] = 1500.00
    
    # Use in test
    result = process_invoice(invoice_data)
```

## Running Tests

### Command Line Options

```bash
# Basic test run
pytest

# Verbose output
pytest -v

# Stop on first failure
pytest -x

# Run specific test file
pytest tests/unit/test_email_sender.py

# Run specific test method
pytest tests/unit/test_email_sender.py::TestEmailSender::test_success

# Run with coverage
pytest --cov=hennessy-aria/src --cov-report=html

# Parallel execution
pytest -n auto

# Generate HTML report
pytest --html=reports/report.html
```

### Makefile Shortcuts

```bash
make test              # Run all tests
make test-unit         # Unit tests only
make test-integration  # Integration tests only
make test-coverage     # With coverage report
make test-parallel     # Parallel execution
make clean            # Clean up artifacts
```

## Extending the Framework

### Adding New Mock Services

1. Create interface in `tests/base/interfaces.py`
2. Implement mock in appropriate file under `tests/mocks/`
3. Add fixture in `tests/conftest.py`
4. Update `__init__.py` files

### Adding New Test Utilities

1. Add utility functions to `tests/utils/test_helpers.py`
2. Add custom assertions to `tests/utils/test_assertions.py`
3. Add test data generators to `tests/utils/test_data_provider.py`

### Adding New Test Categories

1. Add marker to `pytest.ini`
2. Add marker logic to `tests/conftest.py`
3. Document usage in this README

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure source directories are in Python path (handled by `conftest.py`)
2. **Mock Not Working**: Check that patches are applied correctly in test setup
3. **Environment Variables**: Use `isolated_environment` fixture for test isolation
4. **Coverage Issues**: Ensure source paths are correct in `pytest.ini`

### Debug Mode

Run tests in debug mode for detailed output:
```bash
pytest -s -vv --tb=long
make test-debug
```

## Contributing

When adding new tests:

1. Follow the existing patterns and structure
2. Use appropriate base classes (`BaseLambdaTest`, `BaseServiceTest`)
3. Add proper markers and documentation
4. Include both positive and negative test cases
5. Ensure good test coverage

## Support

For questions or issues with the testing framework, please refer to:
- This documentation
- Existing test examples in the codebase
- Pytest documentation: https://docs.pytest.org/
