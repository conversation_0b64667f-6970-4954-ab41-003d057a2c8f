"""
Pytest configuration and fixtures for Lambda microservices testing.
Provides common fixtures and setup for all test modules.
"""

import os
import sys
import pytest
from unittest.mock import Mock, patch
from typing import Dict, Any, Generator

# Add source directories to Python path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'hennessy-aria', 'src'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'hennessy'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'tagtitle', 'src'))

from tests.mocks.aws_services import (
    MockSecretsManager, MockS3Service, MockParameterStore, MockAwsService
)
from tests.mocks.mongo_services import MockMongoDatabase, MockMongo
from tests.utils.test_data_provider import TestDataProvider
from tests.utils.test_assertions import TestAssertions
from tests.utils.test_helpers import TestHelpers


@pytest.fixture(scope="session", autouse=True)
def setup_test_environment():
    """Set up test environment for all tests"""
    TestHelpers.setup_test_environment()
    
    # Create necessary directories
    os.makedirs('tests/logs', exist_ok=True)
    os.makedirs('reports', exist_ok=True)
    os.makedirs('htmlcov', exist_ok=True)
    
    yield
    
    # Cleanup if needed
    pass


@pytest.fixture
def mock_secrets_manager():
    """Provide mock Secrets Manager service"""
    mock_service = MockSecretsManager()
    
    # Add some default test secrets
    mock_service.add_test_secret('test-mongodb_uri', 'mongodb://localhost:27017/test_db')
    mock_service.add_test_secret('test-aria_cm_tokens', {
        'test': {
            'url': 'https://test-aria.example.com',
            'public_url': '/public/v1/apps/',
            'token': 'Bearer test-token'
        }
    })
    mock_service.add_test_secret('test-email_credentials', {
        'username': '<EMAIL>',
        'password': 'test-password'
    })
    
    return mock_service


@pytest.fixture
def mock_s3_service():
    """Provide mock S3 service"""
    mock_service = MockS3Service()
    
    # Add some default test objects
    mock_service.add_test_object('test-bucket', 'test-file.txt', b'test content')
    mock_service.add_test_object('test-bucket', 'documents/invoice.pdf', b'fake pdf content')
    mock_service.add_test_object('test-bucket', 'documents/bol.pdf', b'fake bol content')
    
    return mock_service


@pytest.fixture
def mock_parameter_store():
    """Provide mock Parameter Store service"""
    mock_service = MockParameterStore()
    
    # Add some default test parameters
    mock_service.add_test_parameter('/test/database/host', 'localhost')
    mock_service.add_test_parameter('/test/database/port', '27017')
    mock_service.add_test_parameter('/test/api/config', '{"timeout": 30, "retries": 3}')
    
    return mock_service


@pytest.fixture
def mock_mongo_database():
    """Provide mock MongoDB database"""
    mock_db = MockMongoDatabase('test_database')
    
    # Add some default test documents
    mock_db.add_test_document('emails', {
        'subject': 'Test Email',
        'sender': '<EMAIL>',
        'processed': False
    })
    
    mock_db.add_test_document('invoices', {
        'invoice_number': 'INV-001',
        'vendor': 'Test Vendor',
        'amount': 1000.00,
        'status': 'pending'
    })
    
    return mock_db


@pytest.fixture
def mock_mongo_client():
    """Provide mock MongoDB client (legacy pattern)"""
    mock_mongo = MockMongo('mongodb://localhost:27017/test_db')
    return mock_mongo


@pytest.fixture
def test_data_provider():
    """Provide test data provider"""
    return TestDataProvider()


@pytest.fixture
def test_assertions(request):
    """Provide test assertions helper"""
    return TestAssertions(request.instance)


@pytest.fixture
def lambda_context():
    """Provide mock Lambda context"""
    context = Mock()
    context.function_name = 'test-function'
    context.function_version = '$LATEST'
    context.invoked_function_arn = 'arn:aws:lambda:us-east-1:************:function:test-function'
    context.memory_limit_in_mb = 128
    context.get_remaining_time_in_millis.return_value = 30000
    context.aws_request_id = 'test-request-id'
    context.log_group_name = '/aws/lambda/test-function'
    context.log_stream_name = '2023/01/01/[$LATEST]test-stream'
    return context


@pytest.fixture
def api_gateway_event():
    """Provide sample API Gateway event"""
    return {
        'version': '2.0',
        'routeKey': 'POST /test',
        'rawPath': '/test',
        'rawQueryString': '',
        'headers': {
            'content-type': 'application/json',
            'user-agent': 'test-agent'
        },
        'requestContext': {
            'accountId': '************',
            'apiId': 'test-api',
            'domainName': 'test.execute-api.us-east-1.amazonaws.com',
            'requestId': 'test-request-id',
            'stage': 'test',
            'http': {
                'method': 'POST',
                'path': '/test',
                'protocol': 'HTTP/1.1'
            }
        },
        'body': '{"test": "data"}',
        'isBase64Encoded': False
    }


@pytest.fixture
def s3_event():
    """Provide sample S3 event"""
    return {
        'Records': [
            {
                'eventVersion': '2.1',
                'eventSource': 'aws:s3',
                'eventName': 'ObjectCreated:Put',
                's3': {
                    'bucket': {'name': 'test-bucket'},
                    'object': {'key': 'test-folder/test-file.pdf'}
                }
            }
        ]
    }


@pytest.fixture
def patch_boto3():
    """Patch boto3 client creation"""
    with patch('boto3.client') as mock_client, \
         patch('boto3.session.Session') as mock_session:
        
        # Configure mock clients
        mock_secrets_client = TestHelpers.mock_boto3_client('secretsmanager')
        mock_s3_client = TestHelpers.mock_boto3_client('s3')
        mock_ssm_client = TestHelpers.mock_boto3_client('ssm')
        
        def client_factory(service_name, **kwargs):
            if service_name == 'secretsmanager':
                return mock_secrets_client
            elif service_name == 's3':
                return mock_s3_client
            elif service_name == 'ssm':
                return mock_ssm_client
            else:
                return Mock()
        
        mock_client.side_effect = client_factory
        mock_session.return_value.client.side_effect = client_factory
        
        yield {
            'client': mock_client,
            'session': mock_session,
            'secrets_client': mock_secrets_client,
            's3_client': mock_s3_client,
            'ssm_client': mock_ssm_client
        }


@pytest.fixture
def patch_pymongo():
    """Patch PyMongo client creation"""
    with patch('pymongo.MongoClient') as mock_client:
        mock_mongo_client = TestHelpers.mock_pymongo_client()
        mock_client.return_value = mock_mongo_client
        yield mock_mongo_client


@pytest.fixture(scope="function")
def isolated_environment():
    """Provide isolated environment for each test"""
    # Store original environment
    original_env = os.environ.copy()
    
    # Set up test environment
    TestHelpers.setup_test_environment()
    
    yield
    
    # Restore original environment
    os.environ.clear()
    os.environ.update(original_env)


@pytest.fixture
def temp_file():
    """Provide temporary file for testing"""
    temp_files = []
    
    def create_temp_file(content, suffix='.tmp'):
        temp_path = TestHelpers.create_temp_file(content, suffix)
        temp_files.append(temp_path)
        return temp_path
    
    yield create_temp_file
    
    # Cleanup
    for temp_path in temp_files:
        TestHelpers.cleanup_temp_path(temp_path)


@pytest.fixture
def temp_directory():
    """Provide temporary directory for testing"""
    temp_dirs = []
    
    def create_temp_dir():
        temp_path = TestHelpers.create_temp_directory()
        temp_dirs.append(temp_path)
        return temp_path
    
    yield create_temp_dir
    
    # Cleanup
    for temp_path in temp_dirs:
        TestHelpers.cleanup_temp_path(temp_path)


# Pytest hooks for custom behavior
def pytest_configure(config):
    """Configure pytest with custom settings"""
    # Add custom markers
    config.addinivalue_line("markers", "unit: Unit tests")
    config.addinivalue_line("markers", "integration: Integration tests")
    config.addinivalue_line("markers", "slow: Slow running tests")
    config.addinivalue_line("markers", "aws: AWS service tests")
    config.addinivalue_line("markers", "mongo: MongoDB tests")


def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers based on test location"""
    for item in items:
        # Add markers based on test file location
        if "unit" in str(item.fspath):
            item.add_marker(pytest.mark.unit)
        elif "integration" in str(item.fspath):
            item.add_marker(pytest.mark.integration)
        
        # Add markers based on test name patterns
        if "aws" in item.name.lower():
            item.add_marker(pytest.mark.aws)
        if "mongo" in item.name.lower():
            item.add_marker(pytest.mark.mongo)
        if "slow" in item.name.lower():
            item.add_marker(pytest.mark.slow)


def pytest_runtest_setup(item):
    """Setup for each test run"""
    # Skip tests marked as skip_ci in CI environment
    if "CI" in os.environ and item.get_closest_marker("skip_ci"):
        pytest.skip("Skipped in CI environment")


def pytest_html_report_title(report):
    """Customize HTML report title"""
    report.title = "Lambda Microservices Test Report"
