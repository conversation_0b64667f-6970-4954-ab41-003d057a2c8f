"""
Base interfaces for testing framework following SOLID principles.
These interfaces define contracts for dependency injection and testability.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union
import json


class IAwsService(ABC):
    """Interface for AWS service operations"""
    
    @abstractmethod
    def get_client(self, service_name: str, region_name: str = "us-east-1") -> Any:
        """Get AWS service client"""
        pass


class ISecretsManager(ABC):
    """Interface for AWS Secrets Manager operations"""
    
    @abstractmethod
    def get_secret(self, secret_name: str, return_json: bool = True) -> Union[str, Dict]:
        """Retrieve secret from AWS Secrets Manager"""
        pass
    
    @abstractmethod
    def create_secret(self, secret_name: str, secret_value: Union[str, Dict]) -> bool:
        """Create a new secret in AWS Secrets Manager"""
        pass
    
    @abstractmethod
    def update_secret(self, secret_name: str, secret_value: Union[str, Dict]) -> bool:
        """Update existing secret in AWS Secrets Manager"""
        pass
    
    @abstractmethod
    def delete_secret(self, secret_name: str, recovery_window_days: int = 7) -> bool:
        """Delete secret from AWS Secrets Manager"""
        pass


class IS3Service(ABC):
    """Interface for AWS S3 operations"""
    
    @abstractmethod
    def get_object(self, bucket: str, key: str) -> Dict[str, Any]:
        """Get object from S3 bucket"""
        pass
    
    @abstractmethod
    def put_object(self, bucket: str, key: str, body: bytes, **kwargs) -> Dict[str, Any]:
        """Put object to S3 bucket"""
        pass
    
    @abstractmethod
    def delete_object(self, bucket: str, key: str) -> Dict[str, Any]:
        """Delete object from S3 bucket"""
        pass
    
    @abstractmethod
    def list_objects(self, bucket: str, prefix: str = "") -> List[Dict[str, Any]]:
        """List objects in S3 bucket"""
        pass
    
    @abstractmethod
    def object_exists(self, bucket: str, key: str) -> bool:
        """Check if object exists in S3 bucket"""
        pass


class IParameterStore(ABC):
    """Interface for AWS Systems Manager Parameter Store operations"""
    
    @abstractmethod
    def get_parameter(self, param_name: str, return_json: bool = True, with_decryption: bool = True) -> Union[str, Dict]:
        """Get parameter from Parameter Store"""
        pass
    
    @abstractmethod
    def put_parameter(self, param_name: str, param_value: str, param_type: str = "String") -> bool:
        """Put parameter to Parameter Store"""
        pass


class IMongoDatabase(ABC):
    """Interface for MongoDB operations"""
    
    @abstractmethod
    def get_collection(self, collection_name: str) -> Any:
        """Get MongoDB collection"""
        pass
    
    @abstractmethod
    def insert_one(self, collection_name: str, document: Dict[str, Any]) -> Any:
        """Insert single document"""
        pass
    
    @abstractmethod
    def insert_many(self, collection_name: str, documents: List[Dict[str, Any]]) -> Any:
        """Insert multiple documents"""
        pass
    
    @abstractmethod
    def find_one(self, collection_name: str, filter_dict: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Find single document"""
        pass
    
    @abstractmethod
    def find(self, collection_name: str, filter_dict: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Find multiple documents"""
        pass
    
    @abstractmethod
    def update_one(self, collection_name: str, filter_dict: Dict[str, Any], update_dict: Dict[str, Any]) -> Any:
        """Update single document"""
        pass
    
    @abstractmethod
    def update_many(self, collection_name: str, filter_dict: Dict[str, Any], update_dict: Dict[str, Any]) -> Any:
        """Update multiple documents"""
        pass
    
    @abstractmethod
    def delete_one(self, collection_name: str, filter_dict: Dict[str, Any]) -> Any:
        """Delete single document"""
        pass
    
    @abstractmethod
    def delete_many(self, collection_name: str, filter_dict: Dict[str, Any]) -> Any:
        """Delete multiple documents"""
        pass
    
    @abstractmethod
    def close_connection(self) -> None:
        """Close database connection"""
        pass


class IEmailService(ABC):
    """Interface for email service operations"""
    
    @abstractmethod
    def send_email(self, subject: str, body: str, recipients: List[str], 
                   sender: str = None, attachments: List[Dict] = None) -> bool:
        """Send email"""
        pass


class ILambdaContext(ABC):
    """Interface for Lambda context operations"""
    
    @abstractmethod
    def get_remaining_time_in_millis(self) -> int:
        """Get remaining execution time"""
        pass
    
    @abstractmethod
    def get_function_name(self) -> str:
        """Get Lambda function name"""
        pass
    
    @abstractmethod
    def get_function_version(self) -> str:
        """Get Lambda function version"""
        pass
    
    @abstractmethod
    def get_invoked_function_arn(self) -> str:
        """Get invoked function ARN"""
        pass
    
    @abstractmethod
    def get_memory_limit_in_mb(self) -> int:
        """Get memory limit"""
        pass
    
    @abstractmethod
    def get_aws_request_id(self) -> str:
        """Get AWS request ID"""
        pass


class ITestDataProvider(ABC):
    """Interface for test data providers"""
    
    @abstractmethod
    def get_lambda_event(self, event_type: str) -> Dict[str, Any]:
        """Get sample Lambda event data"""
        pass
    
    @abstractmethod
    def get_lambda_context(self) -> ILambdaContext:
        """Get mock Lambda context"""
        pass
    
    @abstractmethod
    def get_sample_document(self, document_type: str) -> Dict[str, Any]:
        """Get sample document data"""
        pass


class ITestAssertion(ABC):
    """Interface for custom test assertions"""
    
    @abstractmethod
    def assert_lambda_response(self, response: Dict[str, Any], expected_status: int = 200) -> None:
        """Assert Lambda response format and status"""
        pass
    
    @abstractmethod
    def assert_mongo_document(self, document: Dict[str, Any], expected_fields: List[str]) -> None:
        """Assert MongoDB document structure"""
        pass
    
    @abstractmethod
    def assert_s3_object_exists(self, bucket: str, key: str) -> None:
        """Assert S3 object exists"""
        pass
