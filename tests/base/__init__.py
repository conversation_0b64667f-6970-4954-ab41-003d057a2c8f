"""
Base classes and interfaces for the testing framework.
Provides foundation for SOLID principle-based testing.
"""

from .interfaces import (
    IAwsService,
    ISecretsManager,
    IS3Service,
    IParameterStore,
    IMongoDatabase,
    IEmailService,
    ILambdaContext,
    ITestDataProvider,
    ITestAssertion
)

from .base_test import (
    BaseLambdaTest,
    BaseServiceTest,
    BaseIntegrationTest
)

__all__ = [
    'IAwsService',
    'ISecretsManager',
    'IS3Service',
    'IParameterStore',
    'IMongoDatabase',
    'IEmailService',
    'ILambdaContext',
    'ITestDataProvider',
    'ITestAssertion',
    'BaseLambdaTest',
    'BaseServiceTest',
    'BaseIntegrationTest'
]
