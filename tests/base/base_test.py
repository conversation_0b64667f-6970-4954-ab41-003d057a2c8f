"""
Base test classes providing common functionality for Lambda function testing.
Follows SOLID principles with dependency injection and separation of concerns.
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
from typing import Dict, Any, Optional, List
import json
import os
from abc import ABC, abstractmethod

from .interfaces import (
    ISecretsManager, IS3Service, IParameterStore, IMongoDatabase,
    IEmailService, ILambdaContext, ITestDataProvider, ITestAssertion
)


class BaseLambdaTest(unittest.TestCase, ABC):
    """
    Base test class for Lambda functions.
    Provides common setup, teardown, and utility methods.
    """
    
    def __init__(self, methodName: str = "runTest"):
        super().__init__(methodName)
        self.secrets_manager: Optional[ISecretsManager] = None
        self.s3_service: Optional[IS3Service] = None
        self.parameter_store: Optional[IParameterStore] = None
        self.mongo_database: Optional[IMongoDatabase] = None
        self.email_service: Optional[IEmailService] = None
        self.test_data_provider: Optional[ITestDataProvider] = None
        self.test_assertion: Optional[ITestAssertion] = None
    
    def setUp(self) -> None:
        """Set up test dependencies and environment"""
        super().setUp()
        self._setup_environment_variables()
        self._setup_dependencies()
        self._setup_patches()
    
    def tearDown(self) -> None:
        """Clean up after test execution"""
        super().tearDown()
        self._cleanup_dependencies()
        self._cleanup_patches()
    
    def _setup_environment_variables(self) -> None:
        """Set up required environment variables for testing"""
        test_env_vars = {
            'ENV': 'test',
            'MONGO_DATABASE': 'test_database',
            'AWS_DEFAULT_REGION': 'us-east-1',
            'REPORTS_EMAIL': '<EMAIL>',
            'REPORTER_EMAIL': '<EMAIL>',
            'BCC_EMAIL': '<EMAIL>',
            'ARIA_ENV': 'test'
        }
        
        for key, value in test_env_vars.items():
            os.environ.setdefault(key, value)
    
    @abstractmethod
    def _setup_dependencies(self) -> None:
        """Set up test dependencies (to be implemented by subclasses)"""
        pass
    
    def _setup_patches(self) -> None:
        """Set up common patches for AWS services"""
        self.boto3_patches = []
        
        # Patch boto3.client
        self.boto3_client_patch = patch('boto3.client')
        self.mock_boto3_client = self.boto3_client_patch.start()
        self.boto3_patches.append(self.boto3_client_patch)
        
        # Patch boto3.session
        self.boto3_session_patch = patch('boto3.session.Session')
        self.mock_boto3_session = self.boto3_session_patch.start()
        self.boto3_patches.append(self.boto3_session_patch)
    
    def _cleanup_dependencies(self) -> None:
        """Clean up test dependencies"""
        if self.mongo_database:
            self.mongo_database.close_connection()
    
    def _cleanup_patches(self) -> None:
        """Clean up patches"""
        for patch_obj in self.boto3_patches:
            patch_obj.stop()
    
    def create_lambda_event(self, event_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a Lambda event with proper structure"""
        base_event = {
            'version': '2.0',
            'routeKey': 'POST /test',
            'rawPath': '/test',
            'rawQueryString': '',
            'headers': {
                'content-type': 'application/json',
                'user-agent': 'test-agent'
            },
            'requestContext': {
                'accountId': '************',
                'apiId': 'test-api',
                'domainName': 'test.execute-api.us-east-1.amazonaws.com',
                'requestId': 'test-request-id',
                'stage': 'test'
            },
            'isBase64Encoded': False
        }
        
        if 'body' in event_data:
            base_event['body'] = json.dumps(event_data['body']) if isinstance(event_data['body'], dict) else event_data['body']
        
        base_event.update(event_data)
        return base_event
    
    def create_lambda_context(self, **kwargs) -> Mock:
        """Create a mock Lambda context"""
        context = Mock()
        context.function_name = kwargs.get('function_name', 'test-function')
        context.function_version = kwargs.get('function_version', '$LATEST')
        context.invoked_function_arn = kwargs.get('invoked_function_arn', 'arn:aws:lambda:us-east-1:************:function:test-function')
        context.memory_limit_in_mb = kwargs.get('memory_limit_in_mb', 128)
        context.get_remaining_time_in_millis = Mock(return_value=kwargs.get('remaining_time_ms', 30000))
        context.aws_request_id = kwargs.get('aws_request_id', 'test-request-id')
        context.log_group_name = kwargs.get('log_group_name', '/aws/lambda/test-function')
        context.log_stream_name = kwargs.get('log_stream_name', '2023/01/01/[$LATEST]test-stream')
        return context
    
    def assert_lambda_response_format(self, response: Dict[str, Any], expected_status_code: int = 200) -> None:
        """Assert that response follows Lambda response format"""
        self.assertIsInstance(response, dict)
        self.assertIn('statusCode', response)
        self.assertIn('body', response)
        self.assertEqual(response['statusCode'], expected_status_code)
        
        # Validate body is JSON serializable
        if response['body']:
            try:
                json.loads(response['body']) if isinstance(response['body'], str) else response['body']
            except (json.JSONDecodeError, TypeError):
                self.fail("Response body is not valid JSON")
    
    def assert_error_response(self, response: Dict[str, Any], expected_status_code: int = 500) -> None:
        """Assert that response is an error response"""
        self.assert_lambda_response_format(response, expected_status_code)
        body = json.loads(response['body']) if isinstance(response['body'], str) else response['body']
        self.assertIn('error', body)
    
    def assert_success_response(self, response: Dict[str, Any], expected_keys: List[str] = None) -> None:
        """Assert that response is a success response"""
        self.assert_lambda_response_format(response, 200)
        body = json.loads(response['body']) if isinstance(response['body'], str) else response['body']
        
        if expected_keys:
            for key in expected_keys:
                self.assertIn(key, body)


class BaseServiceTest(unittest.TestCase, ABC):
    """
    Base test class for service classes (non-Lambda).
    Provides common functionality for testing utility classes.
    """
    
    def setUp(self) -> None:
        """Set up test environment"""
        super().setUp()
        self._setup_environment_variables()
        self._setup_mocks()
    
    def tearDown(self) -> None:
        """Clean up after test"""
        super().tearDown()
        self._cleanup_mocks()
    
    def _setup_environment_variables(self) -> None:
        """Set up environment variables for testing"""
        test_env_vars = {
            'ENV': 'test',
            'AWS_DEFAULT_REGION': 'us-east-1'
        }
        
        for key, value in test_env_vars.items():
            os.environ.setdefault(key, value)
    
    @abstractmethod
    def _setup_mocks(self) -> None:
        """Set up mocks (to be implemented by subclasses)"""
        pass
    
    def _cleanup_mocks(self) -> None:
        """Clean up mocks"""
        pass


class BaseIntegrationTest(unittest.TestCase, ABC):
    """
    Base test class for integration tests.
    Provides setup for testing with real AWS services (when needed).
    """
    
    def setUp(self) -> None:
        """Set up integration test environment"""
        super().setUp()
        self._setup_environment_variables()
        self._validate_integration_environment()
    
    def _setup_environment_variables(self) -> None:
        """Set up environment variables for integration testing"""
        required_env_vars = [
            'AWS_ACCESS_KEY_ID',
            'AWS_SECRET_ACCESS_KEY',
            'AWS_DEFAULT_REGION'
        ]
        
        missing_vars = [var for var in required_env_vars if not os.environ.get(var)]
        if missing_vars:
            self.skipTest(f"Missing required environment variables: {missing_vars}")
    
    def _validate_integration_environment(self) -> None:
        """Validate that integration test environment is properly configured"""
        # Override in subclasses to add specific validation
        pass
