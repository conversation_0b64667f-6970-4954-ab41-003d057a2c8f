"""
Mock implementations for MongoDB services.
Provides testable implementations for MongoDB operations.
"""

import copy
from typing import Dict, Any, List, Optional, Union
from unittest.mock import Mock, MagicMock
from datetime import datetime
import uuid

from ..base.interfaces import IMongoDatabase


class MockMongoDatabase(IMongoDatabase):
    """Mock implementation of MongoDB database operations"""
    
    def __init__(self, database_name: str = "test_database"):
        self.database_name = database_name
        self.collections = {}  # collection_name -> list of documents
        self.client = MockMongoClient()
    
    def get_collection(self, collection_name: str) -> 'MockMongoCollection':
        """Get MongoDB collection"""
        if collection_name not in self.collections:
            self.collections[collection_name] = []
        
        return MockMongoCollection(collection_name, self.collections[collection_name])
    
    def insert_one(self, collection_name: str, document: Dict[str, Any]) -> Any:
        """Insert single document"""
        if collection_name not in self.collections:
            self.collections[collection_name] = []
        
        # Add _id if not present
        if '_id' not in document:
            document['_id'] = str(uuid.uuid4())
        
        # Add timestamps
        document['created_at'] = datetime.now()
        document['updated_at'] = datetime.now()
        
        self.collections[collection_name].append(copy.deepcopy(document))
        
        result = Mock()
        result.inserted_id = document['_id']
        result.acknowledged = True
        return result
    
    def insert_many(self, collection_name: str, documents: List[Dict[str, Any]]) -> Any:
        """Insert multiple documents"""
        if collection_name not in self.collections:
            self.collections[collection_name] = []
        
        inserted_ids = []
        for document in documents:
            if '_id' not in document:
                document['_id'] = str(uuid.uuid4())
            
            document['created_at'] = datetime.now()
            document['updated_at'] = datetime.now()
            
            self.collections[collection_name].append(copy.deepcopy(document))
            inserted_ids.append(document['_id'])
        
        result = Mock()
        result.inserted_ids = inserted_ids
        result.acknowledged = True
        return result
    
    def find_one(self, collection_name: str, filter_dict: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Find single document"""
        if collection_name not in self.collections:
            return None
        
        for document in self.collections[collection_name]:
            if self._matches_filter(document, filter_dict):
                return copy.deepcopy(document)
        
        return None
    
    def find(self, collection_name: str, filter_dict: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Find multiple documents"""
        if collection_name not in self.collections:
            return []
        
        results = []
        for document in self.collections[collection_name]:
            if self._matches_filter(document, filter_dict):
                results.append(copy.deepcopy(document))
        
        return results
    
    def update_one(self, collection_name: str, filter_dict: Dict[str, Any], update_dict: Dict[str, Any]) -> Any:
        """Update single document"""
        if collection_name not in self.collections:
            result = Mock()
            result.matched_count = 0
            result.modified_count = 0
            result.acknowledged = True
            return result
        
        for i, document in enumerate(self.collections[collection_name]):
            if self._matches_filter(document, filter_dict):
                self._apply_update(document, update_dict)
                document['updated_at'] = datetime.now()
                
                result = Mock()
                result.matched_count = 1
                result.modified_count = 1
                result.acknowledged = True
                return result
        
        result = Mock()
        result.matched_count = 0
        result.modified_count = 0
        result.acknowledged = True
        return result
    
    def update_many(self, collection_name: str, filter_dict: Dict[str, Any], update_dict: Dict[str, Any]) -> Any:
        """Update multiple documents"""
        if collection_name not in self.collections:
            result = Mock()
            result.matched_count = 0
            result.modified_count = 0
            result.acknowledged = True
            return result
        
        matched_count = 0
        modified_count = 0
        
        for document in self.collections[collection_name]:
            if self._matches_filter(document, filter_dict):
                matched_count += 1
                self._apply_update(document, update_dict)
                document['updated_at'] = datetime.now()
                modified_count += 1
        
        result = Mock()
        result.matched_count = matched_count
        result.modified_count = modified_count
        result.acknowledged = True
        return result
    
    def delete_one(self, collection_name: str, filter_dict: Dict[str, Any]) -> Any:
        """Delete single document"""
        if collection_name not in self.collections:
            result = Mock()
            result.deleted_count = 0
            result.acknowledged = True
            return result
        
        for i, document in enumerate(self.collections[collection_name]):
            if self._matches_filter(document, filter_dict):
                del self.collections[collection_name][i]
                
                result = Mock()
                result.deleted_count = 1
                result.acknowledged = True
                return result
        
        result = Mock()
        result.deleted_count = 0
        result.acknowledged = True
        return result
    
    def delete_many(self, collection_name: str, filter_dict: Dict[str, Any]) -> Any:
        """Delete multiple documents"""
        if collection_name not in self.collections:
            result = Mock()
            result.deleted_count = 0
            result.acknowledged = True
            return result
        
        deleted_count = 0
        documents_to_remove = []
        
        for i, document in enumerate(self.collections[collection_name]):
            if self._matches_filter(document, filter_dict):
                documents_to_remove.append(i)
        
        # Remove documents in reverse order to maintain indices
        for i in reversed(documents_to_remove):
            del self.collections[collection_name][i]
            deleted_count += 1
        
        result = Mock()
        result.deleted_count = deleted_count
        result.acknowledged = True
        return result
    
    def close_connection(self) -> None:
        """Close database connection"""
        # Nothing to do for mock implementation
        pass
    
    def _matches_filter(self, document: Dict[str, Any], filter_dict: Dict[str, Any]) -> bool:
        """Check if document matches filter criteria"""
        if not filter_dict:
            return True
        
        for key, value in filter_dict.items():
            if key not in document:
                return False
            
            if isinstance(value, dict):
                # Handle MongoDB operators like $in, $gt, etc.
                if not self._matches_operator(document[key], value):
                    return False
            else:
                if document[key] != value:
                    return False
        
        return True
    
    def _matches_operator(self, field_value: Any, operator_dict: Dict[str, Any]) -> bool:
        """Handle MongoDB query operators"""
        for operator, value in operator_dict.items():
            if operator == '$in':
                if field_value not in value:
                    return False
            elif operator == '$nin':
                if field_value in value:
                    return False
            elif operator == '$gt':
                if not (field_value > value):
                    return False
            elif operator == '$gte':
                if not (field_value >= value):
                    return False
            elif operator == '$lt':
                if not (field_value < value):
                    return False
            elif operator == '$lte':
                if not (field_value <= value):
                    return False
            elif operator == '$ne':
                if field_value == value:
                    return False
            elif operator == '$exists':
                # This is handled at the document level
                pass
            else:
                # Unknown operator, assume false
                return False
        
        return True
    
    def _apply_update(self, document: Dict[str, Any], update_dict: Dict[str, Any]) -> None:
        """Apply update operations to document"""
        for operator, fields in update_dict.items():
            if operator == '$set':
                for field, value in fields.items():
                    document[field] = value
            elif operator == '$unset':
                for field in fields:
                    if field in document:
                        del document[field]
            elif operator == '$inc':
                for field, value in fields.items():
                    if field in document:
                        document[field] += value
                    else:
                        document[field] = value
            elif operator == '$push':
                for field, value in fields.items():
                    if field not in document:
                        document[field] = []
                    document[field].append(value)
            elif operator == '$pull':
                for field, value in fields.items():
                    if field in document and isinstance(document[field], list):
                        document[field] = [item for item in document[field] if item != value]
    
    def add_test_document(self, collection_name: str, document: Dict[str, Any]) -> str:
        """Helper method to add test documents"""
        result = self.insert_one(collection_name, document)
        return result.inserted_id
    
    def clear_collection(self, collection_name: str) -> None:
        """Helper method to clear collection for testing"""
        if collection_name in self.collections:
            self.collections[collection_name] = []
    
    def get_collection_count(self, collection_name: str) -> int:
        """Helper method to get document count in collection"""
        if collection_name not in self.collections:
            return 0
        return len(self.collections[collection_name])


class MockMongoCollection:
    """Mock MongoDB collection"""
    
    def __init__(self, name: str, documents: List[Dict[str, Any]]):
        self.name = name
        self.documents = documents
    
    def insert_one(self, document: Dict[str, Any]) -> Any:
        """Insert single document"""
        if '_id' not in document:
            document['_id'] = str(uuid.uuid4())
        
        document['created_at'] = datetime.now()
        document['updated_at'] = datetime.now()
        
        self.documents.append(copy.deepcopy(document))
        
        result = Mock()
        result.inserted_id = document['_id']
        result.acknowledged = True
        return result
    
    def find_one(self, filter_dict: Dict[str, Any] = None) -> Optional[Dict[str, Any]]:
        """Find single document"""
        if filter_dict is None:
            filter_dict = {}
        
        for document in self.documents:
            if self._matches_filter(document, filter_dict):
                return copy.deepcopy(document)
        
        return None
    
    def find(self, filter_dict: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """Find multiple documents"""
        if filter_dict is None:
            filter_dict = {}
        
        results = []
        for document in self.documents:
            if self._matches_filter(document, filter_dict):
                results.append(copy.deepcopy(document))
        
        return results
    
    def _matches_filter(self, document: Dict[str, Any], filter_dict: Dict[str, Any]) -> bool:
        """Check if document matches filter criteria"""
        if not filter_dict:
            return True
        
        for key, value in filter_dict.items():
            if key not in document or document[key] != value:
                return False
        
        return True


class MockMongoClient:
    """Mock PyMongo client"""
    
    def __init__(self, connection_string: str = None):
        self.connection_string = connection_string
        self.databases = {}
    
    def __getitem__(self, database_name: str) -> MockMongoDatabase:
        """Get database"""
        if database_name not in self.databases:
            self.databases[database_name] = MockMongoDatabase(database_name)
        
        return self.databases[database_name]
    
    def close(self) -> None:
        """Close connection"""
        pass


class MockMongo:
    """Mock implementation matching the existing Mongo class pattern"""
    
    def __init__(self, mongo_uri: str = None):
        self.client = MockMongoClient(mongo_uri)
        self.db = None
        self.col = None
    
    def select_db_and_collection(self, db_name: str, collection_name: str) -> None:
        """Select database and collection"""
        self.db = self.client[db_name]
        self.col = self.db.get_collection(collection_name)
    
    def insert_one(self, data: Dict[str, Any]) -> Any:
        """Insert single document"""
        return self.col.insert_one(data)
    
    def insert_many(self, data: List[Dict[str, Any]]) -> Any:
        """Insert multiple documents"""
        return self.col.insert_many(data)
    
    def update_one(self, filter_dict: Dict[str, Any], data: Dict[str, Any]) -> Any:
        """Update single document"""
        return self.col.update_one(filter_dict, data)
    
    def update_many(self, filter_dict: Dict[str, Any], data: Dict[str, Any]) -> Any:
        """Update multiple documents"""
        return self.col.update_many(filter_dict, data)
    
    def find_one(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Find single document"""
        return self.col.find_one(data)
    
    def find(self, query: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Find multiple documents"""
        return self.col.find(query)
    
    def find_one_and_update(self, filter_dict: Dict[str, Any], data: Dict[str, Any], return_document: bool = True) -> Optional[Dict[str, Any]]:
        """Find and update single document"""
        # First find the document
        existing_doc = self.col.find_one(filter_dict)
        if existing_doc:
            # Update it
            self.col.update_one(filter_dict, data)
            # Return updated document if requested
            if return_document:
                return self.col.find_one(filter_dict)
        
        return existing_doc
    
    def close_connection(self) -> None:
        """Close connection"""
        if self.client:
            self.client.close()
