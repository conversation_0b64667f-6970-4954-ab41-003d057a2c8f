"""
Mock implementations for external services and dependencies.
Provides testable implementations for AWS services, MongoDB, and other external dependencies.
"""

from .aws_services import (
    MockAwsService,
    MockSecretsManager,
    MockSecretsManagerClient,
    MockS3Service,
    MockS3Client,
    MockParameterStore,
    MockParameterStoreClient
)

from .mongo_services import (
    MockMongoDatabase,
    MockMongoCollection,
    MockMongoClient,
    MockMongo
)

__all__ = [
    'MockAwsService',
    'MockSecretsManager',
    'MockSecretsManagerClient',
    'MockS3Service',
    'MockS3Client',
    'MockParameterStore',
    'MockParameterStoreClient',
    'MockMongoDatabase',
    'MockMongoCollection',
    'MockMongoClient',
    'MockMongo'
]
