"""
Mock implementations for AWS services.
Provides testable implementations following the interface contracts.
"""

import json
import base64
from typing import Dict, Any, List, Union, Optional
from unittest.mock import Mock, MagicMock
from datetime import datetime
import uuid

from ..base.interfaces import ISecretsManager, IS3Service, IParameterStore, IAwsService


class MockAwsService(IAwsService):
    """Mock implementation of AWS service factory"""
    
    def __init__(self):
        self.clients = {}
    
    def get_client(self, service_name: str, region_name: str = "us-east-1") -> Any:
        """Get mock AWS service client"""
        if service_name not in self.clients:
            if service_name == 'secretsmanager':
                self.clients[service_name] = MockSecretsManagerClient()
            elif service_name == 's3':
                self.clients[service_name] = MockS3Client()
            elif service_name == 'ssm':
                self.clients[service_name] = MockParameterStoreClient()
            else:
                self.clients[service_name] = Mock()
        
        return self.clients[service_name]


class MockSecretsManager(ISecretsManager):
    """Mock implementation of AWS Secrets Manager"""
    
    def __init__(self):
        self.secrets = {}
        self.client = MockSecretsManagerClient()
    
    def get_secret(self, secret_name: str, return_json: bool = True) -> Union[str, Dict]:
        """Retrieve secret from mock storage"""
        if secret_name not in self.secrets:
            raise Exception(f"Secret {secret_name} not found")
        
        secret_value = self.secrets[secret_name]
        
        if return_json and isinstance(secret_value, str):
            try:
                return json.loads(secret_value)
            except json.JSONDecodeError:
                return secret_value
        
        return secret_value
    
    def create_secret(self, secret_name: str, secret_value: Union[str, Dict]) -> bool:
        """Create a new secret in mock storage"""
        if secret_name in self.secrets:
            raise Exception(f"Secret {secret_name} already exists")
        
        if isinstance(secret_value, dict):
            secret_value = json.dumps(secret_value)
        
        self.secrets[secret_name] = secret_value
        return True
    
    def update_secret(self, secret_name: str, secret_value: Union[str, Dict]) -> bool:
        """Update existing secret in mock storage"""
        if secret_name not in self.secrets:
            raise Exception(f"Secret {secret_name} not found")
        
        if isinstance(secret_value, dict):
            secret_value = json.dumps(secret_value)
        
        self.secrets[secret_name] = secret_value
        return True
    
    def delete_secret(self, secret_name: str, recovery_window_days: int = 7) -> bool:
        """Delete secret from mock storage"""
        if secret_name not in self.secrets:
            raise Exception(f"Secret {secret_name} not found")
        
        del self.secrets[secret_name]
        return True
    
    def add_test_secret(self, secret_name: str, secret_value: Union[str, Dict]) -> None:
        """Helper method to add test secrets"""
        self.secrets[secret_name] = secret_value


class MockSecretsManagerClient:
    """Mock boto3 Secrets Manager client"""
    
    def __init__(self):
        self.secrets = {}
    
    def get_secret_value(self, SecretId: str) -> Dict[str, Any]:
        """Mock get_secret_value operation"""
        if SecretId not in self.secrets:
            from botocore.exceptions import ClientError
            raise ClientError(
                error_response={'Error': {'Code': 'ResourceNotFoundException'}},
                operation_name='GetSecretValue'
            )
        
        return {
            'SecretString': self.secrets[SecretId],
            'ResponseMetadata': {'HTTPStatusCode': 200}
        }
    
    def create_secret(self, Name: str, SecretString: str) -> Dict[str, Any]:
        """Mock create_secret operation"""
        if Name in self.secrets:
            from botocore.exceptions import ClientError
            raise ClientError(
                error_response={'Error': {'Code': 'ResourceExistsException'}},
                operation_name='CreateSecret'
            )
        
        self.secrets[Name] = SecretString
        return {
            'ARN': f'arn:aws:secretsmanager:us-east-1:123456789012:secret:{Name}',
            'Name': Name,
            'ResponseMetadata': {'HTTPStatusCode': 200}
        }
    
    def update_secret(self, SecretId: str, SecretString: str) -> Dict[str, Any]:
        """Mock update_secret operation"""
        if SecretId not in self.secrets:
            from botocore.exceptions import ClientError
            raise ClientError(
                error_response={'Error': {'Code': 'ResourceNotFoundException'}},
                operation_name='UpdateSecret'
            )
        
        self.secrets[SecretId] = SecretString
        return {
            'ARN': f'arn:aws:secretsmanager:us-east-1:123456789012:secret:{SecretId}',
            'Name': SecretId,
            'ResponseMetadata': {'HTTPStatusCode': 200}
        }
    
    def delete_secret(self, SecretId: str, RecoveryWindowInDays: int = 7) -> Dict[str, Any]:
        """Mock delete_secret operation"""
        if SecretId not in self.secrets:
            from botocore.exceptions import ClientError
            raise ClientError(
                error_response={'Error': {'Code': 'ResourceNotFoundException'}},
                operation_name='DeleteSecret'
            )
        
        del self.secrets[SecretId]
        return {
            'ARN': f'arn:aws:secretsmanager:us-east-1:123456789012:secret:{SecretId}',
            'Name': SecretId,
            'DeletionDate': datetime.now(),
            'ResponseMetadata': {'HTTPStatusCode': 200}
        }


class MockS3Service(IS3Service):
    """Mock implementation of AWS S3 service"""
    
    def __init__(self):
        self.objects = {}  # bucket/key -> object data
        self.client = MockS3Client()
    
    def get_object(self, bucket: str, key: str) -> Dict[str, Any]:
        """Get object from mock S3 storage"""
        object_key = f"{bucket}/{key}"
        if object_key not in self.objects:
            raise Exception(f"Object {key} not found in bucket {bucket}")
        
        return self.objects[object_key]
    
    def put_object(self, bucket: str, key: str, body: bytes, **kwargs) -> Dict[str, Any]:
        """Put object to mock S3 storage"""
        object_key = f"{bucket}/{key}"
        
        object_data = {
            'Body': body,
            'ContentLength': len(body),
            'LastModified': datetime.now(),
            'ETag': f'"{uuid.uuid4().hex}"',
            **kwargs
        }
        
        self.objects[object_key] = object_data
        
        return {
            'ETag': object_data['ETag'],
            'ResponseMetadata': {'HTTPStatusCode': 200}
        }
    
    def delete_object(self, bucket: str, key: str) -> Dict[str, Any]:
        """Delete object from mock S3 storage"""
        object_key = f"{bucket}/{key}"
        if object_key in self.objects:
            del self.objects[object_key]
        
        return {'ResponseMetadata': {'HTTPStatusCode': 204}}
    
    def list_objects(self, bucket: str, prefix: str = "") -> List[Dict[str, Any]]:
        """List objects in mock S3 storage"""
        objects = []
        for object_key, object_data in self.objects.items():
            bucket_name, key = object_key.split('/', 1)
            if bucket_name == bucket and key.startswith(prefix):
                objects.append({
                    'Key': key,
                    'LastModified': object_data['LastModified'],
                    'ETag': object_data['ETag'],
                    'Size': object_data['ContentLength']
                })
        
        return objects
    
    def object_exists(self, bucket: str, key: str) -> bool:
        """Check if object exists in mock S3 storage"""
        object_key = f"{bucket}/{key}"
        return object_key in self.objects
    
    def add_test_object(self, bucket: str, key: str, body: bytes, **kwargs) -> None:
        """Helper method to add test objects"""
        self.put_object(bucket, key, body, **kwargs)


class MockS3Client:
    """Mock boto3 S3 client"""
    
    def __init__(self):
        self.objects = {}
    
    def get_object(self, Bucket: str, Key: str) -> Dict[str, Any]:
        """Mock get_object operation"""
        object_key = f"{Bucket}/{Key}"
        if object_key not in self.objects:
            from botocore.exceptions import ClientError
            raise ClientError(
                error_response={'Error': {'Code': 'NoSuchKey'}},
                operation_name='GetObject'
            )
        
        obj_data = self.objects[object_key]
        # Create a mock Body that can be read
        body_mock = Mock()
        body_mock.read.return_value = obj_data['Body']
        
        return {
            'Body': body_mock,
            'ContentLength': obj_data['ContentLength'],
            'LastModified': obj_data['LastModified'],
            'ETag': obj_data['ETag'],
            'ResponseMetadata': {'HTTPStatusCode': 200}
        }
    
    def put_object(self, Bucket: str, Key: str, Body: bytes, **kwargs) -> Dict[str, Any]:
        """Mock put_object operation"""
        object_key = f"{Bucket}/{Key}"
        
        object_data = {
            'Body': Body,
            'ContentLength': len(Body),
            'LastModified': datetime.now(),
            'ETag': f'"{uuid.uuid4().hex}"',
            **kwargs
        }
        
        self.objects[object_key] = object_data
        
        return {
            'ETag': object_data['ETag'],
            'ResponseMetadata': {'HTTPStatusCode': 200}
        }


class MockParameterStore(IParameterStore):
    """Mock implementation of AWS Systems Manager Parameter Store"""
    
    def __init__(self):
        self.parameters = {}
        self.client = MockParameterStoreClient()
    
    def get_parameter(self, param_name: str, return_json: bool = True, with_decryption: bool = True) -> Union[str, Dict]:
        """Get parameter from mock storage"""
        if param_name not in self.parameters:
            raise Exception(f"Parameter {param_name} not found")
        
        param_value = self.parameters[param_name]['Value']
        
        if return_json:
            try:
                return json.loads(param_value)
            except json.JSONDecodeError:
                return param_value
        
        return param_value
    
    def put_parameter(self, param_name: str, param_value: str, param_type: str = "String") -> bool:
        """Put parameter to mock storage"""
        self.parameters[param_name] = {
            'Value': param_value,
            'Type': param_type
        }
        return True
    
    def add_test_parameter(self, param_name: str, param_value: str, param_type: str = "String") -> None:
        """Helper method to add test parameters"""
        self.put_parameter(param_name, param_value, param_type)


class MockParameterStoreClient:
    """Mock boto3 SSM client for Parameter Store"""
    
    def __init__(self):
        self.parameters = {}
    
    def get_parameter(self, Name: str, WithDecryption: bool = True) -> Dict[str, Any]:
        """Mock get_parameter operation"""
        if Name not in self.parameters:
            from botocore.exceptions import ClientError
            raise ClientError(
                error_response={'Error': {'Code': 'ParameterNotFound'}},
                operation_name='GetParameter'
            )
        
        return {
            'Parameter': self.parameters[Name],
            'ResponseMetadata': {'HTTPStatusCode': 200}
        }
