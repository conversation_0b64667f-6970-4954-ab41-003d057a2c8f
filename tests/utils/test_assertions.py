"""
Custom test assertions for Lambda function testing.
Provides specialized assertion methods for common testing patterns.
"""

import json
from typing import Dict, Any, List, Optional, Union
from unittest import TestCase

from ..base.interfaces import ITestAssertion, IS3Service


class TestAssertions(ITestAssertion):
    """Custom assertions for Lambda function testing"""
    
    def __init__(self, test_case: TestCase):
        self.test_case = test_case
    
    def assert_lambda_response(self, response: Dict[str, Any], expected_status: int = 200) -> None:
        """Assert Lambda response format and status"""
        self.test_case.assertIsInstance(response, dict, "Response must be a dictionary")
        self.test_case.assertIn('statusCode', response, "Response must contain 'statusCode'")
        self.test_case.assertIn('body', response, "Response must contain 'body'")
        
        self.test_case.assertEqual(
            response['statusCode'], 
            expected_status, 
            f"Expected status code {expected_status}, got {response['statusCode']}"
        )
        
        # Validate body is JSON serializable if it's a string
        if isinstance(response['body'], str) and response['body']:
            try:
                json.loads(response['body'])
            except json.JSONDecodeError:
                self.test_case.fail("Response body is not valid JSON")
    
    def assert_lambda_success_response(self, response: Dict[str, Any], expected_keys: List[str] = None) -> None:
        """Assert successful Lambda response with optional key validation"""
        self.assert_lambda_response(response, 200)
        
        if expected_keys:
            body = self._parse_response_body(response)
            for key in expected_keys:
                self.test_case.assertIn(key, body, f"Response body must contain key '{key}'")
    
    def assert_lambda_error_response(self, response: Dict[str, Any], expected_status: int = 500, expected_error_message: str = None) -> None:
        """Assert error Lambda response"""
        self.assert_lambda_response(response, expected_status)
        
        body = self._parse_response_body(response)
        self.test_case.assertIn('error', body, "Error response must contain 'error' field")
        
        if expected_error_message:
            error_message = body.get('error', '')
            self.test_case.assertIn(
                expected_error_message.lower(), 
                error_message.lower(), 
                f"Error message should contain '{expected_error_message}'"
            )
    
    def assert_mongo_document(self, document: Dict[str, Any], expected_fields: List[str]) -> None:
        """Assert MongoDB document structure"""
        self.test_case.assertIsInstance(document, dict, "Document must be a dictionary")
        
        for field in expected_fields:
            self.test_case.assertIn(field, document, f"Document must contain field '{field}'")
        
        # Check for common MongoDB fields
        if '_id' in document:
            self.test_case.assertIsNotNone(document['_id'], "Document _id should not be None")
    
    def assert_mongo_document_count(self, collection_name: str, expected_count: int, mongo_service) -> None:
        """Assert document count in MongoDB collection"""
        if hasattr(mongo_service, 'get_collection_count'):
            actual_count = mongo_service.get_collection_count(collection_name)
            self.test_case.assertEqual(
                actual_count, 
                expected_count, 
                f"Expected {expected_count} documents in {collection_name}, got {actual_count}"
            )
    
    def assert_s3_object_exists(self, bucket: str, key: str, s3_service: IS3Service = None) -> None:
        """Assert S3 object exists"""
        if s3_service:
            exists = s3_service.object_exists(bucket, key)
            self.test_case.assertTrue(
                exists, 
                f"S3 object s3://{bucket}/{key} should exist"
            )
    
    def assert_s3_object_not_exists(self, bucket: str, key: str, s3_service: IS3Service = None) -> None:
        """Assert S3 object does not exist"""
        if s3_service:
            exists = s3_service.object_exists(bucket, key)
            self.test_case.assertFalse(
                exists, 
                f"S3 object s3://{bucket}/{key} should not exist"
            )
    
    def assert_s3_object_content(self, bucket: str, key: str, expected_content: bytes, s3_service: IS3Service) -> None:
        """Assert S3 object content matches expected"""
        obj = s3_service.get_object(bucket, key)
        actual_content = obj['Body']
        
        if hasattr(actual_content, 'read'):
            actual_content = actual_content.read()
        
        self.test_case.assertEqual(
            actual_content, 
            expected_content, 
            f"S3 object content does not match expected for s3://{bucket}/{key}"
        )
    
    def assert_email_sent(self, email_service, expected_subject: str = None, expected_recipients: List[str] = None) -> None:
        """Assert email was sent with expected parameters"""
        # This would depend on how the email service mock tracks sent emails
        if hasattr(email_service, 'sent_emails'):
            self.test_case.assertGreater(
                len(email_service.sent_emails), 
                0, 
                "At least one email should have been sent"
            )
            
            if expected_subject or expected_recipients:
                last_email = email_service.sent_emails[-1]
                
                if expected_subject:
                    self.test_case.assertEqual(
                        last_email.get('subject'), 
                        expected_subject, 
                        f"Email subject should be '{expected_subject}'"
                    )
                
                if expected_recipients:
                    actual_recipients = last_email.get('recipients', [])
                    for recipient in expected_recipients:
                        self.test_case.assertIn(
                            recipient, 
                            actual_recipients, 
                            f"Email should be sent to '{recipient}'"
                        )
    
    def assert_secret_exists(self, secret_name: str, secrets_service) -> None:
        """Assert secret exists in Secrets Manager"""
        try:
            secret_value = secrets_service.get_secret(secret_name)
            self.test_case.assertIsNotNone(secret_value, f"Secret '{secret_name}' should exist")
        except Exception as e:
            self.test_case.fail(f"Secret '{secret_name}' should exist but got error: {e}")
    
    def assert_secret_value(self, secret_name: str, expected_value: Union[str, Dict], secrets_service) -> None:
        """Assert secret has expected value"""
        actual_value = secrets_service.get_secret(secret_name)
        self.test_case.assertEqual(
            actual_value, 
            expected_value, 
            f"Secret '{secret_name}' should have expected value"
        )
    
    def assert_json_structure(self, data: Union[str, Dict], expected_structure: Dict[str, type]) -> None:
        """Assert JSON data has expected structure"""
        if isinstance(data, str):
            try:
                data = json.loads(data)
            except json.JSONDecodeError:
                self.test_case.fail("Data is not valid JSON")
        
        self.test_case.assertIsInstance(data, dict, "Data must be a dictionary")
        
        for field, expected_type in expected_structure.items():
            self.test_case.assertIn(field, data, f"Data must contain field '{field}'")
            self.test_case.assertIsInstance(
                data[field], 
                expected_type, 
                f"Field '{field}' must be of type {expected_type.__name__}"
            )
    
    def assert_datetime_recent(self, datetime_value, max_seconds_ago: int = 60) -> None:
        """Assert datetime is recent (within specified seconds)"""
        from datetime import datetime, timedelta
        
        if isinstance(datetime_value, str):
            try:
                datetime_value = datetime.fromisoformat(datetime_value.replace('Z', '+00:00'))
            except ValueError:
                self.test_case.fail(f"Invalid datetime format: {datetime_value}")
        
        now = datetime.now(datetime_value.tzinfo) if datetime_value.tzinfo else datetime.now()
        max_age = timedelta(seconds=max_seconds_ago)
        
        self.test_case.assertLessEqual(
            now - datetime_value, 
            max_age, 
            f"Datetime {datetime_value} is too old (more than {max_seconds_ago} seconds ago)"
        )
    
    def assert_list_contains_item(self, items: List[Any], expected_item: Any, key: str = None) -> None:
        """Assert list contains expected item, optionally by key"""
        if key:
            item_values = [item.get(key) if isinstance(item, dict) else getattr(item, key, None) for item in items]
            expected_value = expected_item.get(key) if isinstance(expected_item, dict) else getattr(expected_item, key, None)
            self.test_case.assertIn(
                expected_value, 
                item_values, 
                f"List should contain item with {key}='{expected_value}'"
            )
        else:
            self.test_case.assertIn(
                expected_item, 
                items, 
                f"List should contain item '{expected_item}'"
            )
    
    def assert_environment_variable(self, var_name: str, expected_value: str = None) -> None:
        """Assert environment variable is set"""
        import os
        
        self.test_case.assertIn(
            var_name, 
            os.environ, 
            f"Environment variable '{var_name}' should be set"
        )
        
        if expected_value:
            actual_value = os.environ.get(var_name)
            self.test_case.assertEqual(
                actual_value, 
                expected_value, 
                f"Environment variable '{var_name}' should have value '{expected_value}'"
            )
    
    def assert_boto3_call_made(self, mock_client, method_name: str, expected_calls: int = 1) -> None:
        """Assert boto3 client method was called expected number of times"""
        if hasattr(mock_client, method_name):
            method_mock = getattr(mock_client, method_name)
            if hasattr(method_mock, 'call_count'):
                self.test_case.assertEqual(
                    method_mock.call_count, 
                    expected_calls, 
                    f"Method '{method_name}' should be called {expected_calls} times"
                )
    
    def assert_boto3_call_with_params(self, mock_client, method_name: str, expected_params: Dict[str, Any]) -> None:
        """Assert boto3 client method was called with expected parameters"""
        if hasattr(mock_client, method_name):
            method_mock = getattr(mock_client, method_name)
            if hasattr(method_mock, 'call_args_list') and method_mock.call_args_list:
                last_call_kwargs = method_mock.call_args_list[-1].kwargs
                for param, expected_value in expected_params.items():
                    self.test_case.assertIn(
                        param, 
                        last_call_kwargs, 
                        f"Method '{method_name}' should be called with parameter '{param}'"
                    )
                    self.test_case.assertEqual(
                        last_call_kwargs[param], 
                        expected_value, 
                        f"Parameter '{param}' should have value '{expected_value}'"
                    )
    
    def _parse_response_body(self, response: Dict[str, Any]) -> Dict[str, Any]:
        """Parse response body as JSON if it's a string"""
        body = response.get('body', {})
        
        if isinstance(body, str):
            try:
                return json.loads(body)
            except json.JSONDecodeError:
                return {}
        
        return body if isinstance(body, dict) else {}
