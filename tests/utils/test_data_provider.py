"""
Test data providers for generating sample data for testing.
Provides realistic test data for Lambda events, MongoDB documents, etc.
"""

import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from unittest.mock import Mock

from ..base.interfaces import ITestDataProvider, ILambdaContext


class TestDataProvider(ITestDataProvider):
    """Provides test data for various testing scenarios"""
    
    def get_lambda_event(self, event_type: str) -> Dict[str, Any]:
        """Get sample Lambda event data based on event type"""
        event_generators = {
            'api_gateway': self._get_api_gateway_event,
            'api_gateway_post': self._get_api_gateway_post_event,
            's3_trigger': self._get_s3_trigger_event,
            'scheduled': self._get_scheduled_event,
            'sqs': self._get_sqs_event,
            'sns': self._get_sns_event,
            'email_sender': self._get_email_sender_event,
            'secrets_handler': self._get_secrets_handler_event,
            'process_email': self._get_process_email_event,
            'invoice_downloader': self._get_invoice_downloader_event
        }
        
        generator = event_generators.get(event_type, self._get_default_event)
        return generator()
    
    def get_lambda_context(self) -> ILambdaContext:
        """Get mock Lambda context"""
        return MockLambdaContext()
    
    def get_sample_document(self, document_type: str) -> Dict[str, Any]:
        """Get sample document data based on document type"""
        document_generators = {
            'email': self._get_email_document,
            'invoice': self._get_invoice_document,
            'bol': self._get_bol_document,
            'title': self._get_title_document,
            'vin': self._get_vin_document,
            'report_row': self._get_report_row_document,
            'status_config': self._get_status_config_document,
            'work_item': self._get_work_item_document
        }
        
        generator = document_generators.get(document_type, self._get_default_document)
        return generator()
    
    def _get_api_gateway_event(self) -> Dict[str, Any]:
        """Generate API Gateway event"""
        return {
            'version': '2.0',
            'routeKey': 'GET /test',
            'rawPath': '/test',
            'rawQueryString': 'param1=value1&param2=value2',
            'headers': {
                'accept': 'application/json',
                'content-type': 'application/json',
                'host': 'test.execute-api.us-east-1.amazonaws.com',
                'user-agent': 'test-agent/1.0',
                'x-forwarded-for': '127.0.0.1',
                'x-forwarded-port': '443',
                'x-forwarded-proto': 'https'
            },
            'queryStringParameters': {
                'param1': 'value1',
                'param2': 'value2'
            },
            'requestContext': {
                'accountId': '************',
                'apiId': 'test-api-id',
                'domainName': 'test.execute-api.us-east-1.amazonaws.com',
                'domainPrefix': 'test',
                'http': {
                    'method': 'GET',
                    'path': '/test',
                    'protocol': 'HTTP/1.1',
                    'sourceIp': '127.0.0.1',
                    'userAgent': 'test-agent/1.0'
                },
                'requestId': str(uuid.uuid4()),
                'routeKey': 'GET /test',
                'stage': 'test',
                'time': datetime.now().strftime('%d/%b/%Y:%H:%M:%S %z'),
                'timeEpoch': int(datetime.now().timestamp() * 1000)
            },
            'isBase64Encoded': False
        }
    
    def _get_api_gateway_post_event(self) -> Dict[str, Any]:
        """Generate API Gateway POST event"""
        event = self._get_api_gateway_event()
        event['routeKey'] = 'POST /test'
        event['requestContext']['http']['method'] = 'POST'
        event['requestContext']['routeKey'] = 'POST /test'
        event['body'] = json.dumps({'test': 'data', 'timestamp': datetime.now().isoformat()})
        return event
    
    def _get_s3_trigger_event(self) -> Dict[str, Any]:
        """Generate S3 trigger event"""
        return {
            'Records': [
                {
                    'eventVersion': '2.1',
                    'eventSource': 'aws:s3',
                    'eventTime': datetime.now().isoformat() + 'Z',
                    'eventName': 'ObjectCreated:Put',
                    'userIdentity': {
                        'principalId': 'test-principal'
                    },
                    'requestParameters': {
                        'sourceIPAddress': '127.0.0.1'
                    },
                    'responseElements': {
                        'x-amz-request-id': str(uuid.uuid4()),
                        'x-amz-id-2': str(uuid.uuid4())
                    },
                    's3': {
                        's3SchemaVersion': '1.0',
                        'configurationId': 'test-config',
                        'bucket': {
                            'name': 'test-bucket',
                            'ownerIdentity': {
                                'principalId': 'test-principal'
                            },
                            'arn': 'arn:aws:s3:::test-bucket'
                        },
                        'object': {
                            'key': 'test-folder/test-file.pdf',
                            'size': 1024,
                            'eTag': str(uuid.uuid4()),
                            'sequencer': str(uuid.uuid4())
                        }
                    }
                }
            ]
        }
    
    def _get_scheduled_event(self) -> Dict[str, Any]:
        """Generate CloudWatch scheduled event"""
        return {
            'id': str(uuid.uuid4()),
            'detail-type': 'Scheduled Event',
            'source': 'aws.events',
            'account': '************',
            'time': datetime.now().isoformat() + 'Z',
            'region': 'us-east-1',
            'detail': {},
            'resources': [
                'arn:aws:events:us-east-1:************:rule/test-rule'
            ]
        }
    
    def _get_sqs_event(self) -> Dict[str, Any]:
        """Generate SQS event"""
        return {
            'Records': [
                {
                    'messageId': str(uuid.uuid4()),
                    'receiptHandle': str(uuid.uuid4()),
                    'body': json.dumps({'test': 'message', 'timestamp': datetime.now().isoformat()}),
                    'attributes': {
                        'ApproximateReceiveCount': '1',
                        'SentTimestamp': str(int(datetime.now().timestamp() * 1000)),
                        'SenderId': 'test-sender',
                        'ApproximateFirstReceiveTimestamp': str(int(datetime.now().timestamp() * 1000))
                    },
                    'messageAttributes': {},
                    'md5OfBody': 'test-md5',
                    'eventSource': 'aws:sqs',
                    'eventSourceARN': 'arn:aws:sqs:us-east-1:************:test-queue',
                    'awsRegion': 'us-east-1'
                }
            ]
        }
    
    def _get_sns_event(self) -> Dict[str, Any]:
        """Generate SNS event"""
        return {
            'Records': [
                {
                    'EventSource': 'aws:sns',
                    'EventVersion': '1.0',
                    'EventSubscriptionArn': 'arn:aws:sns:us-east-1:************:test-topic:test-subscription',
                    'Sns': {
                        'Type': 'Notification',
                        'MessageId': str(uuid.uuid4()),
                        'TopicArn': 'arn:aws:sns:us-east-1:************:test-topic',
                        'Subject': 'Test Subject',
                        'Message': json.dumps({'test': 'notification', 'timestamp': datetime.now().isoformat()}),
                        'Timestamp': datetime.now().isoformat() + 'Z',
                        'SignatureVersion': '1',
                        'Signature': 'test-signature',
                        'SigningCertUrl': 'https://sns.us-east-1.amazonaws.com/test-cert.pem',
                        'UnsubscribeUrl': 'https://sns.us-east-1.amazonaws.com/test-unsubscribe',
                        'MessageAttributes': {}
                    }
                }
            ]
        }
    
    def _get_email_sender_event(self) -> Dict[str, Any]:
        """Generate email sender Lambda event"""
        return {
            'body': json.dumps({
                'subject': 'Test Email Subject',
                'content': '<html><body><h1>Test Email</h1><p>This is a test email.</p></body></html>',
                'attachments': [
                    {
                        'filename': 'test-document.pdf',
                        's3_bucket': 'test-bucket',
                        's3_key': 'documents/test-document.pdf'
                    }
                ]
            }),
            'headers': {
                'content-type': 'application/json'
            }
        }
    
    def _get_secrets_handler_event(self) -> Dict[str, Any]:
        """Generate secrets handler Lambda event"""
        return {
            'body': json.dumps({
                'action': 'get',
                'secret_data': {
                    'test-secret': ''
                }
            }),
            'headers': {
                'content-type': 'application/json'
            }
        }
    
    def _get_process_email_event(self) -> Dict[str, Any]:
        """Generate process email Lambda event"""
        return {
            'body': json.dumps({
                'email_id': str(uuid.uuid4()),
                'subject': 'Invoice from Vendor',
                'sender': '<EMAIL>',
                'attachments': ['invoice.pdf', 'bol.pdf']
            }),
            'headers': {
                'content-type': 'application/json'
            }
        }
    
    def _get_invoice_downloader_event(self) -> Dict[str, Any]:
        """Generate invoice downloader Lambda event"""
        return {
            'body': json.dumps({
                'vendor': 'test-vendor',
                'date_range': {
                    'start': (datetime.now() - timedelta(days=7)).isoformat(),
                    'end': datetime.now().isoformat()
                }
            }),
            'headers': {
                'content-type': 'application/json'
            }
        }
    
    def _get_default_event(self) -> Dict[str, Any]:
        """Generate default Lambda event"""
        return {
            'test': True,
            'timestamp': datetime.now().isoformat(),
            'request_id': str(uuid.uuid4())
        }
    
    def _get_email_document(self) -> Dict[str, Any]:
        """Generate sample email document"""
        return {
            '_id': str(uuid.uuid4()),
            'subject': 'Test Email Subject',
            'sender': '<EMAIL>',
            'recipients': ['<EMAIL>'],
            'body': 'This is a test email body.',
            'attachments': ['document1.pdf', 'document2.xlsx'],
            'received_date': datetime.now(),
            'processed': False,
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }
    
    def _get_invoice_document(self) -> Dict[str, Any]:
        """Generate sample invoice document"""
        return {
            '_id': str(uuid.uuid4()),
            'invoice_number': f'INV-{uuid.uuid4().hex[:8].upper()}',
            'vendor': 'Test Vendor Inc.',
            'amount': 1250.75,
            'currency': 'USD',
            'invoice_date': datetime.now().date().isoformat(),
            'due_date': (datetime.now() + timedelta(days=30)).date().isoformat(),
            'status': 'pending',
            'line_items': [
                {'description': 'Product A', 'quantity': 2, 'unit_price': 500.00},
                {'description': 'Product B', 'quantity': 1, 'unit_price': 250.75}
            ],
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }
    
    def _get_bol_document(self) -> Dict[str, Any]:
        """Generate sample Bill of Lading document"""
        return {
            '_id': str(uuid.uuid4()),
            'bol_number': f'BOL-{uuid.uuid4().hex[:8].upper()}',
            'shipper': 'Test Shipper LLC',
            'consignee': 'Test Consignee Inc.',
            'carrier': 'Test Carrier Corp',
            'origin': 'New York, NY',
            'destination': 'Los Angeles, CA',
            'ship_date': datetime.now().date().isoformat(),
            'delivery_date': (datetime.now() + timedelta(days=5)).date().isoformat(),
            'weight': 2500.0,
            'pieces': 10,
            'freight_charges': 850.00,
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }
    
    def _get_title_document(self) -> Dict[str, Any]:
        """Generate sample title document"""
        return {
            '_id': str(uuid.uuid4()),
            'title_number': f'TITLE-{uuid.uuid4().hex[:8].upper()}',
            'vin': f'1HGBH41JXMN{uuid.uuid4().hex[:6].upper()}',
            'make': 'Honda',
            'model': 'Accord',
            'year': 2023,
            'owner': 'John Doe',
            'lien_holder': 'Test Bank',
            'issue_date': datetime.now().date().isoformat(),
            'status': 'active',
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }
    
    def _get_vin_document(self) -> Dict[str, Any]:
        """Generate sample VIN document"""
        return {
            '_id': str(uuid.uuid4()),
            'vin': f'1HGBH41JXMN{uuid.uuid4().hex[:6].upper()}',
            'make': 'Honda',
            'model': 'Accord',
            'year': 2023,
            'trim': 'EX-L',
            'engine': '2.0L Turbo',
            'transmission': 'CVT',
            'color': 'Silver',
            'mileage': 15000,
            'condition': 'used',
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }
    
    def _get_report_row_document(self) -> Dict[str, Any]:
        """Generate sample report row document"""
        return {
            '_id': str(uuid.uuid4()),
            'report_id': str(uuid.uuid4()),
            'row_number': 1,
            'data': {
                'vin': f'1HGBH41JXMN{uuid.uuid4().hex[:6].upper()}',
                'make': 'Honda',
                'model': 'Accord',
                'year': 2023,
                'price': 25000.00
            },
            'processed': False,
            'errors': [],
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }
    
    def _get_status_config_document(self) -> Dict[str, Any]:
        """Generate sample status config document"""
        return {
            '_id': str(uuid.uuid4()),
            'app_id': 'test-app',
            'statuses': {
                'pending': {'color': 'yellow', 'description': 'Pending processing'},
                'processing': {'color': 'blue', 'description': 'Currently processing'},
                'completed': {'color': 'green', 'description': 'Processing completed'},
                'error': {'color': 'red', 'description': 'Error occurred'}
            },
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }
    
    def _get_work_item_document(self) -> Dict[str, Any]:
        """Generate sample work item document"""
        return {
            '_id': str(uuid.uuid4()),
            'work_item_id': str(uuid.uuid4()),
            'type': 'invoice_processing',
            'status': 'pending',
            'priority': 'normal',
            'assigned_to': 'system',
            'data': {
                'invoice_id': str(uuid.uuid4()),
                'vendor': 'Test Vendor'
            },
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }
    
    def _get_default_document(self) -> Dict[str, Any]:
        """Generate default document"""
        return {
            '_id': str(uuid.uuid4()),
            'type': 'test_document',
            'data': {'test': True},
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }


class MockLambdaContext(ILambdaContext):
    """Mock implementation of Lambda context"""
    
    def __init__(self, **kwargs):
        self.function_name = kwargs.get('function_name', 'test-function')
        self.function_version = kwargs.get('function_version', '$LATEST')
        self.invoked_function_arn = kwargs.get('invoked_function_arn', 'arn:aws:lambda:us-east-1:************:function:test-function')
        self.memory_limit_in_mb = kwargs.get('memory_limit_in_mb', 128)
        self.remaining_time_ms = kwargs.get('remaining_time_ms', 30000)
        self.aws_request_id = kwargs.get('aws_request_id', str(uuid.uuid4()))
        self.log_group_name = kwargs.get('log_group_name', '/aws/lambda/test-function')
        self.log_stream_name = kwargs.get('log_stream_name', f'2023/01/01/[$LATEST]{uuid.uuid4().hex[:8]}')
    
    def get_remaining_time_in_millis(self) -> int:
        """Get remaining execution time"""
        return self.remaining_time_ms
    
    def get_function_name(self) -> str:
        """Get Lambda function name"""
        return self.function_name
    
    def get_function_version(self) -> str:
        """Get Lambda function version"""
        return self.function_version
    
    def get_invoked_function_arn(self) -> str:
        """Get invoked function ARN"""
        return self.invoked_function_arn
    
    def get_memory_limit_in_mb(self) -> int:
        """Get memory limit"""
        return self.memory_limit_in_mb
    
    def get_aws_request_id(self) -> str:
        """Get AWS request ID"""
        return self.aws_request_id
