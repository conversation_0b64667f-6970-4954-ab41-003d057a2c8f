"""
Test helper utilities for common testing operations.
Provides utility functions for setup, teardown, and test data manipulation.
"""

import os
import json
import tempfile
import shutil
from typing import Dict, Any, List, Optional, Union
from unittest.mock import Mock, patch, MagicMock
from contextlib import contextmanager
import uuid
from datetime import datetime


class TestHelpers:
    """Collection of helper methods for testing"""
    
    @staticmethod
    def setup_test_environment(env_vars: Dict[str, str] = None) -> None:
        """Set up test environment variables"""
        default_env_vars = {
            'ENV': 'test',
            'AWS_DEFAULT_REGION': 'us-east-1',
            'MONGO_DATABASE': 'test_database',
            'REPORTS_EMAIL': '<EMAIL>',
            'REPORTER_EMAIL': '<EMAIL>',
            'BCC_EMAIL': '<EMAIL>',
            'ARIA_ENV': 'test'
        }
        
        if env_vars:
            default_env_vars.update(env_vars)
        
        for key, value in default_env_vars.items():
            os.environ.setdefault(key, value)
    
    @staticmethod
    def cleanup_test_environment(env_vars: List[str] = None) -> None:
        """Clean up test environment variables"""
        if env_vars:
            for var in env_vars:
                os.environ.pop(var, None)
    
    @staticmethod
    def create_temp_file(content: Union[str, bytes], suffix: str = '.tmp') -> str:
        """Create temporary file with content"""
        with tempfile.NamedTemporaryFile(mode='wb' if isinstance(content, bytes) else 'w', 
                                       suffix=suffix, delete=False) as f:
            f.write(content)
            return f.name
    
    @staticmethod
    def create_temp_directory() -> str:
        """Create temporary directory"""
        return tempfile.mkdtemp()
    
    @staticmethod
    def cleanup_temp_path(path: str) -> None:
        """Clean up temporary file or directory"""
        if os.path.isfile(path):
            os.unlink(path)
        elif os.path.isdir(path):
            shutil.rmtree(path)
    
    @staticmethod
    def mock_boto3_client(service_name: str, **kwargs) -> Mock:
        """Create mock boto3 client for specific service"""
        mock_client = Mock()
        
        if service_name == 'secretsmanager':
            mock_client.get_secret_value.return_value = {
                'SecretString': json.dumps({'test': 'secret'}),
                'ResponseMetadata': {'HTTPStatusCode': 200}
            }
            mock_client.create_secret.return_value = {
                'ResponseMetadata': {'HTTPStatusCode': 200}
            }
            mock_client.update_secret.return_value = {
                'ResponseMetadata': {'HTTPStatusCode': 200}
            }
            mock_client.delete_secret.return_value = {
                'ResponseMetadata': {'HTTPStatusCode': 200}
            }
        
        elif service_name == 's3':
            mock_body = Mock()
            mock_body.read.return_value = b'test content'
            
            mock_client.get_object.return_value = {
                'Body': mock_body,
                'ContentLength': 12,
                'ResponseMetadata': {'HTTPStatusCode': 200}
            }
            mock_client.put_object.return_value = {
                'ETag': '"test-etag"',
                'ResponseMetadata': {'HTTPStatusCode': 200}
            }
            mock_client.delete_object.return_value = {
                'ResponseMetadata': {'HTTPStatusCode': 204}
            }
            mock_client.list_objects_v2.return_value = {
                'Contents': [],
                'ResponseMetadata': {'HTTPStatusCode': 200}
            }
        
        elif service_name == 'ssm':
            mock_client.get_parameter.return_value = {
                'Parameter': {
                    'Value': json.dumps({'test': 'parameter'})
                },
                'ResponseMetadata': {'HTTPStatusCode': 200}
            }
        
        # Apply any custom kwargs
        for attr, value in kwargs.items():
            setattr(mock_client, attr, value)
        
        return mock_client
    
    @staticmethod
    def mock_pymongo_client(database_name: str = 'test_db') -> Mock:
        """Create mock PyMongo client"""
        mock_client = Mock()
        mock_db = Mock()
        mock_collection = Mock()
        
        # Set up collection operations
        mock_collection.insert_one.return_value = Mock(inserted_id=str(uuid.uuid4()))
        mock_collection.insert_many.return_value = Mock(inserted_ids=[str(uuid.uuid4()) for _ in range(3)])
        mock_collection.find_one.return_value = {'_id': str(uuid.uuid4()), 'test': 'data'}
        mock_collection.find.return_value = [{'_id': str(uuid.uuid4()), 'test': 'data'}]
        mock_collection.update_one.return_value = Mock(matched_count=1, modified_count=1)
        mock_collection.update_many.return_value = Mock(matched_count=2, modified_count=2)
        mock_collection.delete_one.return_value = Mock(deleted_count=1)
        mock_collection.delete_many.return_value = Mock(deleted_count=2)
        
        # Set up database operations
        mock_db.get_collection.return_value = mock_collection
        mock_db.__getitem__.return_value = mock_collection
        
        # Set up client operations
        mock_client.__getitem__.return_value = mock_db
        
        return mock_client
    
    @staticmethod
    @contextmanager
    def patch_boto3_client(service_name: str, **kwargs):
        """Context manager for patching boto3.client"""
        mock_client = TestHelpers.mock_boto3_client(service_name, **kwargs)
        
        with patch('boto3.client') as mock_boto3_client:
            mock_boto3_client.return_value = mock_client
            yield mock_client
    
    @staticmethod
    @contextmanager
    def patch_pymongo_client(**kwargs):
        """Context manager for patching PyMongo client"""
        mock_client = TestHelpers.mock_pymongo_client(**kwargs)
        
        with patch('pymongo.MongoClient') as mock_mongo_client:
            mock_mongo_client.return_value = mock_client
            yield mock_client
    
    @staticmethod
    def generate_test_data(data_type: str, count: int = 1, **kwargs) -> Union[Dict[str, Any], List[Dict[str, Any]]]:
        """Generate test data of specified type"""
        generators = {
            'email': TestHelpers._generate_email_data,
            'invoice': TestHelpers._generate_invoice_data,
            'bol': TestHelpers._generate_bol_data,
            'title': TestHelpers._generate_title_data,
            'vin': TestHelpers._generate_vin_data,
            'user': TestHelpers._generate_user_data,
            'document': TestHelpers._generate_document_data
        }
        
        generator = generators.get(data_type, TestHelpers._generate_generic_data)
        
        if count == 1:
            return generator(**kwargs)
        else:
            return [generator(**kwargs) for _ in range(count)]
    
    @staticmethod
    def _generate_email_data(**kwargs) -> Dict[str, Any]:
        """Generate test email data"""
        return {
            '_id': kwargs.get('_id', str(uuid.uuid4())),
            'subject': kwargs.get('subject', f'Test Email {uuid.uuid4().hex[:8]}'),
            'sender': kwargs.get('sender', '<EMAIL>'),
            'recipients': kwargs.get('recipients', ['<EMAIL>']),
            'body': kwargs.get('body', 'Test email body content'),
            'attachments': kwargs.get('attachments', []),
            'received_date': kwargs.get('received_date', datetime.now()),
            'processed': kwargs.get('processed', False),
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }
    
    @staticmethod
    def _generate_invoice_data(**kwargs) -> Dict[str, Any]:
        """Generate test invoice data"""
        return {
            '_id': kwargs.get('_id', str(uuid.uuid4())),
            'invoice_number': kwargs.get('invoice_number', f'INV-{uuid.uuid4().hex[:8].upper()}'),
            'vendor': kwargs.get('vendor', 'Test Vendor Inc.'),
            'amount': kwargs.get('amount', 1000.00),
            'currency': kwargs.get('currency', 'USD'),
            'invoice_date': kwargs.get('invoice_date', datetime.now().date().isoformat()),
            'status': kwargs.get('status', 'pending'),
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }
    
    @staticmethod
    def _generate_bol_data(**kwargs) -> Dict[str, Any]:
        """Generate test BOL data"""
        return {
            '_id': kwargs.get('_id', str(uuid.uuid4())),
            'bol_number': kwargs.get('bol_number', f'BOL-{uuid.uuid4().hex[:8].upper()}'),
            'shipper': kwargs.get('shipper', 'Test Shipper LLC'),
            'consignee': kwargs.get('consignee', 'Test Consignee Inc.'),
            'carrier': kwargs.get('carrier', 'Test Carrier Corp'),
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }
    
    @staticmethod
    def _generate_title_data(**kwargs) -> Dict[str, Any]:
        """Generate test title data"""
        return {
            '_id': kwargs.get('_id', str(uuid.uuid4())),
            'title_number': kwargs.get('title_number', f'TITLE-{uuid.uuid4().hex[:8].upper()}'),
            'vin': kwargs.get('vin', f'1HGBH41JXMN{uuid.uuid4().hex[:6].upper()}'),
            'make': kwargs.get('make', 'Honda'),
            'model': kwargs.get('model', 'Accord'),
            'year': kwargs.get('year', 2023),
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }
    
    @staticmethod
    def _generate_vin_data(**kwargs) -> Dict[str, Any]:
        """Generate test VIN data"""
        return {
            '_id': kwargs.get('_id', str(uuid.uuid4())),
            'vin': kwargs.get('vin', f'1HGBH41JXMN{uuid.uuid4().hex[:6].upper()}'),
            'make': kwargs.get('make', 'Honda'),
            'model': kwargs.get('model', 'Accord'),
            'year': kwargs.get('year', 2023),
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }
    
    @staticmethod
    def _generate_user_data(**kwargs) -> Dict[str, Any]:
        """Generate test user data"""
        return {
            '_id': kwargs.get('_id', str(uuid.uuid4())),
            'username': kwargs.get('username', f'user_{uuid.uuid4().hex[:8]}'),
            'email': kwargs.get('email', f'user_{uuid.uuid4().hex[:8]}@example.com'),
            'first_name': kwargs.get('first_name', 'Test'),
            'last_name': kwargs.get('last_name', 'User'),
            'active': kwargs.get('active', True),
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }
    
    @staticmethod
    def _generate_document_data(**kwargs) -> Dict[str, Any]:
        """Generate generic test document data"""
        return {
            '_id': kwargs.get('_id', str(uuid.uuid4())),
            'type': kwargs.get('type', 'test_document'),
            'name': kwargs.get('name', f'Test Document {uuid.uuid4().hex[:8]}'),
            'data': kwargs.get('data', {'test': True}),
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }
    
    @staticmethod
    def _generate_generic_data(**kwargs) -> Dict[str, Any]:
        """Generate generic test data"""
        return {
            '_id': str(uuid.uuid4()),
            'test_field': 'test_value',
            'timestamp': datetime.now().isoformat(),
            **kwargs
        }
    
    @staticmethod
    def assert_mock_called_with_subset(mock_obj, expected_subset: Dict[str, Any]) -> bool:
        """Assert mock was called with a subset of expected arguments"""
        if not mock_obj.called:
            return False
        
        call_args = mock_obj.call_args
        if call_args is None:
            return False
        
        # Check both args and kwargs
        all_args = {}
        if call_args.args:
            # Convert positional args to dict if possible
            all_args.update(dict(enumerate(call_args.args)))
        if call_args.kwargs:
            all_args.update(call_args.kwargs)
        
        # Check if all expected keys and values are present
        for key, expected_value in expected_subset.items():
            if key not in all_args or all_args[key] != expected_value:
                return False
        
        return True
    
    @staticmethod
    def create_lambda_event_from_template(template_name: str, **overrides) -> Dict[str, Any]:
        """Create Lambda event from predefined templates"""
        templates = {
            'api_gateway_get': {
                'version': '2.0',
                'routeKey': 'GET /test',
                'rawPath': '/test',
                'headers': {'content-type': 'application/json'},
                'requestContext': {
                    'http': {'method': 'GET'},
                    'requestId': str(uuid.uuid4())
                }
            },
            'api_gateway_post': {
                'version': '2.0',
                'routeKey': 'POST /test',
                'rawPath': '/test',
                'headers': {'content-type': 'application/json'},
                'body': '{}',
                'requestContext': {
                    'http': {'method': 'POST'},
                    'requestId': str(uuid.uuid4())
                }
            },
            's3_event': {
                'Records': [{
                    'eventSource': 'aws:s3',
                    'eventName': 'ObjectCreated:Put',
                    's3': {
                        'bucket': {'name': 'test-bucket'},
                        'object': {'key': 'test-key'}
                    }
                }]
            },
            'scheduled_event': {
                'source': 'aws.events',
                'detail-type': 'Scheduled Event',
                'detail': {}
            }
        }
        
        template = templates.get(template_name, {})
        
        # Apply overrides recursively
        def deep_update(base_dict, update_dict):
            for key, value in update_dict.items():
                if isinstance(value, dict) and key in base_dict and isinstance(base_dict[key], dict):
                    deep_update(base_dict[key], value)
                else:
                    base_dict[key] = value
        
        result = template.copy()
        deep_update(result, overrides)
        
        return result
