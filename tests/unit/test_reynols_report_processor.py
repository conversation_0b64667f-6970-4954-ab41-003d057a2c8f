"""
Unit tests for reynols_report_processor Lambda function.
Tests CSV report processing, VIN data handling, and MongoDB operations.
"""

import pytest
import json
import pandas as pd
from unittest.mock import patch, Mo<PERSON>, MagicMock, mock_open
from datetime import datetime
import os
import tempfile

from tests.base import BaseLambdaTest
from tests.utils import TestDataProvider, TestAssertions


class TestReynolsReportProcessorLambda(BaseLambdaTest):
    """Test class for reynols_report_processor Lambda function"""
    
    def _setup_dependencies(self):
        """Set up test dependencies"""
        self.test_data_provider = TestDataProvider()
        self.test_assertion = TestAssertions(self)
    
    def setUp(self):
        """Set up test environment"""
        super().setUp()
        # Set up additional environment variables specific to this Lambda
        os.environ.update({
            'BUCKET': 'test-bucket',
            'REYNOLS_REPORT_FOLDER': 'post-inventory-reports/',
            'USED_CARS_REPORTS': 'used-cars-reports/',
            'SFTP_CREDENTIALS': 'test-sftp-credentials',
            'FILE_SFTP_EXTENSION': '.csv',
            'ARIA_APP_ID': 'test-app-id',
            'ARIA_APP_ID_USED_CARS': 'test-used-cars-app-id'
        })
    
    @pytest.mark.unit
    @pytest.mark.aws
    @pytest.mark.mongo
    def test_lambda_handler_post_inventory_success(self, mock_secrets_manager, mock_s3_service, mock_mongo_database):
        """Test successful post-inventory report processing"""
        # Arrange
        mock_secrets_manager.add_test_secret('test-mongodb_uri', 'mongodb://localhost:27017/test_db')
        mock_secrets_manager.add_test_secret('test-sftp-credentials', {
            'hostname': 'test-sftp.example.com',
            'username': 'testuser',
            'password': 'testpass',
            'port': 22
        })
        
        # Create sample CSV data
        csv_data = """VIN,STORE,RECEIVED,STOCK #,MAKE,DESC,INV AMT,SLS COST,STK IN NOTES,SVC RO DATE,STAT-CODE,JRNL-PURCH-DATE-DR,ACCTG-MK,RO-CLOSE-DATE
1HGBH41JXMN123456,Store1,2023-01-01,12345,Honda,Accord,25000,23000,Test Notes,2023-01-15,A,2023-01-01,MK1,2023-01-16
2HGBH41JXMN789012,Store2,2023-01-02,67890,Toyota,Camry,28000,26000,Test Notes 2,2023-01-16,B,2023-01-02,MK2,2023-01-17"""
        
        mock_s3_service.add_test_object('test-bucket', 'post-inventory-reports/test-report.csv', csv_data.encode())
        
        event = {
            'body': json.dumps({
                'stage': 'post-inventory'
            })
        }
        context = self.test_data_provider.get_lambda_context()
        
        with patch('reynols_report_processor.lambda_function.get_report_file') as mock_get_report, \
             patch('reynols_report_processor.lambda_function.download_file_from_s3') as mock_download, \
             patch('reynols_report_processor.lambda_function.pd.read_csv') as mock_read_csv, \
             patch('reynols_report_processor.lambda_function.CrudReynolsReport') as mock_crud_report, \
             patch('reynols_report_processor.lambda_function.mark_missing_pending_invoice_vins_as_delete') as mock_mark_missing:
            
            # Configure mocks
            mock_get_report.return_value = 'test-report.csv'
            mock_download.return_value = True
            
            # Create mock DataFrame
            df_data = {
                'VIN': ['1HGBH41JXMN123456', '2HGBH41JXMN789012'],
                'STORE': ['Store1', 'Store2'],
                'RECEIVED': ['2023-01-01', '2023-01-02'],
                'STOCK #': ['12345', '67890'],
                'MAKE': ['Honda', 'Toyota'],
                'DESC': ['Accord', 'Camry'],
                'INV AMT': [25000, 28000],
                'SLS COST': [23000, 26000],
                'STK IN NOTES': ['Test Notes', 'Test Notes 2'],
                'SVC RO DATE': ['2023-01-15', '2023-01-16'],
                'STAT-CODE': ['A', 'B'],
                'JRNL-PURCH-DATE-DR': ['2023-01-01', '2023-01-02'],
                'ACCTG-MK': ['MK1', 'MK2'],
                'RO-CLOSE-DATE': ['2023-01-16', '2023-01-17']
            }
            mock_df = pd.DataFrame(df_data)
            mock_read_csv.return_value = mock_df
            
            # Configure CRUD mock
            mock_crud_instance = Mock()
            mock_crud_report.return_value = mock_crud_instance
            mock_crud_instance.find_report_row_by_vin.return_value = None  # New VIN
            
            # Act
            from reynols_report_processor.lambda_function import lambda_handler
            response = lambda_handler(event, context)
        
        # Assert
        self.test_assertion.assert_lambda_success_response(response)
        body = json.loads(response['body'])
        self.assertEqual(body['message'], 'Report processed correctly')
        
        # Verify CRUD operations were called
        self.assertEqual(mock_crud_instance.insert_row.call_count, 2)
        mock_mark_missing.assert_called_once()
    
    @pytest.mark.unit
    @pytest.mark.aws
    @pytest.mark.mongo
    def test_lambda_handler_used_cars_success(self, mock_secrets_manager, mock_s3_service, mock_mongo_database):
        """Test successful used-cars report processing"""
        # Arrange
        mock_secrets_manager.add_test_secret('test-mongodb_uri', 'mongodb://localhost:27017/test_db')
        mock_secrets_manager.add_test_secret('test-sftp-credentials', {
            'hostname': 'test-sftp.example.com',
            'username': 'testuser',
            'password': 'testpass',
            'port': 22
        })
        
        event = {
            'body': json.dumps({
                'stage': 'used-cars'
            })
        }
        context = self.test_data_provider.get_lambda_context()
        
        with patch('reynols_report_processor.lambda_function.get_report_file') as mock_get_report, \
             patch('reynols_report_processor.lambda_function.download_file_from_s3') as mock_download, \
             patch('reynols_report_processor.lambda_function.pd.read_csv') as mock_read_csv, \
             patch('reynols_report_processor.lambda_function.CrudReynolsReport') as mock_crud_report, \
             patch('reynols_report_processor.lambda_function.CrudInvoices') as mock_crud_invoices, \
             patch('reynols_report_processor.lambda_function.Vin') as mock_vin, \
             patch('reynols_report_processor.lambda_function.mark_missing_pending_invoice_vins_as_delete') as mock_mark_missing:
            
            # Configure mocks
            mock_get_report.return_value = 'test-used-cars-report.csv'
            mock_download.return_value = True
            
            # Create mock DataFrame for used cars
            df_data = {
                'VIN': ['1HGBH41JXMN123456', '2HGBH41JXMN789012'],
                'STORE': ['Store1', 'Store2'],
                'RECEIVED': ['2023-01-01', '2023-01-02'],
                'STOCK #': ['12345', '67890'],
                'MAKE': ['Honda', 'Toyota'],
                'INV-AMT': [25000, 28000],
                'SLS COST': [23000, 26000],
                'STK IN NOTES': ['Test Notes', 'Test Notes 2'],
                'ACCTG-MK': ['MK1', 'MK2'],
                'MEMO 2': ['Memo1', 'Memo2']
            }
            mock_df = pd.DataFrame(df_data)
            mock_read_csv.return_value = mock_df
            
            # Configure CRUD mocks
            mock_crud_report_instance = Mock()
            mock_crud_report.return_value = mock_crud_report_instance
            mock_crud_report_instance.find_report_row_by_vin.return_value = None
            
            mock_crud_invoices_instance = Mock()
            mock_crud_invoices.return_value = mock_crud_invoices_instance
            mock_crud_invoices_instance.find_invoice_used_car_by_vin_and_not_discarded.return_value = None
            
            # Configure VIN validation mock
            mock_vin_instance = Mock()
            mock_vin.return_value = mock_vin_instance
            mock_vin_instance.verify_checksum.return_value = True
            
            # Act
            from reynols_report_processor.lambda_function import lambda_handler
            response = lambda_handler(event, context)
        
        # Assert
        self.test_assertion.assert_lambda_success_response(response)
        body = json.loads(response['body'])
        self.assertEqual(body['message'], 'Report processed correctly')
        
        # Verify CRUD operations were called
        self.assertEqual(mock_crud_report_instance.insert_row_used_car.call_count, 2)
        mock_mark_missing.assert_called_once()
    
    @pytest.mark.unit
    def test_lambda_handler_missing_stage(self):
        """Test error handling when stage is missing"""
        # Arrange
        event = {
            'body': json.dumps({
                # Missing 'stage'
            })
        }
        context = self.test_data_provider.get_lambda_context()
        
        # Act
        from reynols_report_processor.lambda_function import lambda_handler
        response = lambda_handler(event, context)
        
        # Assert
        self.test_assertion.assert_lambda_error_response(response, 500)
        body = json.loads(response['body'])
        self.assertIn('error', body)
        self.assertIn('stage', body['error'].lower())
    
    @pytest.mark.unit
    def test_lambda_handler_invalid_stage(self):
        """Test error handling for invalid stage"""
        # Arrange
        event = {
            'body': json.dumps({
                'stage': 'invalid-stage'
            })
        }
        context = self.test_data_provider.get_lambda_context()
        
        with patch('reynols_report_processor.lambda_function.get_report_file') as mock_get_report:
            mock_get_report.return_value = 'test-report.csv'
            
            # Act
            from reynols_report_processor.lambda_function import lambda_handler
            response = lambda_handler(event, context)
        
        # Assert
        self.test_assertion.assert_lambda_success_response(response)  # Function still succeeds but doesn't process
    
    @pytest.mark.unit
    @pytest.mark.aws
    def test_lambda_handler_sftp_error(self, mock_secrets_manager):
        """Test error handling when SFTP fails"""
        # Arrange
        mock_secrets_manager.add_test_secret('test-mongodb_uri', 'mongodb://localhost:27017/test_db')
        mock_secrets_manager.add_test_secret('test-sftp-credentials', {
            'hostname': 'test-sftp.example.com',
            'username': 'testuser',
            'password': 'testpass',
            'port': 22
        })
        
        event = {
            'body': json.dumps({
                'stage': 'post-inventory'
            })
        }
        context = self.test_data_provider.get_lambda_context()
        
        with patch('reynols_report_processor.lambda_function.get_report_file') as mock_get_report:
            # Simulate SFTP failure
            mock_get_report.side_effect = Exception('SFTP connection failed')
            
            # Act & Assert
            from reynols_report_processor.lambda_function import lambda_handler
            with self.assertRaises(Exception) as cm:
                lambda_handler(event, context)
            
            self.assertIn('Error processing report', str(cm.exception))
    
    @pytest.mark.unit
    @pytest.mark.mongo
    def test_lambda_handler_existing_vin_update(self, mock_secrets_manager, mock_mongo_database):
        """Test updating existing VIN in post-inventory flow"""
        # Arrange
        mock_secrets_manager.add_test_secret('test-mongodb_uri', 'mongodb://localhost:27017/test_db')
        mock_secrets_manager.add_test_secret('test-sftp-credentials', {
            'hostname': 'test-sftp.example.com',
            'username': 'testuser',
            'password': 'testpass',
            'port': 22
        })
        
        event = {
            'body': json.dumps({
                'stage': 'post-inventory'
            })
        }
        context = self.test_data_provider.get_lambda_context()
        
        with patch('reynols_report_processor.lambda_function.get_report_file') as mock_get_report, \
             patch('reynols_report_processor.lambda_function.download_file_from_s3') as mock_download, \
             patch('reynols_report_processor.lambda_function.pd.read_csv') as mock_read_csv, \
             patch('reynols_report_processor.lambda_function.CrudReynolsReport') as mock_crud_report, \
             patch('reynols_report_processor.lambda_function.mark_missing_pending_invoice_vins_as_delete') as mock_mark_missing:
            
            # Configure mocks
            mock_get_report.return_value = 'test-report.csv'
            mock_download.return_value = True
            
            # Create mock DataFrame with single VIN
            df_data = {
                'VIN': ['1HGBH41JXMN123456'],
                'STORE': ['Store1'],
                'RECEIVED': ['2023-01-01'],
                'STOCK #': ['12345'],
                'MAKE': ['Honda'],
                'DESC': ['Accord'],
                'INV AMT': [25000],
                'SLS COST': [23000],
                'STK IN NOTES': ['Test Notes'],
                'SVC RO DATE': ['2023-01-15'],
                'STAT-CODE': ['A'],
                'JRNL-PURCH-DATE-DR': ['2023-01-01'],
                'ACCTG-MK': ['MK1'],
                'RO-CLOSE-DATE': ['2023-01-16']
            }
            mock_df = pd.DataFrame(df_data)
            mock_read_csv.return_value = mock_df
            
            # Configure CRUD mock to return existing VIN
            mock_crud_instance = Mock()
            mock_crud_report.return_value = mock_crud_instance
            existing_vin_data = {'vin': '1HGBH41JXMN123456', 'flows': {}}
            mock_crud_instance.find_report_row_by_vin.return_value = existing_vin_data
            
            # Act
            from reynols_report_processor.lambda_function import lambda_handler
            response = lambda_handler(event, context)
        
        # Assert
        self.test_assertion.assert_lambda_success_response(response)
        
        # Verify update was called instead of insert
        mock_crud_instance.update_row_post_inventory.assert_called_once()
        mock_crud_instance.insert_row.assert_not_called()
    
    @pytest.mark.unit
    @pytest.mark.mongo
    def test_lambda_handler_skip_am_vins(self, mock_secrets_manager, mock_mongo_database):
        """Test skipping VINs that start with 'AM' (sold vehicles)"""
        # Arrange
        mock_secrets_manager.add_test_secret('test-mongodb_uri', 'mongodb://localhost:27017/test_db')
        mock_secrets_manager.add_test_secret('test-sftp-credentials', {
            'hostname': 'test-sftp.example.com',
            'username': 'testuser',
            'password': 'testpass',
            'port': 22
        })
        
        event = {
            'body': json.dumps({
                'stage': 'post-inventory'
            })
        }
        context = self.test_data_provider.get_lambda_context()
        
        with patch('reynols_report_processor.lambda_function.get_report_file') as mock_get_report, \
             patch('reynols_report_processor.lambda_function.download_file_from_s3') as mock_download, \
             patch('reynols_report_processor.lambda_function.pd.read_csv') as mock_read_csv, \
             patch('reynols_report_processor.lambda_function.CrudReynolsReport') as mock_crud_report, \
             patch('reynols_report_processor.lambda_function.mark_missing_pending_invoice_vins_as_delete') as mock_mark_missing:
            
            # Configure mocks
            mock_get_report.return_value = 'test-report.csv'
            mock_download.return_value = True
            
            # Create mock DataFrame with AM VIN (should be skipped)
            df_data = {
                'VIN': ['AM1234567890123456', '1HGBH41JXMN123456'],  # First VIN starts with AM
                'STORE': ['Store1', 'Store2'],
                'RECEIVED': ['2023-01-01', '2023-01-02'],
                'STOCK #': ['12345', '67890'],
                'MAKE': ['Honda', 'Toyota'],
                'DESC': ['Accord', 'Camry'],
                'INV AMT': [25000, 28000],
                'SLS COST': [23000, 26000],
                'STK IN NOTES': ['Test Notes', 'Test Notes 2'],
                'SVC RO DATE': ['2023-01-15', '2023-01-16'],
                'STAT-CODE': ['A', 'B'],
                'JRNL-PURCH-DATE-DR': ['2023-01-01', '2023-01-02'],
                'ACCTG-MK': ['MK1', 'MK2'],
                'RO-CLOSE-DATE': ['2023-01-16', '2023-01-17']
            }
            mock_df = pd.DataFrame(df_data)
            mock_read_csv.return_value = mock_df
            
            # Configure CRUD mock
            mock_crud_instance = Mock()
            mock_crud_report.return_value = mock_crud_instance
            mock_crud_instance.find_report_row_by_vin.return_value = None
            
            # Act
            from reynols_report_processor.lambda_function import lambda_handler
            response = lambda_handler(event, context)
        
        # Assert
        self.test_assertion.assert_lambda_success_response(response)
        
        # Verify only one insert was called (AM VIN was skipped)
        self.assertEqual(mock_crud_instance.insert_row.call_count, 1)
        
        # Verify the correct VIN was processed
        call_args = mock_crud_instance.insert_row.call_args[0]
        processed_row = call_args[0]
        self.assertEqual(processed_row['VIN'], '1HGBH41JXMN123456')
    
    @pytest.mark.unit
    def test_map_store_val_function(self):
        """Test the map_store_val helper function"""
        # This would test the store mapping logic if it's extracted as a separate function
        # For now, we'll test it as part of the main flow
        pass
    
    @pytest.mark.unit
    @pytest.mark.mongo
    def test_mark_work_item_in_aria_as_deleted(self, mock_secrets_manager, mock_mongo_database):
        """Test marking work item as deleted in ARIA"""
        # Arrange
        mock_secrets_manager.add_test_secret('test-mongodb_uri', 'mongodb://localhost:27017/test_db')
        mock_secrets_manager.add_test_secret('test-aria_cm_tokens', {
            'test': {
                'url': 'https://test-aria.example.com',
                'token': 'Bearer test-token'
            }
        })
        
        # Add test status config to mock database
        mock_mongo_database.add_test_document('aria_status', {
            'app_id': 'test-app-id',
            'status': {
                'delete-uuid': {'label': 'Delete'},
                'pending-uuid': {'label': 'Pending'}
            }
        })
        
        with patch('reynols_report_processor.lambda_function.AriaUtils') as mock_aria_utils:
            mock_aria_instance = Mock()
            mock_aria_utils.return_value = mock_aria_instance
            mock_aria_instance.send_bre_request.return_value = {'status': 'success'}
            
            # Act
            from reynols_report_processor.lambda_function import mark_work_item_in_aria_as_deleted
            result = mark_work_item_in_aria_as_deleted('test-app-id', 'test-work-item-id')
        
        # Assert
        self.assertEqual(result, {'status': 'success'})
        mock_aria_instance.construct_reply_bre_request.assert_called_once()
        mock_aria_instance.send_bre_request.assert_called_once()


class TestReynolsReportProcessorHelpers(BaseLambdaTest):
    """Test helper functions used by reynols_report_processor"""
    
    def _setup_dependencies(self):
        """Set up test dependencies"""
        pass
    
    @pytest.mark.unit
    @pytest.mark.mongo
    def test_mark_missing_pending_invoice_vins_as_delete(self, mock_mongo_database):
        """Test marking missing VINs as deleted"""
        # Arrange
        vins_in_report = ['VIN1', 'VIN2', 'VIN3']
        stage = 'post-inventory'
        
        # Mock MongoDB with some VINs that should be marked as deleted
        mock_mongo = Mock()
        mock_mongo.find.return_value = [
            {'vin': 'VIN4', 'flows': {'post-inventory': {'docs': {'invoice': {'aria_data': {'aria_wi_id': 'wi-1'}}}}}},
            {'vin': 'VIN5', 'flows': {'post-inventory': {'docs': {'invoice': {'aria_data': {'aria_wi_id': 'wi-2'}}}}}}
        ]
        
        filter_query = {
            "flows.post-inventory.docs.invoice.aria_data.aria_wi_id": {"$exists": True},
            "flows.post-inventory.docs.invoice.aria_data.status": {"$exists": False},
        }
        
        with patch('reynols_report_processor.lambda_function.mark_work_item_in_aria_as_deleted') as mock_mark_deleted:
            mock_mark_deleted.return_value = {'status': 'success'}
            
            # Act
            from reynols_report_processor.lambda_function import mark_missing_pending_invoice_vins_as_delete
            mark_missing_pending_invoice_vins_as_delete(vins_in_report, stage, mock_mongo, filter_query)
        
        # Assert
        # Verify that VINs not in report were marked as deleted
        self.assertEqual(mock_mark_deleted.call_count, 2)
    
    @pytest.mark.unit
    @pytest.mark.aws
    def test_boto3_utils_integration(self, mock_s3_service, mock_secrets_manager):
        """Test integration with boto3_utils functions"""
        # Arrange
        mock_secrets_manager.add_test_secret('test-secret', {'key': 'value'})
        mock_s3_service.add_test_object('test-bucket', 'reports/test.csv', b'test,data\n1,2')
        
        # Test get_secret
        with patch('reynols_report_processor.boto3_utils.get_secret') as mock_get_secret:
            mock_get_secret.return_value = {'key': 'value'}
            
            from reynols_report_processor.boto3_utils import get_secret
            result = get_secret('test-secret')
            
            self.assertEqual(result, {'key': 'value'})
        
        # Test download_file_from_s3
        with patch('reynols_report_processor.boto3_utils.download_file_from_s3') as mock_download:
            mock_download.return_value = True
            
            from reynols_report_processor.boto3_utils import download_file_from_s3
            result = download_file_from_s3('s3://test-bucket/reports/test.csv', '/tmp/test.csv')
            
            self.assertTrue(result)
    
    @pytest.mark.unit
    @pytest.mark.aws
    def test_sftp_utils_integration(self, mock_secrets_manager):
        """Test integration with SFTP utilities"""
        # Arrange
        mock_secrets_manager.add_test_secret('test-sftp-credentials', {
            'hostname': 'test-sftp.example.com',
            'username': 'testuser',
            'password': 'testpass',
            'port': 22
        })
        
        with patch('reynols_report_processor.sftp_utils.get_report_file') as mock_get_report:
            mock_get_report.return_value = 'downloaded-report.csv'
            
            # Act
            from reynols_report_processor.sftp_utils import get_report_file
            result = get_report_file('test-bucket', 'reports/', report_name_start_with='test')
            
            # Assert
            self.assertEqual(result, 'downloaded-report.csv')
            mock_get_report.assert_called_once_with('test-bucket', 'reports/', report_name_start_with='test')


class TestReynolsReportProcessorIntegration(BaseLambdaTest):
    """Integration tests for reynols_report_processor with real-like data"""
    
    def _setup_dependencies(self):
        """Set up test dependencies"""
        self.test_data_provider = TestDataProvider()
        self.test_assertion = TestAssertions(self)
    
    @pytest.mark.integration
    @pytest.mark.slow
    def test_end_to_end_post_inventory_processing(self, mock_secrets_manager, mock_s3_service, mock_mongo_database):
        """Test complete end-to-end post-inventory processing"""
        # Arrange
        mock_secrets_manager.add_test_secret('test-mongodb_uri', 'mongodb://localhost:27017/test_db')
        mock_secrets_manager.add_test_secret('test-sftp-credentials', {
            'hostname': 'test-sftp.example.com',
            'username': 'testuser',
            'password': 'testpass',
            'port': 22
        })
        
        # Create realistic CSV data
        csv_content = """VIN,STORE,RECEIVED,STOCK #,MAKE,DESC,INV AMT,SLS COST,STK IN NOTES,SVC RO DATE,STAT-CODE,JRNL-PURCH-DATE-DR,ACCTG-MK,RO-CLOSE-DATE
1HGBH41JXMN123456,CAD,2023-01-01,12345,Honda,2023 Accord EX-L,25000.00,23000.00,Clean title,2023-01-15,A,2023-01-01,01,2023-01-16
2T1BURHE0JC123456,FOR,2023-01-02,67890,Toyota,2023 Camry LE,28000.00,26000.00,Minor scratches,2023-01-16,B,2023-01-02,02,2023-01-17
3FADP4FJ8KM123456,HON,2023-01-03,11111,Ford,2023 Focus SE,22000.00,20000.00,Good condition,,C,2023-01-03,03,
AM1234567890123456,JLRG,2023-01-04,22222,Jaguar,2023 XF Premium,45000.00,43000.00,Sold vehicle,2023-01-17,D,2023-01-04,04,2023-01-18"""
        
        mock_s3_service.add_test_object('test-bucket', 'post-inventory-reports/test-report.csv', csv_content.encode())
        
        event = {
            'body': json.dumps({
                'stage': 'post-inventory'
            })
        }
        context = self.test_data_provider.get_lambda_context()
        
        with patch('reynols_report_processor.lambda_function.get_report_file') as mock_get_report, \
             patch('reynols_report_processor.lambda_function.download_file_from_s3') as mock_download, \
             patch('reynols_report_processor.lambda_function.pd.read_csv') as mock_read_csv, \
             patch('reynols_report_processor.lambda_function.CrudReynolsReport') as mock_crud_report, \
             patch('reynols_report_processor.lambda_function.mark_missing_pending_invoice_vins_as_delete') as mock_mark_missing:
            
            # Configure mocks with realistic data
            mock_get_report.return_value = 'test-report.csv'
            mock_download.return_value = True
            
            # Parse the CSV content into DataFrame
            import io
            df = pd.read_csv(io.StringIO(csv_content))
            mock_read_csv.return_value = df
            
            # Configure CRUD mock
            mock_crud_instance = Mock()
            mock_crud_report.return_value = mock_crud_instance
            mock_crud_instance.find_report_row_by_vin.return_value = None
            
            # Act
            from reynols_report_processor.lambda_function import lambda_handler
            response = lambda_handler(event, context)
        
        # Assert
        self.test_assertion.assert_lambda_success_response(response)
        
        # Verify that 3 VINs were processed (AM VIN was skipped)
        self.assertEqual(mock_crud_instance.insert_row.call_count, 3)
        
        # Verify the VINs that were processed
        processed_vins = []
        for call in mock_crud_instance.insert_row.call_args_list:
            row_data = call[0][0]
            processed_vins.append(row_data['VIN'])
        
        expected_vins = ['1HGBH41JXMN123456', '2T1BURHE0JC123456', '3FADP4FJ8KM123456']
        self.assertEqual(sorted(processed_vins), sorted(expected_vins))
        
        # Verify AM VIN was not processed
        self.assertNotIn('AM1234567890123456', processed_vins)


class TestReynolsReportProcessorErrorHandling(BaseLambdaTest):
    """Test error handling scenarios for reynols_report_processor"""

    def _setup_dependencies(self):
        """Set up test dependencies"""
        self.test_data_provider = TestDataProvider()
        self.test_assertion = TestAssertions(self)

    @pytest.mark.unit
    @pytest.mark.error_handling
    def test_lambda_handler_malformed_json(self):
        """Test error handling for malformed JSON in event body"""
        # Arrange
        event = {
            'body': '{"stage": "post-inventory"'  # Missing closing brace
        }
        context = self.test_data_provider.get_lambda_context()

        # Act
        from reynols_report_processor.lambda_function import lambda_handler
        response = lambda_handler(event, context)

        # Assert
        self.test_assertion.assert_lambda_error_response(response, 500)
        body = json.loads(response['body'])
        self.assertIn('error', body)

    @pytest.mark.unit
    @pytest.mark.error_handling
    def test_lambda_handler_missing_body(self):
        """Test error handling when event body is missing"""
        # Arrange
        event = {}  # No body
        context = self.test_data_provider.get_lambda_context()

        # Act
        from reynols_report_processor.lambda_function import lambda_handler
        response = lambda_handler(event, context)

        # Assert
        self.test_assertion.assert_lambda_error_response(response, 500)
        body = json.loads(response['body'])
        self.assertIn('error', body)

    @pytest.mark.unit
    @pytest.mark.error_handling
    @pytest.mark.mongo
    def test_lambda_handler_database_connection_error(self, mock_secrets_manager):
        """Test error handling when database connection fails"""
        # Arrange
        mock_secrets_manager.add_test_secret('test-mongodb_uri', 'mongodb://invalid:27017/test_db')

        event = {
            'body': json.dumps({
                'stage': 'post-inventory'
            })
        }
        context = self.test_data_provider.get_lambda_context()

        with patch('reynols_report_processor.lambda_function.CrudReynolsReport') as mock_crud_report:
            # Simulate database connection error
            mock_crud_report.side_effect = Exception('Database connection failed')

            # Act & Assert
            from reynols_report_processor.lambda_function import lambda_handler
            with self.assertRaises(Exception) as cm:
                lambda_handler(event, context)

            self.assertIn('Error processing report', str(cm.exception))

    @pytest.mark.unit
    @pytest.mark.error_handling
    def test_lambda_handler_csv_parsing_error(self, mock_secrets_manager):
        """Test error handling when CSV parsing fails"""
        # Arrange
        mock_secrets_manager.add_test_secret('test-mongodb_uri', 'mongodb://localhost:27017/test_db')
        mock_secrets_manager.add_test_secret('test-sftp-credentials', {
            'hostname': 'test-sftp.example.com',
            'username': 'testuser',
            'password': 'testpass',
            'port': 22
        })

        event = {
            'body': json.dumps({
                'stage': 'post-inventory'
            })
        }
        context = self.test_data_provider.get_lambda_context()

        with patch('reynols_report_processor.lambda_function.get_report_file') as mock_get_report, \
             patch('reynols_report_processor.lambda_function.download_file_from_s3') as mock_download, \
             patch('reynols_report_processor.lambda_function.pd.read_csv') as mock_read_csv:

            # Configure mocks
            mock_get_report.return_value = 'test-report.csv'
            mock_download.return_value = True

            # Simulate CSV parsing error
            mock_read_csv.side_effect = pd.errors.EmptyDataError('No columns to parse from file')

            # Act & Assert
            from reynols_report_processor.lambda_function import lambda_handler
            with self.assertRaises(Exception) as cm:
                lambda_handler(event, context)

            self.assertIn('Error processing report', str(cm.exception))

    @pytest.mark.unit
    @pytest.mark.error_handling
    def test_lambda_handler_empty_csv_file(self, mock_secrets_manager):
        """Test handling of empty CSV file"""
        # Arrange
        mock_secrets_manager.add_test_secret('test-mongodb_uri', 'mongodb://localhost:27017/test_db')
        mock_secrets_manager.add_test_secret('test-sftp-credentials', {
            'hostname': 'test-sftp.example.com',
            'username': 'testuser',
            'password': 'testpass',
            'port': 22
        })

        event = {
            'body': json.dumps({
                'stage': 'post-inventory'
            })
        }
        context = self.test_data_provider.get_lambda_context()

        with patch('reynols_report_processor.lambda_function.get_report_file') as mock_get_report, \
             patch('reynols_report_processor.lambda_function.download_file_from_s3') as mock_download, \
             patch('reynols_report_processor.lambda_function.pd.read_csv') as mock_read_csv, \
             patch('reynols_report_processor.lambda_function.CrudReynolsReport') as mock_crud_report, \
             patch('reynols_report_processor.lambda_function.mark_missing_pending_invoice_vins_as_delete') as mock_mark_missing:

            # Configure mocks
            mock_get_report.return_value = 'test-report.csv'
            mock_download.return_value = True

            # Create empty DataFrame
            mock_df = pd.DataFrame()
            mock_read_csv.return_value = mock_df

            # Configure CRUD mock
            mock_crud_instance = Mock()
            mock_crud_report.return_value = mock_crud_instance

            # Act
            from reynols_report_processor.lambda_function import lambda_handler
            response = lambda_handler(event, context)

        # Assert
        self.test_assertion.assert_lambda_success_response(response)

        # Verify no inserts were made for empty file
        mock_crud_instance.insert_row.assert_not_called()
        mock_crud_instance.insert_row_used_car.assert_not_called()

    @pytest.mark.unit
    @pytest.mark.error_handling
    def test_lambda_handler_invalid_vin_format(self, mock_secrets_manager):
        """Test handling of invalid VIN formats in used-cars flow"""
        # Arrange
        mock_secrets_manager.add_test_secret('test-mongodb_uri', 'mongodb://localhost:27017/test_db')
        mock_secrets_manager.add_test_secret('test-sftp-credentials', {
            'hostname': 'test-sftp.example.com',
            'username': 'testuser',
            'password': 'testpass',
            'port': 22
        })

        event = {
            'body': json.dumps({
                'stage': 'used-cars'
            })
        }
        context = self.test_data_provider.get_lambda_context()

        with patch('reynols_report_processor.lambda_function.get_report_file') as mock_get_report, \
             patch('reynols_report_processor.lambda_function.download_file_from_s3') as mock_download, \
             patch('reynols_report_processor.lambda_function.pd.read_csv') as mock_read_csv, \
             patch('reynols_report_processor.lambda_function.CrudReynolsReport') as mock_crud_report, \
             patch('reynols_report_processor.lambda_function.CrudInvoices') as mock_crud_invoices, \
             patch('reynols_report_processor.lambda_function.Vin') as mock_vin, \
             patch('reynols_report_processor.lambda_function.mark_missing_pending_invoice_vins_as_delete') as mock_mark_missing:

            # Configure mocks
            mock_get_report.return_value = 'test-used-cars-report.csv'
            mock_download.return_value = True

            # Create mock DataFrame with invalid VIN
            df_data = {
                'VIN': ['INVALID_VIN_123', '1HGBH41JXMN123456'],  # First VIN is invalid
                'STORE': ['Store1', 'Store2'],
                'RECEIVED': ['2023-01-01', '2023-01-02'],
                'STOCK #': ['12345', '67890'],
                'MAKE': ['Honda', 'Toyota'],
                'INV-AMT': [25000, 28000],
                'SLS COST': [23000, 26000],
                'STK IN NOTES': ['Test Notes', 'Test Notes 2'],
                'ACCTG-MK': ['MK1', 'MK2'],
                'MEMO 2': ['Memo1', 'Memo2']
            }
            mock_df = pd.DataFrame(df_data)
            mock_read_csv.return_value = mock_df

            # Configure CRUD mocks
            mock_crud_report_instance = Mock()
            mock_crud_report.return_value = mock_crud_report_instance
            mock_crud_report_instance.find_report_row_by_vin.return_value = None

            mock_crud_invoices_instance = Mock()
            mock_crud_invoices.return_value = mock_crud_invoices_instance
            mock_crud_invoices_instance.find_invoice_used_car_by_vin_and_not_discarded.return_value = None

            # Configure VIN validation mock - first VIN fails, second passes
            def mock_vin_side_effect(vin):
                mock_vin_instance = Mock()
                if vin == 'INVALID_VIN_123':
                    mock_vin_instance.verify_checksum.return_value = False
                else:
                    mock_vin_instance.verify_checksum.return_value = True
                return mock_vin_instance

            mock_vin.side_effect = mock_vin_side_effect

            # Act
            from reynols_report_processor.lambda_function import lambda_handler
            response = lambda_handler(event, context)

        # Assert
        self.test_assertion.assert_lambda_success_response(response)

        # Verify only valid VIN was processed
        self.assertEqual(mock_crud_report_instance.insert_row_used_car.call_count, 1)

        # Verify the correct VIN was processed
        call_args = mock_crud_report_instance.insert_row_used_car.call_args[0]
        processed_row = call_args[0]
        self.assertEqual(processed_row['VIN'], '1HGBH41JXMN123456')


class TestReynolsReportProcessorDataValidation(BaseLambdaTest):
    """Test data validation and transformation in reynols_report_processor"""

    def _setup_dependencies(self):
        """Set up test dependencies"""
        self.test_data_provider = TestDataProvider()
        self.test_assertion = TestAssertions(self)

    @pytest.mark.unit
    @pytest.mark.data_validation
    def test_store_mapping_validation(self, mock_secrets_manager):
        """Test store code mapping and validation"""
        # Arrange
        mock_secrets_manager.add_test_secret('test-mongodb_uri', 'mongodb://localhost:27017/test_db')
        mock_secrets_manager.add_test_secret('test-sftp-credentials', {
            'hostname': 'test-sftp.example.com',
            'username': 'testuser',
            'password': 'testpass',
            'port': 22
        })

        event = {
            'body': json.dumps({
                'stage': 'post-inventory'
            })
        }
        context = self.test_data_provider.get_lambda_context()

        with patch('reynols_report_processor.lambda_function.get_report_file') as mock_get_report, \
             patch('reynols_report_processor.lambda_function.download_file_from_s3') as mock_download, \
             patch('reynols_report_processor.lambda_function.pd.read_csv') as mock_read_csv, \
             patch('reynols_report_processor.lambda_function.CrudReynolsReport') as mock_crud_report, \
             patch('reynols_report_processor.lambda_function.mark_missing_pending_invoice_vins_as_delete') as mock_mark_missing:

            # Configure mocks
            mock_get_report.return_value = 'test-report.csv'
            mock_download.return_value = True

            # Create mock DataFrame with various store codes
            df_data = {
                'VIN': ['1HGBH41JXMN123456', '2HGBH41JXMN789012', '3HGBH41JXMN345678'],
                'STORE': ['CAD', 'FOR', 'UNKNOWN'],  # Test known and unknown store codes
                'RECEIVED': ['2023-01-01', '2023-01-02', '2023-01-03'],
                'STOCK #': ['12345', '67890', '11111'],
                'MAKE': ['Honda', 'Toyota', 'Ford'],
                'DESC': ['Accord', 'Camry', 'Focus'],
                'INV AMT': [25000, 28000, 22000],
                'SLS COST': [23000, 26000, 20000],
                'STK IN NOTES': ['Test Notes', 'Test Notes 2', 'Test Notes 3'],
                'SVC RO DATE': ['2023-01-15', '2023-01-16', '2023-01-17'],
                'STAT-CODE': ['A', 'B', 'C'],
                'JRNL-PURCH-DATE-DR': ['2023-01-01', '2023-01-02', '2023-01-03'],
                'ACCTG-MK': ['MK1', 'MK2', 'MK3'],
                'RO-CLOSE-DATE': ['2023-01-16', '2023-01-17', '2023-01-18']
            }
            mock_df = pd.DataFrame(df_data)
            mock_read_csv.return_value = mock_df

            # Configure CRUD mock
            mock_crud_instance = Mock()
            mock_crud_report.return_value = mock_crud_instance
            mock_crud_instance.find_report_row_by_vin.return_value = None

            # Act
            from reynols_report_processor.lambda_function import lambda_handler
            response = lambda_handler(event, context)

        # Assert
        self.test_assertion.assert_lambda_success_response(response)

        # Verify all VINs were processed regardless of store code
        self.assertEqual(mock_crud_instance.insert_row.call_count, 3)

        # Verify store mapping was applied correctly
        for call in mock_crud_instance.insert_row.call_args_list:
            row_data = call[0][0]
            store_code = row_data['VIN']  # This would need to be adjusted based on actual store mapping logic

    @pytest.mark.unit
    @pytest.mark.data_validation
    def test_date_field_handling(self, mock_secrets_manager):
        """Test handling of date fields including empty/null dates"""
        # Arrange
        mock_secrets_manager.add_test_secret('test-mongodb_uri', 'mongodb://localhost:27017/test_db')
        mock_secrets_manager.add_test_secret('test-sftp-credentials', {
            'hostname': 'test-sftp.example.com',
            'username': 'testuser',
            'password': 'testpass',
            'port': 22
        })

        event = {
            'body': json.dumps({
                'stage': 'post-inventory'
            })
        }
        context = self.test_data_provider.get_lambda_context()

        with patch('reynols_report_processor.lambda_function.get_report_file') as mock_get_report, \
             patch('reynols_report_processor.lambda_function.download_file_from_s3') as mock_download, \
             patch('reynols_report_processor.lambda_function.pd.read_csv') as mock_read_csv, \
             patch('reynols_report_processor.lambda_function.CrudReynolsReport') as mock_crud_report, \
             patch('reynols_report_processor.lambda_function.mark_missing_pending_invoice_vins_as_delete') as mock_mark_missing:

            # Configure mocks
            mock_get_report.return_value = 'test-report.csv'
            mock_download.return_value = True

            # Create mock DataFrame with various date scenarios
            df_data = {
                'VIN': ['1HGBH41JXMN123456', '2HGBH41JXMN789012'],
                'STORE': ['CAD', 'FOR'],
                'RECEIVED': ['2023-01-01', '2023-01-02'],
                'STOCK #': ['12345', '67890'],
                'MAKE': ['Honda', 'Toyota'],
                'DESC': ['Accord', 'Camry'],
                'INV AMT': [25000, 28000],
                'SLS COST': [23000, 26000],
                'STK IN NOTES': ['Test Notes', 'Test Notes 2'],
                'SVC RO DATE': ['2023-01-15', ''],  # Empty date
                'STAT-CODE': ['A', 'B'],
                'JRNL-PURCH-DATE-DR': ['2023-01-01', '2023-01-02'],
                'ACCTG-MK': ['MK1', 'MK2'],
                'RO-CLOSE-DATE': ['2023-01-16', None]  # Null date
            }
            mock_df = pd.DataFrame(df_data)
            mock_read_csv.return_value = mock_df

            # Configure CRUD mock
            mock_crud_instance = Mock()
            mock_crud_report.return_value = mock_crud_instance
            mock_crud_instance.find_report_row_by_vin.return_value = None

            # Act
            from reynols_report_processor.lambda_function import lambda_handler
            response = lambda_handler(event, context)

        # Assert
        self.test_assertion.assert_lambda_success_response(response)

        # Verify both VINs were processed despite date issues
        self.assertEqual(mock_crud_instance.insert_row.call_count, 2)

    @pytest.mark.unit
    @pytest.mark.data_validation
    def test_numeric_field_validation(self, mock_secrets_manager):
        """Test handling of numeric fields with various formats"""
        # Arrange
        mock_secrets_manager.add_test_secret('test-mongodb_uri', 'mongodb://localhost:27017/test_db')
        mock_secrets_manager.add_test_secret('test-sftp-credentials', {
            'hostname': 'test-sftp.example.com',
            'username': 'testuser',
            'password': 'testpass',
            'port': 22
        })

        event = {
            'body': json.dumps({
                'stage': 'used-cars'
            })
        }
        context = self.test_data_provider.get_lambda_context()

        with patch('reynols_report_processor.lambda_function.get_report_file') as mock_get_report, \
             patch('reynols_report_processor.lambda_function.download_file_from_s3') as mock_download, \
             patch('reynols_report_processor.lambda_function.pd.read_csv') as mock_read_csv, \
             patch('reynols_report_processor.lambda_function.CrudReynolsReport') as mock_crud_report, \
             patch('reynols_report_processor.lambda_function.CrudInvoices') as mock_crud_invoices, \
             patch('reynols_report_processor.lambda_function.Vin') as mock_vin, \
             patch('reynols_report_processor.lambda_function.mark_missing_pending_invoice_vins_as_delete') as mock_mark_missing:

            # Configure mocks
            mock_get_report.return_value = 'test-used-cars-report.csv'
            mock_download.return_value = True

            # Create mock DataFrame with various numeric formats
            df_data = {
                'VIN': ['1HGBH41JXMN123456', '2HGBH41JXMN789012'],
                'STORE': ['Store1', 'Store2'],
                'RECEIVED': ['2023-01-01', '2023-01-02'],
                'STOCK #': ['12345', '67890'],
                'MAKE': ['Honda', 'Toyota'],
                'INV-AMT': ['25,000.00', '28000'],  # Different numeric formats
                'SLS COST': ['$23,000.00', '26000.50'],  # With currency symbol
                'STK IN NOTES': ['Test Notes', 'Test Notes 2'],
                'ACCTG-MK': ['MK1', 'MK2'],
                'MEMO 2': ['Memo1', 'Memo2']
            }
            mock_df = pd.DataFrame(df_data)
            mock_read_csv.return_value = mock_df

            # Configure CRUD mocks
            mock_crud_report_instance = Mock()
            mock_crud_report.return_value = mock_crud_report_instance
            mock_crud_report_instance.find_report_row_by_vin.return_value = None

            mock_crud_invoices_instance = Mock()
            mock_crud_invoices.return_value = mock_crud_invoices_instance
            mock_crud_invoices_instance.find_invoice_used_car_by_vin_and_not_discarded.return_value = None

            # Configure VIN validation mock
            mock_vin_instance = Mock()
            mock_vin.return_value = mock_vin_instance
            mock_vin_instance.verify_checksum.return_value = True

            # Act
            from reynols_report_processor.lambda_function import lambda_handler
            response = lambda_handler(event, context)

        # Assert
        self.test_assertion.assert_lambda_success_response(response)

        # Verify both VINs were processed despite numeric format variations
        self.assertEqual(mock_crud_report_instance.insert_row_used_car.call_count, 2)
