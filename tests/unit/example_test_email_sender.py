"""
Example unit test for email_sender Lambda function.
Demonstrates how to use the testing framework for Lambda function testing.
"""

import pytest
import json
from unittest.mock import patch, Mock

from tests.base import BaseLambdaTest
from tests.utils import TestDataProvider, TestAssertions


class TestEmailSenderLambda(BaseLambdaTest):
    """Example test class for email_sender Lambda function"""
    
    def _setup_dependencies(self):
        """Set up test dependencies"""
        self.test_data_provider = TestDataProvider()
        self.test_assertion = TestAssertions(self)
    
    @pytest.mark.unit
    @pytest.mark.email
    def test_lambda_handler_success(self, mock_secrets_manager, mock_s3_service):
        """Test successful email sending"""
        # Arrange
        event = self.test_data_provider.get_lambda_event('email_sender')
        context = self.test_data_provider.get_lambda_context()
        
        # Mock Outlook service
        with patch('email_sender.lambda_function.Outlook') as mock_outlook:
            mock_outlook_instance = Mock()
            mock_outlook.return_value = mock_outlook_instance
            mock_outlook_instance.send_email_notification.return_value = None
            
            # Act
            # Note: You would import the actual lambda_function here
            # from email_sender.lambda_function import lambda_handler
            # response = lambda_handler(event, context)
            
            # For demonstration, we'll create a mock response
            response = {
                'statusCode': 200,
                'body': json.dumps({
                    'message': 'Email sent successfully',
                    'recipients': 1,
                    'attachments': 1
                })
            }
        
        # Assert
        self.test_assertion.assert_lambda_success_response(response, ['message', 'recipients'])
        
        # Verify Outlook service was called
        # mock_outlook_instance.send_email_notification.assert_called_once()
    
    @pytest.mark.unit
    @pytest.mark.email
    def test_lambda_handler_missing_subject(self):
        """Test error handling for missing subject"""
        # Arrange
        event = {
            'body': json.dumps({
                'content': 'Test email content'
                # Missing 'subject'
            })
        }
        context = self.test_data_provider.get_lambda_context()
        
        # Act
        # from email_sender.lambda_function import lambda_handler
        # response = lambda_handler(event, context)
        
        # For demonstration, we'll create a mock error response
        response = {
            'statusCode': 400,
            'body': json.dumps({
                'error': 'Missing required fields: subject and content are required'
            })
        }
        
        # Assert
        self.test_assertion.assert_lambda_error_response(response, 400, 'Missing required fields')
    
    @pytest.mark.unit
    @pytest.mark.email
    @pytest.mark.aws
    def test_lambda_handler_s3_attachment(self, mock_s3_service):
        """Test email sending with S3 attachment"""
        # Arrange
        mock_s3_service.add_test_object('test-bucket', 'documents/test.pdf', b'fake pdf content')
        
        event = {
            'body': json.dumps({
                'subject': 'Test Email with Attachment',
                'content': 'Test email content',
                'attachments': [
                    {
                        'filename': 'test.pdf',
                        's3_bucket': 'test-bucket',
                        's3_key': 'documents/test.pdf'
                    }
                ]
            })
        }
        context = self.test_data_provider.get_lambda_context()
        
        with patch('email_sender.lambda_function.Outlook') as mock_outlook, \
             patch('email_sender.lambda_function.boto3.client') as mock_boto3:
            
            # Configure S3 client mock
            mock_s3_client = Mock()
            mock_boto3.return_value = mock_s3_client
            mock_s3_client.get_object.return_value = {
                'Body': Mock(read=Mock(return_value=b'fake pdf content'))
            }
            
            # Configure Outlook mock
            mock_outlook_instance = Mock()
            mock_outlook.return_value = mock_outlook_instance
            
            # Act
            # from email_sender.lambda_function import lambda_handler
            # response = lambda_handler(event, context)
            
            # For demonstration
            response = {
                'statusCode': 200,
                'body': json.dumps({
                    'message': 'Email sent successfully',
                    'recipients': 1,
                    'attachments': 1
                })
            }
        
        # Assert
        self.test_assertion.assert_lambda_success_response(response)
        
        # Verify S3 object was retrieved
        # mock_s3_client.get_object.assert_called_once_with(
        #     Bucket='test-bucket',
        #     Key='documents/test.pdf'
        # )
    
    @pytest.mark.unit
    @pytest.mark.email
    def test_lambda_handler_no_recipients_configured(self):
        """Test error handling when no recipients are configured"""
        # Arrange
        event = self.test_data_provider.get_lambda_event('email_sender')
        context = self.test_data_provider.get_lambda_context()
        
        # Mock environment variable to be empty
        with patch.dict('os.environ', {'REPORTS_EMAIL': ''}):
            # Act
            # from email_sender.lambda_function import lambda_handler
            # response = lambda_handler(event, context)
            
            # For demonstration
            response = {
                'statusCode': 400,
                'body': json.dumps({
                    'error': 'No recipients configured in REPORTS_EMAIL environment variable'
                })
            }
        
        # Assert
        self.test_assertion.assert_lambda_error_response(response, 400, 'No recipients configured')
    
    @pytest.mark.unit
    @pytest.mark.email
    def test_lambda_handler_outlook_service_failure(self):
        """Test error handling when Outlook service fails"""
        # Arrange
        event = self.test_data_provider.get_lambda_event('email_sender')
        context = self.test_data_provider.get_lambda_context()
        
        with patch('email_sender.lambda_function.Outlook') as mock_outlook:
            # Configure Outlook to raise exception
            mock_outlook_instance = Mock()
            mock_outlook.return_value = mock_outlook_instance
            mock_outlook_instance.send_email_notification.side_effect = Exception('SMTP connection failed')
            
            # Act
            # from email_sender.lambda_function import lambda_handler
            # response = lambda_handler(event, context)
            
            # For demonstration
            response = {
                'statusCode': 500,
                'body': json.dumps({
                    'error': 'Failed to send email: SMTP connection failed'
                })
            }
        
        # Assert
        self.test_assertion.assert_lambda_error_response(response, 500, 'Failed to send email')
    
    @pytest.mark.unit
    @pytest.mark.email
    def test_create_lambda_event_helper(self):
        """Test the helper method for creating Lambda events"""
        # Arrange & Act
        event = self.create_lambda_event({
            'body': {'test': 'data'},
            'headers': {'custom-header': 'value'}
        })
        
        # Assert
        self.assertIn('version', event)
        self.assertIn('requestContext', event)
        self.assertIn('body', event)
        self.assertEqual(event['headers']['custom-header'], 'value')
        
        # Verify body is JSON string
        body_data = json.loads(event['body'])
        self.assertEqual(body_data['test'], 'data')
    
    @pytest.mark.unit
    def test_create_lambda_context_helper(self):
        """Test the helper method for creating Lambda context"""
        # Arrange & Act
        context = self.create_lambda_context(
            function_name='test-email-sender',
            memory_limit_in_mb=256,
            remaining_time_ms=25000
        )
        
        # Assert
        self.assertEqual(context.function_name, 'test-email-sender')
        self.assertEqual(context.memory_limit_in_mb, 256)
        self.assertEqual(context.get_remaining_time_in_millis(), 25000)
        self.assertIsNotNone(context.aws_request_id)


# Example of testing utility classes
class TestEmailSenderUtilities(BaseLambdaTest):
    """Test utility classes used by email_sender Lambda"""
    
    def _setup_dependencies(self):
        """Set up test dependencies"""
        pass
    
    @pytest.mark.unit
    @pytest.mark.aws
    def test_boto3_utils_get_secret(self, mock_secrets_manager):
        """Test boto3_utils get_secret function"""
        # Arrange
        mock_secrets_manager.add_test_secret('test-secret', {'key': 'value'})
        
        # Act
        # from email_sender.boto3_utils import get_secret
        # result = get_secret('test-secret')
        
        # For demonstration
        result = {'key': 'value'}
        
        # Assert
        self.assertIsInstance(result, dict)
        self.assertEqual(result['key'], 'value')
    
    @pytest.mark.unit
    @pytest.mark.aws
    def test_boto3_utils_get_aria_credentials(self, mock_secrets_manager):
        """Test boto3_utils get_aria_credentials function"""
        # Arrange
        mock_secrets_manager.add_test_secret('test-aria_cm_tokens', {
            'test': {
                'url': 'https://test-aria.example.com',
                'token': 'Bearer test-token'
            }
        })
        
        with patch.dict('os.environ', {'ENV': 'test', 'ARIA_ENV': 'test'}):
            # Act
            # from email_sender.boto3_utils import get_aria_credentials
            # result = get_aria_credentials()
            
            # For demonstration
            result = {
                'url': 'https://test-aria.example.com',
                'token': 'Bearer test-token'
            }
        
        # Assert
        self.assertIn('url', result)
        self.assertIn('token', result)
        self.assertTrue(result['token'].startswith('Bearer'))
