from Process_Utils.reynolds_app import <PERSON><PERSON><PERSON>, PopUpException, PostedVehicleException, UpdateReynoldsException, VinDataException, get_screenshot
from dotenv import load_dotenv
import traceback
from HEN_Utilities.aria_utils import ARIA
import time
from HEN_Utilities.utilities_handler import APIClient
from HEN_Utilities.cars_preprocessing import generate_input_dict_used_cars
import pyautogui
import base64
from datetime import datetime
import requests
import json

load_dotenv()

# API_ENDPOINT = "https://n26dxl5s43.execute-api.us-east-1.amazonaws.com/snd-hen/"
# API_TOKEN = "H9BPXBgmqEK95R2XqVjzJxNa27W3dk"
# SECRET = "snd-hen-reynolds_env_vars_snd"

API_ENDPOINT = "https://3aehxmmp2b.execute-api.us-east-1.amazonaws.com/prd-hen/"
API_TOKEN = "dwgMcKW9alRxy0HilyapDRlTIA8DCt"
SECRET = "prd-hen-reynolds_env_vars_prd"

def process_vehicles():    

    api_client = APIClient(
        api_endpoint=API_ENDPOINT,
        api_token=API_TOKEN
    )

    env_vars = api_client.get_secret(SECRET) 
    params = api_client.get_secret(env_vars['CREDENTIALS_SECRET_USER_2'])
    limit = env_vars.get('BATCH_SIZE_USED_CARS', 0)
    stage = "used-cars"

    uuid_completed_status = api_client.get_completed_status_uuid(env_vars["ARIA_INVOICE_APP_ID_USED_CARS"], env_vars["DB_NAME"])
    bre_response = {
        "aria_status":{
               "value": uuid_completed_status
            }
        }
    
    uuid_needs_status = api_client.get_needs_status_uuid(env_vars["ARIA_INVOICE_APP_ID_USED_CARS"], env_vars["DB_NAME"])

    aria = ARIA(env_vars["ARIA_BASE_URL"], env_vars["ARIA_TOKEN"], env_vars["ARIA_INVOICE_APP_ID_USED_CARS"])

    cars = []
    response = api_client.get_cars_to_stock_in_used_cars(env_vars["DB_NAME"], limit)
    if response is not None:
        cars = generate_input_dict_used_cars(response)

    reynolds_closed = False
    processed_vins = [car['VIN'] for car in cars]

    

    if cars and uuid_completed_status:

        reynolds = ReynoldsApp(api_client)
        reynolds_closed = True
         
        previous_store = ""
        actual_store = ""
        for index, new_car in enumerate(cars):
            
            try:
                print("VIN",new_car["vin"])
                aria.create_event(
                    item_id=new_car["aria_wi_id"],
                    title="The bot has started stocking.",
                    status=0
                )
                api_client.update_vin_in_mongo(env_vars["DB_NAME"], new_car["vin"],
                                                 {"flows.used-cars.updated_at": datetime.now().isoformat()}, {})
                actual_store = new_car["Brand"] 

                if reynolds_closed == True:
                    reynolds.open_reynolds()
                    if index == 0:
                        reynolds.update_reynolds()    
                    reynolds.login(params['user'],params['actual_password'])
                    reynolds_closed = False
                
                if previous_store != actual_store:
                    reynolds.go_to_accounting(new_car["Store"], is_new_car=False)
                    reynolds.select_new_or_used_car_to_stock_in(is_new_car=False)

                reynolds.insert_vehicle(new_car, is_new_car=False)
                
                aria.bre_reply(new_car["aria_wi_id"], bre_response)
                time.sleep(2)
                aria.create_event(
                    item_id=new_car["aria_wi_id"],
                    title="This invoice has been stocked in",
                    status=0
                )
                api_client.update_vin_status_in_mongo(new_car["VIN"], stage, 11, env_vars["DB_NAME"], finished=True)
                api_client.update_invoice_vin_status(new_car["VIN"], env_vars["DB_NAME"], "used-cars")     

            except PostedVehicleException as e:
                reynolds.logger.log("INFO", f"Pop up when doing the stock in of {new_car["VIN"]}: {traceback.format_exc()}")
                api_client.update_vin_status_in_mongo(new_car["VIN"], stage, 10, env_vars["DB_NAME"])
                aria.exception_aria_reply(new_car["aria_wi_id"], uuid_needs_status, "Error when doing stock in: Vehicle it's posted", e.screenshot)

            except PopUpException as e:
                reynolds.logger.log("INFO", f"Pop up when doing the stock in of {new_car["VIN"]}: {traceback.format_exc()}")
                api_client.update_vin_status_in_mongo(new_car["VIN"], stage, 9, env_vars["DB_NAME"])   
                aria.exception_aria_reply(new_car["aria_wi_id"], uuid_needs_status, "Error when doing stock in: Pop up", e.screenshot)

            except VinDataException as e:
                reynolds.logger.log("INFO", f"VIN data not completed {new_car["VIN"]}: {traceback.format_exc()}")
                api_client.update_vin_status_in_mongo(new_car["VIN"], stage, 10, env_vars["DB_NAME"])      
                aria.exception_aria_reply(new_car["aria_wi_id"], uuid_needs_status, "Error when doing stock in: Stock number data not complete", e.screenshot)

            except UpdateReynoldsException as e:
                reynolds.logger.log("INFO", f"Reynolds needs to get updated. Updating...")
                # Wait some time before closing reynolds to ensure its updated. 
                # We are waiting 5 mins
                time.sleep(300)

            except Exception:
                reynolds.logger.log("INFO", f"Error when doing the stock in of {new_car["VIN"]}: {traceback.format_exc()}")
                api_client.update_vin_status_in_mongo(new_car["VIN"], stage, 10, env_vars["DB_NAME"])
                screenshot = get_screenshot()
                aria.exception_aria_reply(new_car["aria_wi_id"], uuid_needs_status, "Error when doing stock in", screenshot)

            finally:
                reynolds.close_reynolds()
                reynolds_closed = True
                actual_store = ""
                previous_store = actual_store

        reynolds.close_reynolds()
        reynolds.invoke_rpa.close()

    return processed_vins


if __name__ == "__main__":
    processed_vins = process_vehicles()
    
    if processed_vins:
        headers = {
            'Authorization': f'Bearer {API_TOKEN}',
        }
        body = json.dumps({"vins": processed_vins, "type": "stock_in", "stage": "used-cars"})
        response = requests.post(API_ENDPOINT + "report_to_aria", json=body, headers=headers)
