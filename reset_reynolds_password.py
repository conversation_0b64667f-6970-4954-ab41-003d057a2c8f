from dotenv import load_dotenv
import traceback
from HEN_Utilities.utilities_handler import APIClient
from Process_Utils.reynolds_app import ReynoldsApp
import random
import string
import os

load_dotenv()

API_ENDPOINT = "https://l9axzrmhlk.execute-api.us-east-1.amazonaws.com/snd-hen/"
API_TOKEN = "H9BPXBgmqEK95R2XqVjzJxNa27W3dk"
SECRET = "snd-hen-reynolds_env_vars_snd"
SECRET_CREDENTIALS = "snd-hen-reynolds_credentials"

# API_ENDPOINT = "https://3aehxmmp2b.execute-api.us-east-1.amazonaws.com/prd-hen/"
# API_TOKEN = "dwgMcKW9alRxy0HilyapDRlTIA8DCt"
# SECRET = "prd-hen-reynolds_env_vars_prd"
#SECRET_CREDENTIALS = "prd-hen-reynolds_credentials"

def generate_password(length=12, use_digits=True, use_specials=False):
    char_pool = string.ascii_letters  # Upper and lowercase letters
    if use_digits:
        char_pool += string.digits
    if use_specials:
        char_pool += string.punctuation
    
    password = ''.join(random.choice(char_pool) for _ in range(length))
    return password


if __name__ == "__main__":

    api_client = APIClient(
        api_endpoint=API_ENDPOINT,
        api_token=API_TOKEN
    )

    env_vars = api_client.get_secret(SECRET) 
    params = api_client.get_secret(env_vars['CREDENTIALS_SECRET']) 

    reynolds = ReynoldsApp()

    new_password = generate_password()
    print("NEW PASSWORD", new_password)

    try:
        reynolds.open_reynolds()

        reynolds.reset_password(params['user'],params['actual_password'], new_password)
        
        api_client.update_secret(SECRET_CREDENTIALS, params['user'], params['actual_password'], new_password) 
        reynolds.logger.log("INFO", f"Successfully changed password")

    except Exception:
        print(f"Error when resetting the password: ", traceback.format_exc())
        reynolds.logger.log("ERROR", f"Error when resetting the password: {traceback.format_exc()}")
    finally:
        reynolds.close_reynolds()
