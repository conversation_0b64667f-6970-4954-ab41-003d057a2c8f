from Utilities.Common_Utilities.logger_utility import Logger
import traceback
import socket

class Environment():

    def __init__(self, log_path):
        self.l = Logger(log_path)

    # Get hostname
    #   OUTPUT:
    #       -hostname
    def get_hostname(self):
        try:
            hostname = socket.gethostname()
            return hostname
        except Exception as e:
            raise Exception ('Exception occurred on get_hostname method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))
    