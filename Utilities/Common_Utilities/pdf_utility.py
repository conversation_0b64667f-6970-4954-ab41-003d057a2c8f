import os
from Utilities.Common_Utilities.logger_utility import <PERSON><PERSON>
import traceback
from PyPDF2 import Pd<PERSON><PERSON><PERSON><PERSON>, PdfMerger, PdfWriter
from reportlab.lib.pagesizes import LETTER
from reportlab.pdfgen.canvas import Canvas
import traceback

class Pdf():

    def __init__(self, log_path):
        self.l = Logger(log_path)
        
    #Stamp
    #   INPUT:
    #       -content_pdf: path of the pdf with content
    #       -stamp_pdf: path of the pdf with stamp
    #       -pdf_result: path of the output pdf
    #       -page_indices   
    def stamp(self, content_pdf, stamp_pdf, pdf_result, page_indices):
        try:
            reader_stamp = PdfReader(stamp_pdf)

            writer = PdfWriter()

            reader = PdfReader(content_pdf)
            if page_indices == "ALL":
                page_indices = list(range(0, len(reader.pages)))
            for index in page_indices:
                content_page = reader.pages[index]
                mediabox = content_page.mediabox
                image_page = reader_stamp.pages[index]
                content_page.merge_page(image_page)
                content_page.mediabox = mediabox
                writer.add_page(content_page)
            with open(pdf_result, "wb") as fp:
                writer.write(fp)
        except Exception as e:  
            raise Exception ('Exception occurred on stamp method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))
        
    #Merge pdf
    #   INPUT:
    #       -pdf_list: list with pdf to be merged
    #       -destination_path: folder to save merged pdf
    #   OUTPUT:
    #       -merged_file_path: path of the merged pdf
    def merge_pdf(self, pdf_list, destination_path):
        try:
            merged_filename=""
            merger = PdfMerger()
            for item in pdf_list:
                merger.append(item)
                merged_filename += '{}_&_'.format(os.path.basename(item.replace('.pdf', '').replace(' ','')))
            merged_filename[:-3] if len(merged_filename) < 200 else merged_filename[:200] 
            merged_file_path = os.path.join(destination_path, merged_filename + '.pdf')
            merger.write(merged_file_path)
            merger.close()
            return merged_file_path
        except Exception as e:            
            raise Exception ('Exception occurred on merge_pdf method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))
        
    #Read pdf
    #   INPUT:
    #       -file_path: path of the file
    #   OUTPUT:
    #       -pdf_file
    def read_pdf(self, file_path):
        try:
            pdf_file = PdfReader(open(file_path, 'rb'))
            return pdf_file
        except Exception as e:            
            raise Exception ('Exception occurred on read_pdf method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))
        
    #Create pdf
    #   OUTPUT:
    #       -pdf_object
    def create_pdf(self):
        try:
            pdf_object = PdfWriter()
            return pdf_object
        except Exception as e:           
            raise Exception ('Exception occurred on crete_pdf method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))
        
    #Add pdf page
    #   INPUT:
    #       -pdf_object: pdf where new pages will be added
    #       -page: page to be added
    #   OUTPUT:
    #       -pdf_object: pdf object updated
    def add_pdf_page(self, pdf_object, page):
        try:
            pdf_object.add_page(page)
            return pdf_object
        except Exception as e:            
            raise Exception ('Exception occurred on crete_pdf method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))
        
    #Write pdf
    #   INPUT:
    #       -filepath
    #       -pdf_object
    def write_pdf(self, pdf_object, filepath):
        try:
            with open(filepath, 'wb') as outputStream:
                pdf_object.write(outputStream)
        except Exception as e:           
            raise Exception ('Exception occurred on write_pdf method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))
        

    #Create watermark pdf
    #   INPUT:
    #       -watermark_file: file name of the output pdf
    #       -data_to_be_written
    #       -font
    #       -font_size
    #       -color
    #       -width_rate
    #       -height_rate
    def create_watermark_multipage(self, watermark_file, data_to_be_written, font, font_size, color, width_rate, height_rate):
        try:
            canvas = Canvas(watermark_file, pagesize=LETTER)
            width, height = LETTER
            for data in data_to_be_written:
                if data == None:
                    data = ''
                # setting up the font and the font size
                canvas.setFont(font, float(font_size))
                # setting up the color of the font as red
                canvas.setFillColor(color)
                # writing this text on the PDF file
                canvas.drawString(float(width_rate)*width, float(height_rate)*height, data)
                canvas.showPage()
            canvas.save()
        except Exception as e:           
            raise Exception ('Exception occurred on write_pdf method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))  