from selenium import webdriver
from Utilities.Common_Utilities.logger_utility import Logger
import traceback

class Chrome():

    def __init__(self, log_path):
        self.l = Logger(log_path)

    # Open chrome
    #   INPUT:
    #       -arguments: parameters to launch chrome
    #   OUTPUT:
    #       -driver: driver to be used with all the interactions
    def open_chrome(self, arguments):
        try:
            options = webdriver.ChromeOptions()
            if arguments != "":
                arguments_list = arguments.split(',')
                for i in arguments_list:
                    options.add_argument(i)
            driver = webdriver.Chrome(options=options)
            return driver
        except Exception as e:           
            raise Exception ('Exception occurred on open_chrome method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))

    # Navigate
    #   INPUT:
    #       -driver: driver
    #       -url: link to navigate
    def navigate(self, driver, url):
        try:
            driver.get(url)
        except Exception as e:  
            raise Exception ('Exception occurred on navigate method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))

    # Close browser
    #   INPUT:
    #       -driver: driver
    def close(self, driver):
        try:
            driver.close()
        except Exception as e:           
            raise Exception ('Exception occurred on close method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))