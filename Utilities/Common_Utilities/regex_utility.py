from Utilities.Common_Utilities.logger_utility import Logger
import re
import traceback

class Regex():

    def __init__(self,log_path):
        self.l = Logger(log_path)

    # Extract regex group
    #   INPUT:
    #       -str: string to apply regex
    #       -regex: regex pattern
    #       -group_number: group to extract
    def extract_regex_group(self, str, regex, group_number = 0): 
        try:
            result = re.search(regex,str)
            if group_number != '':
                try:
                    result = result.group(group_number)
                except:
                    result = ""
            return result
        except Exception as e:            
            raise Exception('Exception occurred on extract_regex_group method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))
        
    # Compile a regular expression pattern into a regular expression object
    #   INPUT:
    #       -regex: regex pattern
    #   OUTPUT:
    #       -object
    def compile_expression_into_object(self, regex): 
        try:
            object = re.compile(regex)
            return object
        except Exception as e:          
            raise Exception('Exception occurred on compile method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))
        
    # Return all non-overlapping matches of pattern in string, as a list of strings or tuples
    #   INPUT:
    #       -str: string to apply regex
    #       -regex: regex pattern
    def find_all_occurrences(self, regex, str): 
        try:
            occurrences = re.findall(regex, str)
            return occurrences
        except Exception as e:           
            raise Exception('Exception occurred on find_all_occurrences method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))
        
    # Check if string follows a regex pattern
    #   INPUT:
    #       -str: string to apply regex
    #       -regex: regex pattern
    #   OUTUT:
    #       -match
    def match(self, regex, str): 
        try:
            match = re.search(regex, str)
            return match
        except Exception as e:            
            raise Exception('Exception occurred on match method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))
        
    # Replace regex matches with a string
    #   INPUT:
    #       -replace_str: string to replace
    #       -str: string to be replaced
    #       -regex: regex pattern
    #   OUTUT:
    #       -result
    def replace(self, regex, str, replace_str): 
        try:
            result = re.sub(regex, str, replace_str)
            return result
        except Exception as e:           
            raise Exception('Exception occurred on replace method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))
        