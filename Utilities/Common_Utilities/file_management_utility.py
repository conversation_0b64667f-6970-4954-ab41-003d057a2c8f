import os
from Utilities.Common_Utilities.logger_utility import Logger
from os import listdir
from os.path import isfile, join
import shutil
import traceback

class File_Management():

    def __init__(self,log_path):
        self.l = Logger(log_path)

    # Remove file
    #   INPUT:
    #       -file: filename
    def remove_file(self,file):
        try:
            os.remove(file)
        except Exception as e:
            raise Exception('Exception occurred on remove_file method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))
        
    # File exists
    #   INPUT:
    #       -file: filename
    #   OUTPUT:
    #       -found: flag indicating if file exists or not
    def file_exists(self,file):
        try:
            found = os.path.isfile(file)
            return found
        except Exception as e:           
            raise Exception('Exception occurred on file_exists method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))
        
    # Folder exists
    #   INPUT:
    #       -folder_name
    #   OUTPUT:
    #       -found: flag indicating if folder exists or not
    def folder_exists(self, folder_name):
        try:
            found = os.path.isdir(folder_name)
            return found
        except Exception as e:         
            raise Exception('Exception occurred on folder_exists method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))
        
    # Create directory
    #   INPUT:
    #       -folder_name
    def create_directory(self, folder_name):
        try:
            os.makedirs(folder_name)
        except Exception as e:       
            raise Exception('Exception occurred on create_directory method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))
        
    # Get files from folder
    #   INPUT:
    #       -file_path: file path of folder
    #   OUTPUT:
    #       -files: list of files from folder
    def get_files_from_folder(self, file_path):
        try:
            files = [os.path.join(file_path, f) for f in listdir(file_path) if isfile(join(file_path, f))]
            return files
        except Exception as e:           
            raise Exception('Exception occurred on get_files_from_folder method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))
        
    # Get folders from directory
    #   INPUT:
    #       -folder_path
    #   OUTPUT:
    #       -folders: list of folders from directory
    def get_folders_from_directory(self, folder_path):
        try:
            folders = os.listdir(folder_path)
            return folders
        except Exception as e:            
            raise Exception('Exception occurred on get_folders_from_directory method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))
    
    # Move file
    #   INPUT:
    #       -file: filename
    #       -destination_path
    def move_file(self, file, destination_path):
        try:
            shutil.move(file, destination_path)
        except Exception as e:            
            raise Exception('Exception occurred on move_file method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))
        
    # Create file
    #   INPUT:
    #       -file_path: filename
    def create_file(self, file_path):
        try:    
            f = open(file_path, "x")
        except Exception as e:
            raise Exception('Exception occurred on create_file method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))
        
    # Copy file
    #   INPUT:
    #       -file: filename
    #       -destination_path
    def copy_file(self, file, destination_path):
        try:
            shutil.copy(file, destination_path)
        except Exception as e:           
            raise Exception('Exception occurred on copy_file method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))
        