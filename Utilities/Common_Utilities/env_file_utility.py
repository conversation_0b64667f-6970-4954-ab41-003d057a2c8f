from Utilities.Common_Utilities.logger_utility import Logger
from dotenv import dotenv_values
import traceback

class Env_File():

    def __init__(self, log_path, file_path):
        self.l = Logger(log_path)
        self.dotenv_path = file_path

    # Get values from .env
    #   OUTPUT:
    #       -env_values: dictionaty with all the values from .env
    def get_values(self):
        try:  
            env_values = dotenv_values(self.dotenv_path) #Extract env variables
            return env_values
        except Exception as e:
            raise Exception ('Exception occurred on get_values method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))