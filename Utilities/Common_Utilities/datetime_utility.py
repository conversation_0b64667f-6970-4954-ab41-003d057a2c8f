from Utilities.Common_Utilities.logger_utility import Logger
import datetime
import traceback

class Datetime():

    def __init__(self, log_path):        
        self.l = Logger(log_path)

    # Get current day
    #   OUTPUT
    #       -today: date as a datetime object
    def get_today(self):
        try:
            today = datetime.date.today()
            return today
        except Exception as e:
            raise Exception ('Exception occurred on get_today method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))
        
    # Get current datetime
    #   OUTPUT
    #       -now: datetime
    def get_now(self):
        try:
            now = datetime.datetime.now()
            return now
        except Exception as e:
            raise Exception ('Exception occurred on get_now method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))
        
    # Get week day
    #   OUTPUT
    #       -week_day: datetime
    def get_week_day(self):
        try:
            today = self.get_today()
            week_day = today.weekday()
            return week_day
        except Exception as e:
            raise Exception ('Exception occurred on get_week_day method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))
        
    # Format date
    #   INPUT
    #       -date
    #       -format
    #   OUTPUT
    #       -formatted_date
    def format_date(self, date, format):
        try:
            formatted_date = date.strftime(format)
            return formatted_date
        except Exception as e:
            raise Exception ('Exception occurred on format_date method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))
        
    # Convert string into datetime
    #   INPUT
    #       -str
    #       -format
    #   OUTPUT
    #       -date
    def convert_string_to_datetime(self, str, format):
        try:
            date = datetime.datetime.strptime(str, format)
            return date
        except Exception as e:
            raise Exception ('Exception occurred on convert_string_to_datetime method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))
        
    # Add timespan to date
    #   INPUT
    #       -date
    #       -seconds
    #       -microseconds
    #       -miliseconds
    #       -minutes
    #       -hours
    #       -weeks
    #   OUTPUT
    #       -date
    def add_timespan_to_date(self, date, days=0, seconds=0, microseconds=0, milliseconds=0, minutes=0, hours=0, weeks=0):
        try:
            date = date + datetime.timedelta(days=days, seconds=seconds, microseconds=microseconds, milliseconds=milliseconds, minutes=minutes, hours=hours, weeks=weeks)
            return date
        except Exception as e:
            raise Exception ('Exception occurred on add_timespan_to_date method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))