from Utilities.Common_Utilities.logger_utility import Logger
from Utilities.Common_Utilities.mongo_utility import Mongo
from Utilities.Common_Utilities.json_utility import Json
import traceback

class Logger_mongo():

    def __init__(self, log_path, token, url, mongo_db_name):
        self.l = Logger(log_path)
        self.json = Json(log_path)
        self.mongo = Mongo(log_path, token, url)
        
        self.mongo_db_name = mongo_db_name

    # Upload_log_on_mongo
    #   INPUT:
    #       -item_key: key of mongodb where the log will be uploaded 
    #       -item_log: log to be updated
    #       -mongo_collection_logs_name
    #       -attempt: current attempt of item
    #       -process_name
    def upload_log_on_mongo(self, item_key, item_log, mongo_collection_logs_name, attempt, process_name):
        try:
            #CHECK IF ITEM IS ALREADY ON DB
            mongo_json = {
                    "action": "get_many",
                    "database_name": self.mongo_db_name, 
                    "collection_name": mongo_collection_logs_name,
                    "data": {"item_key": item_key},
                }
            item = self.mongo.execute_query(mongo_json)
            
            if item != '[]':
                #IF ITEM ALREADY EXISTS, APPEND LOG
                item = self.json.load_json(item)[0]
                mongo_json = {
                    "action": "update_one",
                    "database_name": self.mongo_db_name, 
                    "collection_name": mongo_collection_logs_name,
                    "data": {"$set":{"item_data.log": item['item_data']['log'] + "\n{}\nAttempt number {}\n".format(process_name, attempt) + item_log}},
                    "filter": {"item_key": item_key},
                }
            else:
                #IF ITEM DOES NOT EXIST, CREATE ITEM
                mongo_json = {
                    "action": "insert_one",
                    "database_name": self.mongo_db_name, 
                    "collection_name": mongo_collection_logs_name,
                    "key": item_key,
                    "data": {"log": "{}\nAttempt number {}\n".format(process_name, attempt) + item_log}
                }
            self.mongo.execute_query(mongo_json)
        except Exception as e:
            raise Exception ('Exception occurred on upload_log_on_mongo method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))