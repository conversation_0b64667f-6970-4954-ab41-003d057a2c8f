import base64
import json
import boto3
import os
from typing import List, Dict, Optional

"""
External email function interface - integrates with the email Lambda function
"""

def send_email_notification(subject: str, html_content: str, attachments: Optional[List[Dict]] = None) -> bool:
    """
    Send email notification using the email Lambda function
    
    Args:
        subject (str): Email subject line
        html_content (str): HTML email content
        attachments (list, optional): List of attachment dictionaries:
            - {'filename': 'file.pdf', 'content': 'base64_encoded_content'}
            - {'filename': 'file.pdf', 'path': '/tmp/file.pdf'}
            - {'filename': 'file.pdf', 's3_bucket': 'my-bucket', 's3_key': 'path/to/file.pdf'}
    
    Returns:
        bool: True if email sent successfully, False otherwise
    """
    
    try:
        # print(f"=== EMAIL NOTIFICATION ===")
        # print(f"Subject: {subject}")
        # print(f"Content Length: {html_content} characters")
        # print(f"Attachments: {len(attachments) if attachments else 0}")
        
        # if attachments:
        #     for attachment in attachments:
        #         print(f"  - {attachment.get('filename', 'unknown')} at {attachment.get('path', 'unknown')}")

        # Get Lambda function name from environment variable
        # lambda_function_name = os.environ.get('EMAIL_LAMBDA_FUNCTION_NAME', 'universal-email-sender')
        # lambda_function_name = "snd-hen-email_sender"       
        
        lambda_function_name = f"{os.getenv('ENV')}-email_sender"
        # Prepare the event payload
        event_payload = {
            'subject': subject,
            'content': html_content
        }
        
        # Process attachments - convert file paths to base64
        processed_attachments = []
        if attachments:
            for attachment in attachments:
                if 'path' in attachment:
                    try:
                        # Read file and encode to base64
                        with open(attachment['path'], 'rb') as file:
                            file_content = file.read()
                            base64_content = base64.b64encode(file_content).decode('utf-8')
                            processed_attachments.append({
                                'filename': attachment['filename'],
                                'content': base64_content
                            })
                    except Exception as e:
                        print(f"Failed to read file {attachment['path']}: {e}")
                        continue

        
        event_payload = {
            'subject': subject,
            'content': html_content,
            'attachments': processed_attachments if processed_attachments else None
        }

        # Initialize Lambda client
        lambda_client = boto3.client('lambda')
        
        # Invoke the Lambda function
        response = lambda_client.invoke(
            FunctionName=lambda_function_name,
            InvocationType='RequestResponse',  # Synchronous
            Payload=json.dumps(event_payload)
        )
        
        # Parse the response
        response_payload = json.loads(response['Payload'].read())
        
        if response.get('StatusCode') == 200 and response_payload.get('statusCode') == 200:
            print(f"Email sent successfully via Lambda: {subject}")
            return True
        else:
            print(f"Failed to send email via Lambda: {response_payload}")
            return False
            
    except Exception as e:
        print(f"Failed to send email via Lambda: {e}")
        return False
