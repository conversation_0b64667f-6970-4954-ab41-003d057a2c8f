functions:
  lambda_authorizer:
    name: ${self:custom.servicePrefix}-lambda_authorizer
    handler: src/lambda_authorizer/lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 128
    timeout: 180
    ephemeralStorageSize: 512
    role: LambdaAuthorizerRole
   
    package:
      individually: true
      patterns:
        - "!**/*"
        - src/lambda_authorizer/**
        - src/common/**
  
  bre:
    name: ${self:custom.servicePrefix}-bre
    handler: src/bre/lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 2048
    timeout: 900
    ephemeralStorageSize: 512
    role: BreRole
    layers:
      - { Ref: PyMongoAWSLayerLambdaLayer }
      - { Ref: Boto3LayerLambdaLayer }
      - { Ref: RequestsLayerLambdaLayer }
    package:
      individually: true
      patterns:
        - "!**/*"
        - src/bre/**
        - src/common/aria_helper/**
        - src/common/validation_helper/**

  bre_handler:
    name: ${self:custom.servicePrefix}-bre_handler
    handler: src/bre_handler/lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 180
    ephemeralStorageSize: 512
    role: BreHandlerRole
    layers:
      - { Ref: PyMongoAWSLayerLambdaLayer }
      - { Ref: Boto3LayerLambdaLayer }
      - { Ref: RequestsLayerLambdaLayer }
    package:
      individually: true
      patterns:
        - "!**/*"
        - src/bre_handler/**
        - src/common/aria_helper/**
    events:
      - httpApi:
          path: /${self:custom.servicePrefix}/bre_handler
          method: post
          authorizer: lambda_authorizer

  llm_extractor:
    name: ${self:custom.servicePrefix}-llm_extractor
    handler: src/llm_extractor/lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    role: LlmExtractorRole
    layers:
      - { Ref: PyMongoAWSLayerLambdaLayer }
      - { Ref: Boto3LayerLambdaLayer }
      - { Ref: OpenAILayerLambdaLayer }
      - { Ref: RequestsLayerLambdaLayer }
      - { Ref: PydanticLayerLambdaLayer }
    package:
      individually: true
      patterns:
        - "!**/*"
        - src/llm_extractor/**
        - src/common/**

  bre_validation:
    name: ${self:custom.servicePrefix}-bre_validation
    handler: src/bre_validation/lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    role: BreValidationRole
    layers:
      - { Ref: PyMongoAWSLayerLambdaLayer }
      - { Ref: Boto3LayerLambdaLayer }
      - { Ref: RequestsLayerLambdaLayer }
    package:
      individually: true
      patterns:
        - "!**/*"
        - src/bre_validation/**
        - src/common/aria_helper/**
        - src/common/validation_helper/**
