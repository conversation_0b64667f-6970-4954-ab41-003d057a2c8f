from Process_Utils.reynolds_app import <PERSON><PERSON><PERSON>, ModelNotSelected, get_screenshot
from dotenv import load_dotenv
import traceback
from HEN_Utilities.aria_utils import ARIA
import time
from HEN_Utilities.utilities_handler import APIClient
from HEN_Utilities.titles_preprocessing import generate_input_dict_titles
import pyautogui
import base64
from datetime import datetime
import requests
import json

load_dotenv()

# API_ENDPOINT = "https://n26dxl5s43.execute-api.us-east-1.amazonaws.com/snd-hen/"
# API_TOKEN = "H9BPXBgmqEK95R2XqVjzJxNa27W3dk"
# SECRET = "snd-hen-reynolds_env_vars_snd"

API_ENDPOINT = "https://3aehxmmp2b.execute-api.us-east-1.amazonaws.com/prd-hen/"
API_TOKEN = "dwgMcKW9alRxy0HilyapDRlTIA8DCt"
SECRET = "prd-hen-reynolds_env_vars_prd"

def process_titles():    

    api_client = APIClient(
        api_endpoint=API_ENDPOINT,
        api_token=API_TOKEN
    )

    env_vars = api_client.get_secret(SECRET) 
    params = api_client.get_secret(env_vars['CREDENTIALS_SECRET_USER_4'])
    limit = env_vars.get('BATCH_SIZE_TITLE', 0)


    uuid_completed_status = api_client.get_completed_status_uuid(env_vars["ARIA_INVOICE_APP_ID_TITLES"], env_vars["DB_NAME"])
    uuid_needs_status = api_client.get_needs_status_uuid(env_vars["ARIA_INVOICE_APP_ID_TITLES"], env_vars["DB_NAME"])
    uuid_not_stock_in_status = api_client.get_not_in_stock_status_uuid(env_vars["ARIA_INVOICE_APP_ID_TITLES"], env_vars["DB_NAME"])

    bre_response = {
        "aria_status":{
               "value": uuid_completed_status
            }
        }
    

    aria = ARIA(env_vars["ARIA_BASE_URL"], env_vars["ARIA_TOKEN"], env_vars["ARIA_INVOICE_APP_ID_TITLES"])

    titles_to_process = []
    response = api_client.get_titles_to_stock_in_post_inventory(env_vars["DB_NAME"], limit)
    if response is not None:
        titles_to_process = generate_input_dict_titles(response)

    reynolds_closed = False

    if titles_to_process and uuid_completed_status:

        reynolds = ReynoldsApp(api_client)
        reynolds_closed = True
         
        actual_store = ""
        for idx, title in enumerate(titles_to_process):

            title_date = title["title_date"]
            is_mso = title["is_mso"]
            make = title["make"]
            store = title["store"]
            vin = title["vin"]
            reynolds_model = title.get("reynolds_model", None)


            try:
                aria.create_event(
                    item_id=title["aria_wi_id"],
                    title="The bot has started stocking.",
                    status=0
                )
                # This function internally always update item_modified with current time
                api_client.update_title_in_mongo(env_vars["DB_NAME"], title["aria_wi_id"],{}, {})

                if reynolds_closed == True:
                    reynolds.open_reynolds()
                    if idx == 0:
                        reynolds.update_reynolds()    
                    reynolds.login(params['user'],params['actual_password'])
                    reynolds_closed = False
                
                brand = reynolds.get_map_store_brand(store)
                branch = reynolds.get_branch_by_store(store)
                reynolds.go_to_vms(brand,branch=branch)
                found = reynolds.insert_title_date(vin, title_date, reynolds_model, is_mso)

                if found:
                    api_client.update_title_in_mongo(env_vars["DB_NAME"], title["aria_wi_id"], {"status": "Completed"}, {"status_history": "Completed"})
                    aria.create_event(
                        item_id=title["aria_wi_id"],
                        title="This title has been stocked in",
                        status=0
                    )
                    aria.bre_reply(title["aria_wi_id"], bre_response)
                else:
                    reynolds.logger.log("INFO",
                                        f"VIN not found in DMS {vin}")
                    api_client.update_title_in_mongo(env_vars["DB_NAME"], title["aria_wi_id"],
                                                    {"status": "Not in Stock"},
                                                    {"status_history": "Not in Stock"})
                    screenshot = get_screenshot()
                    aria.exception_aria_reply(title["aria_wi_id"], uuid_not_stock_in_status, "VIN not found in DMS",
                                            screenshot)


            except ModelNotSelected as e:
                reynolds.logger.log("ERROR", f"Model not selected for title {vin}: {e}")
                api_client.update_title_in_mongo(env_vars["DB_NAME"], title["aria_wi_id"], {"status": "Needs Attention"}, {"status_history": "Needs Attention"})
                aria.exception_aria_reply(title["aria_wi_id"], uuid_needs_status, "Error when doing stock in: Model needs to be selected", e.screenshot)

                reynolds.close_reynolds()
                reynolds_closed = True

            except Exception:   
                reynolds.logger.log("INFO", f"Error when doing the stock in of title for {vin}: {traceback.format_exc()}")
                api_client.update_title_in_mongo(env_vars["DB_NAME"], title["aria_wi_id"], {"status": "Needs Attention"}, {"status_history": "Needs Attention"})
                screenshot = get_screenshot()
                aria.exception_aria_reply(title["aria_wi_id"], uuid_needs_status, "Error when doing stock in", screenshot)

                reynolds.close_reynolds()
                reynolds_closed = True
        if not reynolds_closed:
            reynolds.close_reynolds()
        reynolds.invoke_rpa.close()



if __name__ == "__main__":
    process_titles()

